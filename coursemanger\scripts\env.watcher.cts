/*
 * @Author: 冉志诚
 * @Date: 2024-07-16 16:54:25
 * @LastEditTime: 2024-12-18 14:53:03
 * @FilePath: \coursemanger\scripts\env.watcher.cts
 * @Description:
 */
import fsPromise from 'node:fs/promises';
import path from 'node:path';

const watchFilePath = path.resolve(
  __dirname,
  '../config/env.proxy.ts',
);

async function startEnvWatch() {
  try {
    console.log('[env watcher] proxy代理监听启动');
    const watcher = fsPromise.watch(
      path.resolve(__dirname, '../proxy.env'),
      {},
    );
    // 监听当前进程是否断开
    process.on('SIGINT', () => {
      console.log('env watcher 退出');
      // 删除文件
      // fs.unlinkSync(cachePath);
    });
    const handle = debounce(async () => {
      // 读取当前文件
      try {
        console.log(
          `[env watcher] ${new Date().toLocaleString()} 代理文件改变,重启代理服务器中!`,
        );
        const content = await fsPromise.readFile(
          watchFilePath,
          {
            encoding: 'utf-8',
          },
        );
        if (content.length > 0) {
          // 写入
          await fsPromise.writeFile(watchFilePath, content);
        }
      } catch (error) {
        console.log('🚀 ~ error:', error);
      }
    }, 1000);

    for await (const event of watcher) {
      handle(event);
    }
  } catch (error) {}
}
startEnvWatch();

function debounce(
  func: Function,
  wait = 100,
  immediate?: Function,
) {
  let timer: any;
  return (...args: any) => {
    if (timer) clearTimeout(timer);
    if (immediate) {
      const callNow = !timer;
      timer = setTimeout(() => {
        timer = null;
      }, wait);
      if (callNow) func(...args);
    } else {
      timer = setTimeout(() => {
        func(...args);
      }, wait);
    }
  };
}
