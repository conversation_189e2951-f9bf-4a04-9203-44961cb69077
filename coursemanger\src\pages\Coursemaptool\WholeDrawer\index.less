.whole_drawer {
    width: 100%;
    height: 100%;
    overflow: auto;
    display: flex;
    padding: 20px;
    >div {
        height: 100%;
        padding: 20px;
        box-sizing: border-box;
        background: #FFFFFF;
        box-shadow: 0px 0px 20px 0px rgba(232, 232, 232, 0.7);
        border-radius: 10px;
        border: 1px solid #EFEFEF;
    }

    .mapv3_x6_view{
        background-color: #000;
    }

    .content_box {
        margin-bottom: 20px;
        .video-wrap{
            position: relative;
            min-height: 16.13vw !important;
            background-color: #000;
            
            #video-dom{
                position: absolute;
                height: 100%;
            }
            iframe {
                min-height: 350px;
            }
            // .video-container{
            //     // padding-top: 56.25% !important;
            // }
        }
        .title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;

            .text {
                line-height: 30px;
                flex: 1;
                width: 0;
                font-size: 16px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 600;
                color: #000;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                user-select:text;
            }

            .number_view {
                font-weight: 400;
                font-size: 14px;
                color: #808080;
            }
        }
    }
    .teaching_syllabus{
        width: 320px;
        height: 100%;
        overflow: auto;
    }
    .left_page {
        width: 320px;
        display: flex;
        flex-direction: column;

        .content_box {
            .number_view{
                width: 100%;
                height: auto;
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 10px;

                .left_view{
                    display: flex;
                    align-items: center;
                }
            }
            .target_list {
                display: flex;
                flex-direction: column;
                gap: 5px;
                margin-bottom: 10px;
                .target_item {
                    .ant-typography {
                        margin-bottom: 0;
                    }
                    display: flex;
                    .text {
                        padding: 0 10px;
                        height: 22px;
                        text-align: center;
                        line-height: 22px;
                        border-radius: 13px;
                        margin-right: 10px;
                        font-size: 14px;
                        color: var(--primary-color);
                        background: var(--second-color);
                    }

                    .desc {
                        flex: 1;
                        width: 0;
                    }
                }
            }
        }

        .detail {
            background: #F5F6F8;
            border-radius: 4px;
            padding: 10px;
            font-weight: 400;
            font-size: 14px;
            color: #58595C;

            .ant-typography {
                text-align: justify;
                max-height: 200px;
                overflow: auto;
            }

            .ant-typography-ellipsis {
                p:first-child, p:last-child{
                    display: inline;
                }
                height: auto;
            }
        }

    }

    .middle_page {
        flex: 1;
        min-width: 300px;
        margin: 0 10px;
        overflow: auto;

        .resource_list {
            position: absolute;
            top: 0;
            right: 0;
            width: 228px;
            background: rgba(0, 0, 0, .5);
            border-radius: 0px 6px 6px 0px;
            z-index: 100;
            // display: none;

            .ant-list {
                width: 100%;
                height: 100%;
                overflow: auto;
                border: none;

                .ant-list-item {
                    border-bottom: none;
                    padding: 15px;
                }

                .content_item {
                    display: flex;
                    width: 100%;

                    .content_name {
                        flex: 1 0 auto;
                        width: 1px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                }
            }
        }
        .list_item{
            margin-bottom: 10px;
        }
        .dots {
            font-size: 14px;
            margin-bottom: 10px;

            .dot {
                font-weight: 400;
                color: #808080;
            }

            .value {
                background: rgba(59, 156, 255, .1);
                border-radius: 4px;
                font-weight: 400;
                color: #3B9CFF;
                padding: 2px 6px;
            }
        }

        .desc {
            background: #F5F6F8;
            border-radius: 4px;
            padding: 10px;
        }

        .bottom_view {
            width: 240px;
            height: 10px;
        }
    }

    .right_page {
        overflow: auto;
        width: 400px;


        .target_list {
            display: flex;
            flex-direction: column;
            gap: 5px;
            margin-bottom: 10px;
            .target_item {
                .ant-typography {
                    margin-bottom: 0;
                }
                display: flex;
                .text {
                    height: 22px;
                    padding: 0 10px;
                    text-align: center;
                    line-height: 22px;
                    border-radius: 13px;
                    margin-right: 10px;
                    font-size: 14px;
                    color: var(--primary-color);
                    background: var(--second-color);
                }

                .desc {
                    flex: 1;
                    width: 0;
                }

                .percent{
                    width: 50px;
                    display: flex;
                    justify-content: center;
                }
            }
        }
        .divider {
            width: 100%;
            height: 1px;
            margin-bottom: 20px;
        }

        .content_box {
            .list {
                display: flex;
                align-items: center;
                padding: 12px 0;
                border-bottom: 1px dashed #EDEDED;
                .dot {
                    width: 8px;
                    height: 8px;
                    background: #C0C0C0;
                    border-radius: 50%;
                    margin-right: 6px;
                }
            }
        }
    }

    .drawer_view {
        width: 100%;
        height: 100%;
        min-height: 100%;
        padding-right: 0;
        padding-bottom: 0;

        .content_item {
            width: 85%;
            overflow: hidden;
            display: flex;
            align-items: center;


            // 超出一行显示省略号
            .content_name {
                width: 85%;
                overflow: hidden;
                text-overflow: ellipsis; //文本溢出显示省略号
                white-space: nowrap; //文本不会换行
            }
        }

        .topic_container {
            .flex-sb {
                flex-wrap: wrap;
            }

            .info_msg {
                display: flex;
                flex-wrap: wrap;

                .ant-space-item-split {
                    display: none;
                }

                .ant-space {
                    width: 100%;
                }
            }
        }

        .rate_box {
            width: 100%;
            height: 85px;
            display: flex;
            align-items: center;

            .left_view {
                width: 50%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;

                .name_box {
                    width: 70%;
                    height: 100%;
                    display: flex;
                    align-items: center;

                    .redio_box {
                        width: 10px;
                        height: 10px;
                        background: #549CFF;
                        opacity: 0.5;
                        border-radius: 50%;
                        margin-right: 10px;
                    }

                    .rate_title {
                        font-size: 15px;
                        font-family: PingFangSC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #000;
                    }

                }

                .rate_value {
                    font-size: 20px;
                    font-family: Arial-BoldMT, Arial;
                    font-weight: 550;
                    color: #549CFF;

                }
            }
        }

        .options_view {
            width: 100%;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
        }


        .detail_view {
            width: 100%;
            height: calc(100vh - 400px);
            overflow: auto;
            border: 1px solid #eee;
            margin-top: 10px;

            .courseqa_container {
                padding: 0 15px 0 15px !important;
            }
        }

        .title {
            margin-top: 10px;
            margin-bottom: 10px;
            width: 100%;
            line-height: 30px;
            display: flex;
            align-items: flex-start;
            justify-content: space-between;

            .span1 {
                font-size: 16px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 600;
                color: #000000;
                cursor: pointer;
                max-width: 65%;
                // height: 100%;
                // overflow: hidden;
                // text-overflow:ellipsis;
                // white-space: nowrap;
            }
        }

        .detail {

            width: 100%;
            height: auto;

            p {
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #000000;
                line-height: 21px;
            }
        }

        .link_overflow {
            // 超出一行显示省略号
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis; //文本溢出显示省略号
            white-space: nowrap; //文本不会换行
        }

        .video_view {
            width: 100%;
            height: 50px;
            display: flex;
            align-items: center;

            .redio_view {
                width: 7px;
                height: 7px;
                border-radius: 50%;
                background-color: var(--primary-color);
                margin-top: 15px;
                margin-bottom: 15px;
            }

            .title_span {
                font-size: 16px;
                margin-left: 6px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #000000;
                line-height: 20px;
            }
        }


        .excel_view {
            width: 100%;
            height: 40px;
            border: 1px solid #eeeeee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 10px;
            cursor: pointer;

            .left_view {
                width: 80%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: flex-start;

                span {
                    overflow: hidden;
                    text-overflow: ellipsis; //文本溢出显示省略号
                    white-space: nowrap; //文本不会换行
                }
            }


        }

        .ant-collapse>.ant-collapse-item {
            background-color: #fff;
        }

        .divider_dashed {
            margin-top: 20px;
            width: 100%;
            height: 0px;
            border-bottom: 1px dashed #E6E6E6;
        }

        .topic_box {
            position: relative;
            width: 100%;
            height: auto;

            &:hover {
                .remove_topic {
                    display: block;
                }
            }

            .remove_topic {
                position: absolute;
                right: 10px;
                top: 20px;
                cursor: pointer;
                z-index: 2;
                display: none;
            }

        }

        .number_view {
            width: 100%;
            height: auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;

            .left_view {
                display: flex;
                align-items: center;
            }
        }

        .other_view {
            width: 100%;
            height: auto;
            display: flex;
            align-items: center;
            flex-wrap: wrap;

            .redio_view {
                width: 7px;
                height: 7px;
                border-radius: 50%;
                background-color: var(--primary-color);
                margin-top: 20px;
                margin-bottom: 15px;
            }

            .other_title {
                font-size: 16px;
                font-family: Helvetica;
                color: #000000;
                line-height: 20px;
                margin-left: 6px;
                margin-top: 13.5px;
                margin-bottom: 8.5px;
                margin-right: 10px;
            }


            .other_item {
                padding-left: 10px;
                padding-right: 10px;
                border: 1px solid var(--primary-color);
                border-radius: 16px;
                margin-left: 10px;
                height: 28px;
                display: flex;
                align-items: center;
                margin-top: 9.5px;
                margin-bottom: 4.5px;
                cursor: pointer;

                .span_name {
                    font-size: 14px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: var(--primary-color);
                    line-height: 20px;
                    max-width: 160px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    -o-text-overflow: ellipsis;
                }

                .icon_delete {
                    color: var(--primary-color);
                    margin-left: 5px;
                }
            }

            .file_item {
                height: 35px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #FDF4F3;
                border-radius: 4px;
                padding-left: 10px;
                padding-right: 10px;
                cursor: pointer;
                margin-right: 15px;

                .span1 {
                    margin-left: 10px;
                    font-size: 14px;
                    font-family: Helvetica;
                    color: #DF4A43;
                    line-height: 17px;
                }
            }
        }
    }
}

@media screen and (max-width: 768px) {
    .document_modal_wrap {
        width: 100% !important;
        top: 20px;
    }
}
