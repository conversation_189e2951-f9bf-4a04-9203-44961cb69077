import { Button, Checkbox, Modal, Progress, Spin,message } from 'antd';
import <PERSON><PERSON> from 'antd/lib/upload/Dragger';
import WebUploader from '@/components/UploadModal/WebUploader'
import React, { useState,useEffect } from "react";
import { useSelector } from "umi";
import Icon from "@ant-design/icons";
import { ReactComponent as plus_icon } from "@/assets/imgs/icon/plus_icon.svg";
import useLocale from "@/hooks/useLocale";
import { storageConfig } from '@/api/addCourse'
import jQuery from 'jquery';
import { HandleZip,handleZipTask,folderImport,getFolderImport,filesave,filemerge } from '@/api/course'
import {asyncLoadScript} from "@/utils";
const PlusIcon = (props: any) => <Icon component={plus_icon} {...props} />;
const AddZipModal = ({visible,onClose,contentConfirmLoading,uploadOk,pathVal,folderPath}: any) => {
  const { t } = useLocale();
  const [modalResourceLoading, setModalResourceLoading] = useState<boolean>(false);
  const [file, setFile] = useState<any>(null);
  const [uploadStatus, setUploadStatus] = useState<"success" | "exception" | "active" | 'normal' | "">("");
  const [percent,setPercent] = useState(0);
  const [percent2,setPercent2] = useState(0);
  useEffect(()=> {
    (window as any).$ = (window as any).jQuery = jQuery;
    asyncLoadScript('/rman/libs/webuploader/webuploader.js');
  },[])
  // 检查是否为zip文件的函数
  const isZipFile = (file: any) => {
    // 检查MIME类型
    const validMimeTypes = [
      'application/zip',
      'application/x-zip-compressed',
      'application/x-zip',
      'application/octet-stream'
    ];

    // 检查文件扩展名
    const fileName = file.name.toLowerCase();
    const hasZipExtension = fileName.endsWith('.zip');

    // 同时检查MIME类型和文件扩展名
    const hasMimeType = validMimeTypes.includes(file.type);

    return hasZipExtension && (hasMimeType || file.type === '');
  };

  const props = {
    name: 'file',
    multiple: false,
    showUploadList: false,
    accept: '.zip,application/zip,application/x-zip-compressed,application/x-zip',
    customRequest: () => true,
    onChange: async (info: any) => {
      if (!isZipFile(info.file)) {
        message.error('只能上传 zip 格式的压缩文件！请选择 .zip 文件');
        return;
      }
      setFile(info.file);
      setUploadStatus("normal")
      const path = await getPath([{
        fileName: info.file.name,
        fileLength: info.file.size,
        fileType: 'other',
        poolType:window.localStorage.getItem('upform_platform')==='Lark'?'ROLE':'',
        pathType:1
      }])
      if (path) {
        // 分片上传
        try {
          const uploadRes = await uploadZipByChunks(info.file.originFileObj, path as string);
          console.log(uploadRes)
          if (uploadRes) {
            // 分片合并后，继续后续解压和导入逻辑
            const data: string =  await HandleZipFn({ filePath: path + `/${info.file.name}`, fileSize: info.file.size }) as string;
            if (!data) return;
            const bal:any = await pollTaskStatus(data)
            setPercent(100);
            if (bal) {
              setUploadStatus('active')
              const id   =   await folderImportFn({
                folderPath: folderPath,
                zipFilePath: bal?.path || '',
                zipExtractPath: bal?.path.split('.')[0]  })
              console.log(id,'id')
              if (id) {
                const val:any = await pollFolderStatus(id as string)
                if (val?.isCompleted) {
                  message.success('上传成功')
                  uploadOk()
                }
              }
            }
          }

        } catch (e) {
          console.log(e)
          // setUploadStatus('exception');
        }
      }
    },
    beforeUpload: (file:any) => {
      // 在上传前再次验证文件类型
      if (!isZipFile(file)) {
        message.error('只能上传 zip 格式的压缩文件！请选择 .zip 文件');
        return false;
      }

      return true;
    },
    className: 'dragger',
    // ...uploadProps,
  };
  function getPath(params:any) {
    return new Promise((resolve, reject) => {
      storageConfig(params).then(res => {
        if (res && res.success && res.data && res.data[0] && res.data[0].path) {
          resolve(res.data[0].path)
        } else {
          resolve("");
        }
      }).catch(err => {
        resolve("");
      })
    })
  }
  let intervalId: any;
   function  handlePercent() {
    if (!intervalId) {
      intervalId = setInterval(() => {
        // 使用函数式更新，获取最新值
        setPercent((prev) => {
          const newValue = prev + 10;
          if (newValue >= 100) {
            clearInterval(intervalId); // 达到 100 时清除定时器
            intervalId = null;
            return 100; // 直接设为 100，避免溢出
          }
          return newValue;
        });
      }, 100);
    }
  }
  function handlePercent2(num: number) {
    if (!intervalId) {
      intervalId = setInterval(() => {
        // 使用函数式更新，获取最新值
        setPercent2((prev) => {
          const newValue = prev + 10;
          if (newValue >= num) {
            clearInterval(intervalId); // 达到 100 时清除定时器
            intervalId = null;
            return num; // 直接设为 100，避免溢出
          }
          return newValue;
        });
      }, 100);
    }
  }
   function HandleZipFn(data:any) {
     return new Promise((resolve,reject) => {
       HandleZip(data).then(res => {
         console.log(res,'res')
         if (res.status === 200) {
           resolve(res.data.data);
         }
       }).catch(err => {
         reject(false)
       })
     })
  }
  function handleZipTaskFn(data: any) {
    let attempts = 0;
     return new Promise((resolve,reject) => {
       handleZipTask(data).then(res => {
         console.log(res,'res')
         if (res.status === 200) {
           resolve(res.data.data);
         }
       }).catch(err => {
         reject(err)
       })
     })
  }
  function pollTaskStatus(data: string) {
    let attempts = 0;
    let timeoutId: NodeJS.Timeout | null = null;

    return new Promise((resolve, reject) => {
      const checkStatus = async () => {
        try {
          const response: any = await handleZipTaskFn(data);
          console.log(attempts,'attempts')
          console.log('response', response);
          // 终止条件：达到最大尝试次数
          // if (attempts >= 20) {
          //   clearTimeout(timeoutId!);
          //   return;
          // }

          // 处理响应结果
          if (response != null) {
            if (response?.isCompleted) {
              clearTimeout(timeoutId!);
              setPercent(100)
              resolve(response);
            }  else {
              if (response?.endTime && response?.endTime.length > 0) {
                clearTimeout(timeoutId!);
                message.error('上传失败')
                onClose()
              } else {
                attempts++;
                timeoutId = setTimeout(checkStatus, 500); // 继续轮询
                setPercent((prev) => {
                  const newValue = prev + 10;
                  if (newValue >= 90) {
                    intervalId = null;
                    return 90; // 直接设为 90
                  }
                  return newValue;
                });
              }
            }
          } else {
            attempts++;
            timeoutId = setTimeout(checkStatus, 500); // 继续轮询
            setPercent((prev) => {
              const newValue = prev + 10;
              if (newValue >= 90) {
                intervalId = null;
                return 90; // 直接设为 90
              }
              return newValue;
            });
          }
        } catch (error) {
         console.log('轮询失败:', error);
          clearTimeout(timeoutId!);
          // reject(error); // 显式拒绝 Promise
        }
      };

      checkStatus();
    });
  }
  function pollFolderStatus(data: string) {
    let attempts = 0;
    let timeoutId: NodeJS.Timeout | null = null;

    return new Promise((resolve, reject) => {
      const checkStatus = async () => {
        try {
          const response: any = await getFolderImportFn(data);
          console.log(attempts,'attempts')
          console.log('response', response);
          // 终止条件：达到最大尝试次数
          // if (attempts >= 20) {
          //   clearTimeout(timeoutId!);
          //   return;
          // }
          // 处理响应结果
          if (response != null && response?.isCompleted) {
            clearTimeout(timeoutId!);
            setPercent2(100)
            resolve(response);
          } else {
            attempts++;
            timeoutId = setTimeout(checkStatus, 500); // 继续轮询
            setPercent2((prev) => {
              const newValue = prev + 10;
              if (newValue >= 90) {
                intervalId = null;
                return 90; // 直接设为 90
              }
              return newValue;
            });
          }
        } catch (error) {
          console.log('轮询失败:', error);
          clearTimeout(timeoutId!);
          // reject(error); // 显式拒绝 Promise
        }
      };

      checkStatus();
    });
  }
  function folderImportFn(data: any) {
     return new Promise((resolve,reject) => {
       folderImport(data).then(res => {
         console.log(res,'folderImportFn')
         if (res && res.status === 200 && res.data && res.data.data) {
           resolve(res.data.data);
         } else {
           resolve(null);
         }
       }).catch(err => {
         resolve(null);
       })
     })
  }
  function getFolderImportFn(data: any) {
     return new Promise((resolve,reject) => {
       getFolderImport(data).then(res => {
         if (res && res.status === 200 && res.statusText === 'OK' && res.data && res.data.data) {
           resolve(res.data.data);
         } else {
           resolve(null);
         }
       }).catch(err => {
         resolve(null);
       })
     })
  }
  const uploadFile = async (fileList: any[]) => {
    console.log(fileList);
    if (!fileList || fileList.length === 0) return;
    const fileObj = fileList[0].originFileObj;
    setFile(fileObj);
    // HandleZip({  }).then(res => {
    //压缩zip
    const data = await HandleZipFn({ filePath: '',fileSize: fileObj.size })
    setUploadStatus("normal");
    handlePercent()
    // const data2  = await handleZipTask(data)
    setTimeout(() => {
      setUploadStatus("active");
      handlePercent2(100)
    },5000)

    console.log(data)
    // setTimeout(() => {
    //   handlePercent('active')
    // },2000)
    // })
    // setModalResourceLoading(true);

    // 假设你有一个上传接口 uploadZipFile
    // try {
    //   // 这里用 fetch/axios/umi-request 均可，以下为伪代码
    //   const formData = new FormData();
    //   formData.append('file', fileObj);
    //
    //   // 伪代码：你需要根据实际接口替换
    //   // const response = await uploadZipFile(formData, {
    //   //   onUploadProgress: (progressEvent: any) => {
    //   //     // 计算进度百分比
    //   //     const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
    //   //     // 这里假设 setTaskPanls 是你用来更新进度的
    //   //     // setTaskPanls([{ progress: percent / 100 }]);
    //   //   }
    //   // });
    //
    //   setUploadStatus("success");
    //   // 上传成功后的处理
    //   // 你可以在这里调用 onClose 或其他回调
    // } catch (error) {
    //   setUploadStatus("exception");
    //   // 错误处理
    // } finally {
    //   setModalResourceLoading(false);
    // }
  };
  function getGuid(prefix?: string) {
    let guid = (+new Date()).toString(32);
    for (let i = 0; i < 5; i++) {
      guid += Math.floor(Math.random() * 65535).toString(32);
    }
    return (prefix || 'wu_') + guid;
  }
  // 分片上传方法
  async function uploadZipByChunks(file: File & { lastModifiedDate: string }, uploadPath: string) {

    console.log('执行了',file)
    const CHUNK_SIZE = 2 * 1024 * 1024; // 2MB
    const totalChunks = Math.ceil(file.size / CHUNK_SIZE);
    // const guid = `${file.name}_${Date.now()}`; // 可用更优算法生成唯一guid
    let uploaded = 0;
    // let fileGuid = guid;
    const files = new (window as any).WebUploader.Lib.File(
      (window as any).WebUploader.Base.guid(),
      // orginTasks[index],
      file
      // item.file.originFileObj?.size || item.file //兼容拖拽有originFileObj的bug
    );
    console.log(files)
    const guid =  getGuid();
    for (let i = 0; i < totalChunks; i++) {
      const start = i * CHUNK_SIZE;
      const end = Math.min(file.size, start + CHUNK_SIZE);
      const blob = file.slice(start, end);
      const chunk = new File([blob], `${file.name}.part${i}`, {
        type: file.type || 'application/octet-stream'
      });
      console.log(chunk)
      const formData = new FormData();
      formData.append('file', chunk);
      formData.append('name', file.name);
      formData.append('size',file.size as unknown as string);
      formData.append('chunk', i.toString());
      formData.append('chunks', totalChunks.toString());
      formData.append('id', (window as any).WebUploader.Base.guid());
      formData.append('guid', guid);
      formData.append('lastModifiedDate',file.lastModifiedDate)
      formData.append('type', 'other');
      formData.append('fileGuid', uploadPath);
      // 你可以根据后端要求补充其他参数
      try {
        await filesave(formData);
        uploaded++;
        // setPercent(Math.round((uploaded / totalChunks) * 100));
      } catch (e) {
        message.error(`第${i + 1}片上传失败`);
        throw e;
      }
    }
    // 分片全部上传后，调用合并接口
    const { success } =   await filemerge(guid, file.name, uploadPath);
    return success;
    // console.log(test)
    // return { guid, fileName: file.name, fileGuid: guid, filePath: uploadPath + `/${file.name}` };
  }
  return <Modal
    title='zip上传'
    wrapClassName="resource-upload-modal"
    open={visible}
    footer={null}
    onCancel={onClose}
    width={550}>
    <div className="file-wrp">
      <div className="preview">
        <div className="pr-upload-wrapper">
          {(uploadStatus === 'normal' || uploadStatus === 'active') &&
            <div>
              <div>
                <div>{file.name}{percent === 100 ? t("解压成功") : t("正在解压...")}</div>
                <Progress percent={percent} status={uploadStatus} />
              </div>
              <div>
                <div>{file.name}{percent2 === 100 ? t('上传成功') : t("正在上传...")}</div>
                <Progress percent={percent2} status={uploadStatus} />
              </div>
            </div>
          }
          {
            uploadStatus === '' &&
            <Spin spinning={contentConfirmLoading}>
              <Dragger disabled={modalResourceLoading || contentConfirmLoading} {...props}>
                <p className="ant-upload-drag-icon">
                  <PlusIcon />
                </p>
                <p className="ant-upload-text">{t("将文件拖拽至此处，或")}<a>{t("点击上传")}</a></p>
              </Dragger>
            </Spin>}
        </div>
      </div>
    </div>
  </Modal>
}
export default AddZipModal;
