import React, { FC, useEffect, useRef, useState } from 'react';
import { history, useLocation } from "umi";
import "./HomeworkManagement.less";
import { Spin, Modal, message, But<PERSON>, Tooltip } from "antd";
import ChapterItem from "./components/ChapterItem";
import HomeworkItem from './components/HomeworkItem';
import HomeworkSubmission from "./homeworkSubmission";
import chapterApis from "@/api/chapter";
import { downloadSubmissions, deleteHomeworkApi, searchHomeworkList, urgeHomework } from "@/api/homework";
import { dealHomeworkTree, initExpandKeys } from "./utils/tools";
import AddHomework from '@/pages/HomeworkManagement/AddHomework';
import { IconFont } from '@/components/iconFont';
import moment from "moment";
import { PlusOutlined } from "@ant-design/icons";
import useLocale from '@/hooks/useLocale';

const HomeworkManagement: FC = () => {
  const { t } = useLocale();
  const location: any = useLocation();
  const [loading, setLoading] = useState(false);
  const [treeData, setTreeData] = useState<any[]>([]);
  const [selectNode, setSelectNode] = useState<any>();
  const [chapter, setChapter] = useState<any[]>([]);
  const [chapterItem, setChapterItem] = useState<any>({});
  const [chapterTreeData, setChapterTreeDate] = useState<any[]>([]);
  const [pageKey, setPageKey] = useState<string>("homework_all_list"); // 页面显示key(homework_all_list 、new_homework、edit_homework、chapter_homework、homework_detail）

  // const [showPage, setShowPage] = useState<number>(1); // 1:章节列表作业，2：作业详情

  useEffect(() => {
    getChapterTree();
    getChapter();
  }, []);

  const titleRender = (node: any) => {
    // console.info(node);
    switch (node.resourseType) {
      case "": case "chapter":
        return <ChapterItem data={node} />;
      case "homework":
        return <HomeworkItem data={node} />;
      case "map":
        return <HomeworkItem data={node} hideEndTime={true} hideUrge={true} hideWaitBtn={true} />
    }

  };
  const handleToFirst = () => {
    getChapterTree();
    setPageKey('chapter_homework');
  };

  const getMapData = (source: any[]) => {
    const data = {
      guid: "44abbe8248554e9daf0ea4296122ec02",
      id: "44abbe8248554e9daf0ea4296122ec02",
      key: "44abbe8248554e9daf0ea4296122ec02",
      title: "章节作业测试",
      name: "章节作业测试",
      className: "chapter-item",
      resourseType: "chapter",
      children: [
        {
          guid: "44abbe8248554e9daf0ea4296122ec01",
          id: "44abbe8248554e9daf0ea4296122ec01",
          key: "44abbe8248554e9daf0ea4296122ec01",
          title: "测什么",
          name: "测什么",
          className: "section-item",
          resourseType: "map",
          children: []
        },
        {
          guid: "44abbe8248554e9daf0ea4296122ec08",
          id: "44abbe8248554e9daf0ea4296122ec08",
          key: "44abbe8248554e9daf0ea4296122ec08",
          title: "怎么测",
          name: "怎么测",
          className: "section-item",
          resourseType: "map",
          children: []
        },
        {
          guid: "44abbe8248554e9daf0ea4296122ec09",
          id: "44abbe8248554e9daf0ea4296122ec09",
          key: "44abbe8248554e9daf0ea4296122ec09",
          title: "谁来测",
          name: "谁来测",
          className: "section-item",
          resourseType: "map",
          children: []
        },
      ]
    }
    source.unshift(data)
    return source
  }

  /**
   * 获取item的类型
   *
   * @param {*} item
   * @return {*}
   */
  const getItemType = (item: any, level: number) => {
    // debugger
    if (item.resourseType === 'homework') {
      return 'homework';
    } else if (item.resourseType && item.resourseType !== 'chapter') {
      return 'resource';
    } else if (level === 1) {
      return 'chapter';
    } else {
      return 'section';
    }
  };

  /**
  * 递归：原始chapter --> treeData
  *
  * @param {any[]} data
  * @return {*}
  */
  const formatChapter = (
    data: any[],
    level: number,
    orders: number[],
    pName?: string,
  ) => {
    let thisLevel = level + 1; // 记录当前层级
    let treeData: any[] = []; // 处理后的treeData
    let order = 0; // 记录资源数量，资源不增加小节编号
    let drafts: any[] = []; // 统计草稿章节
    let toPublish: any[] = []; // 统计待发布章节
    let published: any[] = []; // 统计已发布章节
    let chapters: any[] = []; // 统计章
    let sections: any[] = []; // 统计节
    let resources: any[] = []; // 统计资源

    data.forEach((item, index) => {
      let type = getItemType(item, thisLevel); // 判断type给item添加类名
      let curItem = {
        ...item,
        key: item.id,
        level: thisLevel,
        labelTitle: `${[...orders, index - order + 1].join('.')} ${item.name}`,
        className: 'type-' + type,
        title: item.name,
        orders: [...orders, index - order + 1],
        pName,
      };
      // 资源类型统计
      if (
        type === 'resource' ||
        type === 'homework' ||
        type === 'hyperlink' ||
        type === 'case' ||
        type === "material"
      ) {
        ++order;
        resources.push(curItem);
      } else if (type === 'chapter') {
        chapters.push(curItem);
      } else {
        sections.push(curItem);
      }
      //章节状态统计
      if (type !== 'resource') {
        if (item.status == '-1') {
          // 草稿
          drafts.push(curItem);
        } else if (item.status == '1') {
          // 已发布
          published.push(curItem);
        } else {
          // 待发布
          toPublish.push(curItem);
        }
      }
      if ((item.children && item.children.length <= 0) || !item.children) {
        // 无children，返回
        treeData.push(curItem);
      } else {
        // 有children，递归
        treeData.push({
          ...curItem,
          children: formatChapter(
            item.children,
            thisLevel,
            [...orders, index - order + 1],
            item.name,
          ),
        });
      }
    });
    return treeData;
  };

  /**
     * 获取章节资源树
     *
     */
  const getChapter = () => {
    const id = location.query.id;
    let data = `courseId=${id}`;
    data = data + `&status=2`;
    chapterApis.getChapter(data).then((res: any) => {
      if (res && res.status === 200 && res.data) {
        setChapter(res.data);
        console.log(res.data,'setChapter');
        
        const treeData = formatChapter(res.data, 0, []);
        setChapterTreeDate(treeData);
      }
    });
  };

  const getChapterTree = () => {
    setLoading(true);
    searchHomeworkList(location.query.id).then(res => {
      if (res.status === 200) {
        console.log(res.data,'先22222');
        
        setTreeData(res.data);
      }
    }).catch((err) => {
      message.error(t("查询失败"));
    }).finally(() => {
      setLoading(false);
    });
  };

  //作业导出
  const handleExport = () => {
    downloadSubmissions({ courseId: location.query.id }).then((res: any) => {
      if (res.status === 400) {
        message.error(res.message);
      } else {
        let link = document.createElement('a');
        link.target = '_blank';
        link.style.display = 'none';
        link.href = `/exam-api/resource/homework/download?courseId=${location.query.id}&courseSemester=${location.query.sm ?? 1}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link); //下载完成移除元素
      }
    });

  };

  // 获取信息
  const findSecectChapterInfo2 = (data: any[], sectionId: string) => {
    for (const node of data) {
      if (node.describe === sectionId) {
        console.log(node,'node');
        
        return node; // 找到匹配的节点，直接返回 id
      }
      if (node.children && node.children.length > 0) {
        const result: any = findSecectChapterInfo(node.children, sectionId); // 递归查找子节点
        if (result) {
          return result; // 一旦在子节点中找到，立即返回 id
        }
      }
    }
    return null; // 如果没有找到，返回 null
  }

  const handleAddHomework2 = () => {
    setChapterItem({ status: 1, className: 'new-homework' });
    setPageKey('new_homework')
  }


  const handleEditHomework2 = (info: any) => {
    const data = findSecectChapterInfo(chapter, info.id) || { ...info, connectChapter: 'noConnect', describe: info.id };
    setChapterItem({
      ...data,
      className: 'type-homework',
    });
    console.log(data,'99999');
    
    setPageKey('edit_homework')
  }

   /**
   * 查找章节信息，并确保返回的数据包含 guid 和 parentId 字段
   * @param data 章节树数据
   * @param sectionId 目标章节ID
   * @returns 包含 guid 和 parentId 的章节节点对象
   */
   const findSecectChapterInfo = (data: any[], sectionId: string): any => {
    for (const node of data) {
      if (node.describe === sectionId) {
        // 检查并补全 guid 和 parentId 字段
        return {
          ...node,
          guid: node.guid ?? node.id ?? '',
          parentId: node.parentId ?? '',
        };
      }
      if (node.children && node.children.length > 0) {
        const result: any = findSecectChapterInfo(node.children, sectionId);
        if (result) {
          return result;
        }
      }
    }
    return null;
  };

  const handleAddHomework = () => {
    setChapterItem({ status: 1, className: 'new-homework' });
    setPageKey('new_homework');
  };

  const handleEditHomework = (info: any) => {
    console.log(info, 'info',chapter);

    // 查找章节信息，确保包含 guid 和 parentId
    let data = findSecectChapterInfo(chapter, info.id);
    if (!data) {
      // 如果未找到，补全必要字段
      data = {
        ...info,
        connectChapter: 'noConnect',
        describe: info.id,
        guid: info.guid ?? info.id ?? '',
        parentId: info.parentId ?? info.sectionId ?? '',
      };
    } else {
      // 如果找到但缺少字段，也补全
      data = {
        ...data,
        guid: data.guid ?? data.id ?? '',
        parentId: data.parentId ?? info.sectionId ?? '',
      };
    }

    setChapterItem({
      ...data,
      className: 'type-homework',
    });
    setPageKey('edit_homework');
  };

  // 删除
  const handleDeleteHomework = (info: any) => {
    Modal.confirm({
      title: '删除作业',
      content: `确定要删除该作业吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk() {
        const params = {
          homeworkId: info.id,
          chapterId: info.chapterId,
          sectionId: info.sectionId,
          courseId: location.query.id,
          courseSemester: location.query.sm ?? 1,
          list: chapter,
        };

        deleteHomeworkApi(params).then((res: any) => {
          if (res.status === 200) {
            message.success("删除成功");
            getChapterTree();
          } else {
            message.error(res.message ?? '删除失败！');
          }
        })
      },

    });
  }

  // 批阅
  const handleCorrectWork = (info: any) => {
    const data = findSecectChapterInfo(chapter, info.id,);
    console.log(data,'data',info); // averageScore
    
    setSelectNode({ ...data, ...info, resourseType: 'homework',averageScore:info.averageScore });
    setPageKey('homework_detail')
  }

  // 催促提交作业
  const handleUrge = (info: any) => {
    let detailUrl
    if (location.query.type == 'microMajor') {
      detailUrl = `/learn/workbench/#/microprofessional/homework?id=${location.query.id}&type=1&sm=${location.query.sm}&isJoin=true&type=${location.query.type}`
    } else {
      detailUrl = `/learn/course/detail/${location.query.type}/courseWare/${location.query.id}?semester=${location.query.sm || 1}&homework=${info.id}`
    }

    Modal.confirm({
      content: "将给未提交作业的学生下发通知提醒，是否操作？",
      onOk() {
        urgeHomework(info.id, {
          detailUrl,
          courseName: info.title
        }).then((res: any) => {
          if (res.status === 200) {
            message.success("操作成功");
          } else {
            message.error("操作失败");
          }
        });
      }
    });
  };


  const openHomeworkDom = () => {
    return <AddHomework
      chapterAllTreeData={chapterTreeData}
      chapter={chapter}
      handleBack={() => {
        setPageKey('homework_all_list');
        getChapterTree();
      }}
      chapterItem={chapterItem}
    />
  }


  const homeworkListDom = () => {
    return (
      <div className="tree-container">
        <div className='homework-header-btn'>
          <Button type='primary' icon={<PlusOutlined />} onClick={handleAddHomework}>新建作业</Button>
          <div className='download-btn' onClick={handleExport}>导出作业成绩</div>
        </div>

        <div className='homework-table-style'>
          {treeData.map((item: any) => (
            <div key={item.id} className='homework-item'>
              <div className='left'>
                <div style={{ color: '#000', fontSize: 16 }}>
                  {item.state === 'draft' && <span className='draft'>草稿</span>}
                  <span className='edit-title' onClick={() => { handleEditHomework(item) }}>{item.title}</span>
                </div>
                <div style={{ color: 'rgba(0,0,0,0.5)', fontSize: 13, display: 'flex', gap: 40 }}>
                  <span>完成方式：<span className={`${item.howIsDone == 1 ? '' : 'team'}`} style={{ color: 'rgba(0,0,0,0.7)' }}>{item.howIsDone ? (item.howIsDone == 1 ? '个人' : '小组') : '-'}</span></span>
                  <span>截止时间：<span style={{ color: 'rgba(0,0,0,0.7)' }}>{item.closeTime ? moment(item.closeTime).format("yyyy-MM-DD HH:mm:ss") : '-'}</span></span>
                  <span>所属章节：<span style={{ color: 'rgba(0,0,0,0.7)' }}>{item.sectionName || item.chapterName}</span></span>
                </div>
              </div>

              <div className='action-btn'>
                <Tooltip title='编辑'><IconFont type='iconedit1' onClick={() => { handleEditHomework(item) }} /></Tooltip>
                <Tooltip title='删除'><IconFont type='icondelete2' onClick={() => { handleDeleteHomework(item) }} /></Tooltip>
                <Tooltip title='查看提交情况'><IconFont style={{ fontSize: 18 }} type='iconxiangqingchakan' onClick={() => { handleCorrectWork(item) }} /></Tooltip>
              </div>

              <div className='right'>
                <div  style={{ fontSize: 16 }}>提交情况：
                  {item.state === 'pub' && <Tooltip title='点击查看作业提交情况'>
                    <span className={item.state === 'pub' ? 'submit-info-notice' : ''} onClick={() => { handleCorrectWork(item) }}>
                    <span style={{ color: 'var(--primary-color)', fontSize: 24, fontWeight: 'bold' }}>{item.stuSubmitNum ?? '-'}</span>
                    <span>/{item.stuNum ?? '-'}</span>
                    <span> 已提交</span>
                  </span>
                  </Tooltip>}
                  {item.state === 'draft' && <span>-</span>}
                </div>
                <div  style={{ fontSize: 16 }}>平均得分：
                  <span style={{ color: 'var(--primary-color)', fontSize: 24, fontWeight: 'bold' }} >{item.averageScore ?? ''}</span>
                  <span> 分</span>
                </div>
                {item.noCheckNum > 0 && <Button type='primary' onClick={() => { handleCorrectWork(item) }}>{item.noCheckNum} 待批阅</Button>}
                {item.state === 'pub' && (item.stuSubmitNum < item.stuNum) && <Button ghost type="primary" onClick={() => { handleUrge(item) }}>催交</Button>}
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  const showCurrentDom = () => {
    switch (pageKey) {
      case 'homework_all_list':
        return homeworkListDom(); // 作业列表页面
      case 'new_homework':
        return openHomeworkDom(); // 新建作业页面
      case 'edit_homework':
        return openHomeworkDom(); // 编辑作业页面
      case 'homework_detail':
        return <HomeworkSubmission homeworkItem={selectNode} handleBack={handleToFirst} />
      case 'chapter_homework':
        return homeworkListDom();
      default:
        return homeworkListDom();
    }
  }

  return <div className='homework-container'>
    <Spin spinning={loading}>
      {showCurrentDom()}
    </Spin>
  </div>
  // return <div className='homework-container'>
  //   {
  //     showPage === 1 ? <div className="tree-container">
  //       {treeData.length > 0 ?
  //         // <>{location.query.showDownload && <div className='homework-header'>
  //         //   <div className='title'>{t("作业")}</div>
  //         //   <span className='export-btn' onClick={handleExport}>
  //         //     <IconFont type="iconexport"></IconFont><span>{t("作业提交情况导出Excel")}</span>
  //         //   </span>
  //         // </div>}
  //         //   <Tree
  //         //     blockNode
  //         //     defaultExpandAll
  //         //     expandedKeys={expandedKeys}
  //         //     icon={null}
  //         //     onExpand={(expandKeys) => {
  //         //       setExpandedKeys((expandKeys as string[]));
  //         //     }}
  //         //     treeData={treeData}
  //         //     titleRender={(node) => titleRender(node)}
  //         //     onSelect={(selectedKeys, info: any) => {
  //         //       if (info.node.resourseType === "homework" || info.node.resourseType === "map") {
  //         //         setShowPage(2);
  //         //         setSelectNode(info.node);
  //         //       }

  //         //     }} />
  //         // </>
  //         <div>
  //           {treeData.map((item: any) => (
  //             <div key={item.id}>{item.title}</div>
  //           ))}
  //         </div>
  //          : <div className='empty-container'>
  //           {/* <div className="empty-header">{t("作业")}</div> */}
  //           <div className="empty-content">
  //             {loading.current ? <LoadingOutlined /> :
  //               <>{t("暂时没有作业")}
  //                 {location.query.type !== "map" && <>
  //                   {t("，请前往")}
  //                   <a href={`#/editcourse/${location.query.type === 'mooc' ? 'moocChapter' : 'chapter'}?id=${location.query.id}&type=${location.query.type}&sm=${location.query.sm ?? 1}`}>{t("章节内容")}</a>{t("页面创建作业")}
  //                 </>}
  //               </>}
  //           </div>

  //         </div>}
  //     </div> : <HomeworkSubmission homeworkItem={selectNode} handleBack={handleToFirst} />}

  // </div>;
};

export default HomeworkManagement;
