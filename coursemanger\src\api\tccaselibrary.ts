import HTTP from './index';

// 分页查询课程案例库
export function getCaseList(params: any) {
  return HTTP(`/learn/teaching/case/library/query/page`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 通过id查询教学课程案例数据
export function getCaseDataById(id: string) {
  return HTTP(`/learn/teaching/case/library/find/by/${id}`, {
    method: 'GET',
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 新增教学课程案例数据
export function addNewTeachingCase(data: any) {
  return HTTP.post('/learn/teaching/case/library/add', data)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

//批量删除教学课程案例数据
export function deleteTeachingCaseBatch(data: any) {
  return HTTP.post('/learn/teaching/case/library/batch/delete', data)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

//批量发布、驳回教学课程案例数据
export function publishAndRejectBatch(data: any, params?: any) {
  return HTTP.post('/learn/teaching/case/library/batch/publish', data, {
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

//修改教学课程案例数据
export function updateCase(data: any) {
  return HTTP.post('/learn/teaching/case/library/update', data)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

//导出教学课程案例数据
export function exportCaseData(params: any) {
  return HTTP(`/learn/teaching/case/library/export/list`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export const queryCollegesList = () =>
  HTTP('/learn/teaching/case/library/college/list', {
    method: 'GET',
    params: { isHome: 0 },
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 回收站-批量删除|还原教学课程案例 0 删除  1  还原
export const deleteCaseCompleteBatch = (params: { type: 0 | 1}, data: string[]) => HTTP(`/learn/teaching/case/library/batch/completely/delete`, {
  method: 'POST',
  params,
  data
}).then(res => {
  if (res.status === 200) {
    return res.data;
  }
})
.catch(error => {
  return error;
});

//回收站-清空回收站
export const clearAllCaseInRecycle = () => HTTP(`/learn/teaching/case/library/clear/data`, {
  method: 'GET',
}).then(res => {
  if (res.status === 200) {
    return res.data;
  }
})
.catch(error => {
  return error;
});

//回收站-分页查询课程案例库
export const queryCaseListInRecycle = (params: {name: string, page: number, size: number}) => HTTP(`/learn/teaching/case/library/query/completely/page`, {
  method: 'GET',
  params
}).then(res => {
  if (res.status === 200) {
    return res.data;
  }
})
.catch(error => {
  return error;
});