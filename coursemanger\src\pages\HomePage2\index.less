.app {
  text-align: center;
  min-height: 100vh;
  min-width: 1200px;
  display: flex;
  flex-direction: column;
  background: url("../../assets/imgs/homePage2/bg.png") no-repeat center center fixed;
  background-size: cover;

}

.app-main {
  flex: 1;
  padding: 40px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.modules-container {
  max-width: 1200px;
  margin: auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  padding: 20px;
}
.module-icon-bottom {
  //padding-top: 24px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  p {
    font-size: 10px;
    text-align: left;
    margin: 0 !important;
  }
}
.module-card {
  //background: white;
  //width: 300px;
  //height: 450px;
  border-radius: 12px;
  padding:12px 24px;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-repeat: no-repeat;
  background-size: cover;
}

.module-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.module-icon-container {
  width: 170px;
  height: 170px;
  //background: #e6f7ff;
  //border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  img {
    width: 100%;
    height: auto;
  }
}

.module-icon {
  font-size: 32px;
}

.module-card h2 {
  color: #333;
  margin: 0 0 12px 0;
  font-size: 28px;
  font-weight: 800;
}


.app-footer {
  background-color: #fff;
  color: #666;
  padding: 20px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}
