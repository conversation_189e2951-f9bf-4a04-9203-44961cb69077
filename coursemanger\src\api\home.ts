import HTTP from './index';

// * 课程库

export function getLatestResources() {
  return HTTP.get(`/learn/v1/statistics/latest/resources`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getOpenResources() {
  return HTTP.get(`/learn/v1/statistics/recently/open/resources`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getAssistant() {
  return HTTP.get(`/learn/v1/statistics/teaching/assistant`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getWelcome() {
  return HTTP.get(`/learn/v1/statistics/welcome/section`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getCourse() {
  return HTTP.get(`/learn/v1/teaching/my/course`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function reqWeeks(firstDay: string) {
  return HTTP.get(
    `/ipingestman/schedule/course_loop/usercoursecalendar?date=${firstDay}`,
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function reqEvents(day: string) {
  return HTTP.get(
    `/ipingestman/schedule/course_loop/usercoursedayplan?date=${day}`,
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function reqNoticeCourseId() {
  return HTTP.get(`/learn/v1/course/live/random`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
export const fetchFileFormat = () =>
  HTTP.get(`/rman/v1/upload/format`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });

export const addRemark = (data: any) =>
  HTTP.post('/ipingestman/schedule/course_loop/usercalendarremark', data)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
export const deleteRemark = (params: any) =>
  HTTP.get('/ipingestman/schedule/course_loop/usercalendarremark', { params })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });

export const getWeek = (params: any) =>
  HTTP.get('/ipingestman/schedule/supervision/current_semester_teaching_week', {
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });

export const getClassSchedule = (week: number,semester:string) =>
  HTTP.get(
    `/ipingestman/schedule/course_loop/new_usercoursecalendar?week=${week}&semester=${semester}`,
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });

export const getSemesterList = () =>
  HTTP.get(`/unifiedplatform/v1/base/data/database/get/semester/by/name`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });

// 我的资源文件树
export const getResouceTree = () =>
  HTTP.get(`/rman/v1/folder/all/tree?level=2`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });

export const getFolderFile = (data: any) =>
  HTTP.post('/rman/v1/search/folder', data)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });

export const getMyResLink = (
  contentId: string,
  link = false,
  shareFlag_ = false,
) => {
  let url = '';
  if (shareFlag_) {
    url = `/entity/base/${contentId}`;
  } else {
    if (
      navigator.userAgent.match(/Mobi/i) ||
      navigator.userAgent.match(/Android/i) ||
      navigator.userAgent.match(/iPhone/i) ||
      window.innerWidth < 768
    ) {
      url = `/entity/base/${contentId}?isSysAuth=true`;
    } else {
      url = `/entity/base/${contentId}`;
    }
  }

  return HTTP(`${link}` ? url : `/entity/base/${contentId}?isSysAuth=true`, {
    method: 'GET',
  });
};
