import HTTP from './index';
import { fetchEventSource } from '@microsoft/fetch-event-source';

// 获取课程列表(班级课和公开课)
export const reqCourseListApi = (data: any) => {
    return HTTP(`/learn/v1/teaching/course/get/courses/list`, {
        method: 'GET',
        params: data
    }).then(res => {
        if (res.status === 200) {
            return res.data;
        }
    }).catch(error => {
        console.error(error);
    });
}

// 获取课程章节
export const reqCourseChapterApi = (data: any) => {
    return HTTP(`/learn/v1/teaching/course/get/chapter/contents`, {
        method: 'GET',
        params: data
    })
}

export function getChapter(param: any) {
    const data = param instanceof Object ? Object.keys(param).map(key => `${key}=${param[key]}`).join('&') : param;
    return HTTP.get(`/learn/v1/teaching/course/get/chapter/contents?${data}`)
        .then(res => {
            if (res.status === 200) {
                return res.data;
            }
        })
        .catch(error => {
            console.error(error);
        });
}


const abortController = new AbortController();
const signal = abortController.signal;
const postStream: (url: string, data?: unknown) => Promise<any> | any = (
    url,
    data
) => {

    const headers: HeadersInit = { 'Content-Type': 'application/json' }
    return fetch(url, {
        method: 'POST',
        body: data ? JSON.stringify(data) : undefined,
        signal,
        headers: headers
    })
}

export function getHomeworkDetail(
    pageType: 'resource' | 'template',
    homeworkId: string,
    parentId: string | undefined,
    courseId: string,
) {
    return HTTP.get(
        `/exam-api/${pageType}/homework/detail?id=${homeworkId}&courseId=${courseId}${parentId ? `&parentId=${parentId}` : ''
        }`,
    )
        .then(res => {
            if (res.status === 200) {
                return res.data;
            }
        })
        .catch(error => {
            console.error(error);
        });
}

// 主观题批改
export const reqMessage = (data: any) => {
    return postStream(`/aiclass/v1/homework/correct`, data)
}

// 学生答案
export const getStudentAnswer = async (data: any) => {
    return HTTP(`/aiclass/v1/homework/getStudentAnswer?homeworkId=${data.homeworkId}&questionId=${data.questionId}`, {
        method: 'GET',
    }).then(res => {
        if (res.status === 200) {
            return res.data;
            // return {
            //     status: 200,
            //     data: [
            //         {
            //             id: '111',
            //             "stuCode": "20220242001",
            //             "stuName": "李胜",
            //             "answer": '马克思强调人的本质由社会关系决定。例如，职场中人的身份（员工、领导）由生产关系定义。但未深入讨论个体如何主动塑造社会关系。'
            //         },
            //         {
            //             id: '112',
            //             "stuCode": "20220242002",
            //             "stuName": "胡荣华",
            //             "answer": "现代社交媒体的“人设”现象印证了这一点——人在不同平台（如微信、微博）呈现不同形象，本质是社会关系的投射。但未联系马克思主义阶级分析。"
            //         },
            //         {
            //             id: '113',
            //             "stuCode": "20220242003",
            //             "stuName": "徐值",
            //             "answer": '人的角色（如父母、公民）随社会关系变化。比如“全职妈妈”的社会评价受经济结构影响。但未反思这种关系中的异化问题。'
            //         },
            //         // {
            //         //     id: '111',
            //         //     "stuCode": "20220242004",
            //         //     "stuName": "郭倩",
            //         //     "answer": '引用《关于费尔巴哈的提纲》说明人脱离不了社会，如农民工的权益问题反映生产关系不平等。但解决方案未提及。'
            //         // },
            //         // {
            //         //     id: '111',
            //         //     "stuCode": "20220242005",
            //         //     "stuName": "吴昌云",
            //         //     "answer": '平台经济中，外卖骑手的“算法困局”体现其本质被资本关系支配。但未延伸至集体行动的可能性。'
            //         // },
            //         // {
            //         //     id: '111',
            //         //     "stuCode": "20220242006",
            //         //     "stuName": "周子瑜",
            //         //     "answer": '马克思的论断揭示：人的“自我”是关系的产物。例如，互联网时代的“数字劳工”（如内容创作者）看似自由，实则被流量逻辑异化。需通过劳动者联合（如平台工会）重构社会关系。'
            //         // },
            //         // {
            //         //     id: '111',
            //         //     "stuCode": "20220242007",
            //         //     "stuName": "刘洋",
            //         //     "answer": '结合社会学“角色冲突”理论：当代人因多元社会关系（如职场、家庭）陷入身份撕裂。马克思主义的解决路径是变革压迫性结构（如996工作制），而非仅个体调节。'
            //         // },
            //         // {
            //         //     id: '111',
            //         //     "stuCode": "20220242008",
            //         //     "stuName": "张裕",
            //         //     "answer": '马克思是说人不能独居，要有朋友。'
            //         // },
            //         // {
            //         //     id: '111',
            //         //     "stuCode": "20220242009",
            //         //     "stuName": "徐贵昆",
            //         //     "answer": '这句话证明社会主义优越性，因为资本主义让人冷漠。'
            //         // },
            //         // {
            //         //     id: '111',
            //         //     "stuCode": "20220242010",
            //         //     "stuName": "任飞非",
            //         //     "answer": '社会关系就是亲情和友情，所以要多交友。马克思也支持这点。'
            //         // },
            //     ]
            // }
        }
    }).catch(error => {
        console.error(error);
    });
}
