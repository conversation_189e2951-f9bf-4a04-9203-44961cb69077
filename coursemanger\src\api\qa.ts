/*
 * @Author: lijin
 * @Description: 问答 API 请求
 * @Date: 2022-02-22 16:17:37
 * @LastEditTime: 2022-02-28 15:40:41
 * @LastEditors: lijin
 * @FilePath: \coursemanger\src\api\qa.ts
 */
import HTTP from '@/api/index';

namespace QAService {
  //--------------统计
  /**
   * @description: 未读数量统计
   * @param {*}
   * @return {*}
   */
  export const fetchUnReadCount = (param: {
    user_id_of_unread: string;
    link_id: string;
  }) =>
    HTTP('/forumservice/ForumBase/unread_count/', {
      method: 'GET',
      params: param,
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  //-------------------------------主题
  /**
   * @description: 查询主题
   * @param {QA.TopicSearch} param
   * @return {*}
   */
  export const fetchTopicList = (param: QA.TopicSearch) =>
    HTTP('/forumservice/ForumBase/Topic/', {
      method: 'GET',
      params: param,
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  /**
   * @description: 主题新增
   * @param {QA.TopicSearch} param
   * @return {*}
   */
  export const addTopic = (data: QA.TopicAdd) =>
    HTTP('/forumservice/ForumBase/Topic/', {
      method: 'POST',
      data,
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  /**
   * @description: 删除主题
   * @param {string} topicids 主题id[]
   * @param {string} linkids  资源id[]--删除资源下的所有主题
   * @return {*}
   */
  export const deleteTopics = (
    topic_id_list: string[],
    link_id: string
  ) => {
    return HTTP('/forumservice/ForumBase/Topic/delete/', {
      method: 'POST',
      data: {
        topic_id_list: topic_id_list.length > 0 ? topic_id_list : undefined,
        link_id
      },
    }).then(res => {
      if (res.status === 200) {
        return res.data;
      }
    });
  };

  /**
   * @description: 已读主题
   * @param {QA} data
   * @return {*}
   */
  export const readTopic = (data: QA.TopicReadParam) => {
    return HTTP('/forumservice/ForumBase/mark_read/', {
      method: 'POST',
      data,
    }).then(res => {
      if (res.status === 200) {
        return res.data;
      }
    });
  };

  // -------------------------回复
  /**
   * @description: 查询回复
   * @param {QA.ReplySearch} param
   * @return {*}
   */
  export const fetchReplyList = (param: QA.ReplySearch) =>
    HTTP('/forumservice/ForumBase/Comment/', {
      method: 'GET',
      params: param,
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  /**
   * @description: 新增回复
   * @param {QA.ReplyAdd} data
   * @return {*}
   */
  export const addReply = (data: QA.ReplyAdd) =>
    HTTP('/forumservice/ForumBase/Comment/', {
      method: 'POST',
      data,
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });

  /**
   * @description: 删除回复
   * @param {string[]} comment_id_list
   * @return {*}
   */
  export const deleteReply = (comment_id_list: string[], link_id: string) =>
    HTTP('/forumservice/ForumBase/Comment/delete/', {
      method: 'POST',
      data: { comment_id_list, link_id },
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });

  /**
   * @description: 已读回复
   * @param {string} read_user_id 阅读者的ID
   * @param {string[]} comment_id_list 评论ID的列表 或者字符串 “all”。若 comment_id_list 传入字符串参数 all 则会标记所有与用户相关的评论为已读状态。
   * @return {*}
   */
  export const readReply = (
    read_user_id: string,
    comment_id_list: string[],
  ) => {
    if (comment_id_list.length <= 0) {
      return;
    }
    return HTTP('/forumservice/ForumBase/mark_read/', {
      method: 'POST',
      data: { ids: comment_id_list.join(",") },
    }).then(res => {
      if (res.status === 200) {
        return res.data;
      }
    });
  };
  export const getReply = (params: any, data: any) => {
    return HTTP('/forumservice/ForumBase/Reply/', {
      method: 'GET',
      params,
      data
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }
  // ----------- 评论
  /**
   * @description: 新增评论
   * @param {QA.CommentAdd} data
   * @return {*}
   */
  export const addComment = (data: QA.CommentAdd) =>
    HTTP('/forumservice/ForumBase/Reply/', {
      method: 'POST',
      data,
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });

  /**
   * @description: 删除评论
   * @param {string[]} reply_id_list
   * @return {*}
   */
  export const deleteComment = (reply_id_list: string[], link_id: string) =>
    HTTP('/forumservice/ForumBase/Reply/delete/', {
      method: 'POST',
      data: {
        reply_id_list,
        link_id
      },
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  /**
   * @description: 已读评论
   * @param {string} read_user_id 阅读者的ID
   * @param {string[]} comment_id_list 评论ID的列表 或者字符串 “all”。若 comment_id_list 传入字符串参数 all 则会标记所有与用户相关的评论为已读状态。
   * @return {*}
   */
  export const readComment = (
    read_user_id: string,
    reply_id_list: string[],
  ) => {
    if (reply_id_list.length <= 0) {
      return;
    }
    return HTTP('/forumservice/ForumBase/mark_read/', {
      method: 'POST',
      data: { ids: reply_id_list.join(",") },
    }).then(res => {
      if (res.status === 200) {
        return res.data;
      }
    });
  };
  //-------------------其他
  /**
   * @description: 批量获取用户头像
   * @param {string[]} userIds
   * @return {*}
   */
  export const fetchUser = (userIds: string[], link_id: string) => {
    if (userIds.length <= 0) {
      return;
    }
    return HTTP('/forumservice/ForumBase/UserAvatar/list/', {
      method: 'POST',
      data: {
        user_id_list: userIds,
        link_id
      },
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  };
  export const setPublic = (data: any) => {
    return HTTP('/forumservice/ForumBase/Topic/permissions/', {
      method: 'POST',
      data,
    }).then(res => {
      if (res.status === 200) {
        return res.data;
      }
    });
  };
}
export default QAService;
