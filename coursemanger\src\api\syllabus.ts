import { message } from "antd";
import HTTP from "./index";

/**
 * 待我审核
 */
export const auditToMe = (params: any) => {
  return HTTP('/unifiedplatform/v1/audit/owner/examine/instance', {
    method: 'POST',
    params
  }).then(res => {
    if (res.status === 200) {
      return res.data
    }
  }).catch(err => {
    return err
  })
}

export const videoAuditToMe = (params: any) => {
  return HTTP('/pyfa/v1/audit/owner/examine/instance', {
    method: 'get',
    params
  }).then(res => {
    if (res.status === 200) {
      return res.data
    }
  }).catch(err => {
    return err
  })
}
export const myVideoAudit = (params: any) => {
  return HTTP(`/pyfa/v1/audit/owner/instance`, {
    method: 'get',
    params
  }).then(res => {
    if (res.status === 200) {
      return res.data
    }
  }).catch(err => {
    return err
  })
};

/**
 * 我发起的审核
 */
export const myInitateAudit = (params: any) => {
  return HTTP(`/unifiedplatform/v1/audit/owner/instance`, {
    method: 'POST',
    params
  }).then(res => {
    if (res.status === 200) {
      return res.data
    }
  }).catch(err => {
    return err
  })
};

/**
 * 查询流程审核实例资源
 */
export const getAuditInstance = (params: any) => {
  return HTTP(`/unifiedplatform/v1/audit/instance/resource`, {
    method: 'GET',
    params
  }).then(res => {
    if (res.status === 200) {
      return res.data
    }
  }).catch(err => {
    return err
  })
}

  //批量撤回审核
  export const batchRevokeResource = (data: any) => {
    return HTTP(`/pyfa/v1/audit/revoke`, {
      method: 'POST',
      data
    }).then(res => {
      if (res.status === 200) {
        return res.data
      }
    }).catch(err => {
      return err
    })
  };
/**
 * 批量通过审核
 */

export const opreateAudit = (data: any) => {
  return HTTP(`/pyfa/v1/audit/process`, {
    method: 'POST',
    data,
  }).then(res => {
    if (res.status === 200) {
      return res.data
    }
  }).catch(err => {
    return err
  })
}

/**
 * 查询审核日志
 */

export const getAuditLogs = (contentId: string, page: number, size: number) => {
  return HTTP(`/unifiedplatform/v1/audit/log`, {
    method: 'GET',
    params: { contentId, page, size }
  }).then(res => {
    if (res.status === 200) {
      return res.data
    }
  }).catch(err => {
    return err
  })
}

/**
 * 撤回审核
 */
export const revokeAudit = (params: any) => {
  return HTTP(`/pyfa/v1/audit/revoke`, {
    method: 'POST',
    params
  }).then(res => {
    if (res.status === 200) {
      return res.data
    }
  }).catch(err => {
    return err
  })
}
export const getToken = () => {
    return HTTP('/learn/bpm/info',
    { method: 'GET' }
    ).then(res => {
        if (res.status === 200) {
        return res.data
        }
    }).catch(err => {
        return err
    })
  }
/**
 * 获取流程实例日志
 */
export const getProcessInstanceLogs = (processInstanceId: string, headers: BpmnHeaders) => {
    return HTTP('/flowbpm/bpm/task/list-by-process-instance-id', {
      method: 'GET',
      params: { processInstanceId },
      headers
    }).then(res => {
      if (res.status === 200) {
        return res.data
      }
    }).catch(err => {
      return err
    })
  }
