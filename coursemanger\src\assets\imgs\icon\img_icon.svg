<?xml version="1.0" encoding="UTF-8"?>
<svg width="28px" height="28px" viewBox="0 0 28 28" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 10备份 4</title>
    <defs>
        <linearGradient x1="-8.10802576%" y1="-8.72225403%" x2="108.528912%" y2="110.467875%" id="linearGradient-1">
            <stop stop-color="#9FF2B3" offset="0%"></stop>
            <stop stop-color="#18B643" offset="100%"></stop>
        </linearGradient>
        <path d="M14.2222222,-6.4423999e-16 L1.77777777,-6.4423999e-16 C0.800006407,-6.4423999e-16 -1.18713967e-15,0.868299043 -1.18713967e-15,1.92953797 L-1.18713967e-15,14.070462 C-1.18713967e-15,15.131701 0.800006407,16 1.77777777,16 L14.2222222,16 C15.1999936,16 16,15.131701 16,14.070462 L16,1.92953797 C16,0.868299043 15.1999936,-6.4423999e-16 14.2222222,-6.4423999e-16 Z M3.93296949,2.25863524 C4.83000077,2.25863524 5.55717442,3.04788415 5.55717442,4.02149061 C5.55717442,4.99509706 4.83000077,5.78434597 3.93296949,5.78434597 C3.03593821,5.78434597 2.30876456,4.99509706 2.30876456,4.02149061 C2.30876456,3.04788415 3.03593819,2.25863524 3.93296949,2.25863524 Z M1.77777777,13.1057162 L4.8888782,8.62643994 L7.11108974,11.5207469 L10.2222115,7.17929804 L14.2222009,13.1057162 L1.77777777,13.1057162 L1.77777777,13.1057162 Z" id="path-2"></path>
        <filter x="-43.8%" y="-31.2%" width="187.5%" height="187.5%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.505882353   0 0 0 0 0.874509804   0 0 0 0 0.596078431  0 0 0 0.6 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="切图" transform="translate(-172.000000, -179.000000)">
            <g id="编组-10备份-4" transform="translate(172.000000, 179.000000)">
                <circle id="椭圆形" fill="#DDF4E2" cx="14" cy="14" r="14"></circle>
                <g id="图片" transform="translate(6.000000, 6.000000)" fill-rule="nonzero" opacity="0.900000036">
                    <g id="形状">
                        <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                        <use fill="url(#linearGradient-1)" xlink:href="#path-2"></use>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>