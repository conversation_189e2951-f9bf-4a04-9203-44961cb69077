import React, { useState, useEffect } from "react";
import { Tabs, Table, Button, message, InputNumber, Input, Modal } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import './index.less'
import { submitScoreCorrection, getSubmissionDetail2 } from '@/api/homework';
import { rearg } from "lodash";
interface interfaceContentRight {
    questionList: any[];
    changeQuestionList: (list: any[]) => void;
    btnLoading: boolean
    ids: any
}

const columns: ColumnsType = [
    {
        title: '姓名',
        dataIndex: 'stuName',
        render: (text: string) => {
            return <span className="stuName">{text}</span>
        },
        width: 100,
        align: 'center'
    },
    {
        title: '学号',
        dataIndex: 'stuCode',
        render: (text: string) => {
            return <span className="stuCode">{text}</span>
        },
        align: 'center'
    },
    {
        title: '解答',
        dataIndex: 'answer',
        render: (text: string) => {
            return <span className="answer">{text || '-'}</span>
        },
    },
    {
        title: 'AI评分',
        dataIndex: 'score',
        render: (text: string) => {
            return <span className="score">{text}</span>
        },
        width: 100,
        align: 'center'
    },
    {
        title: 'AI评语',
        dataIndex: 'comment',
        render: (text: string) => {
            return <span className="comment">{text}</span>
        },
    },

];

const ContentRight = (props: interfaceContentRight) => {
    const { questionList, changeQuestionList, btnLoading, ids } = props;
    const [tabList, setTabList] = useState<any[]>([]);
    const [activeTabKey, setActiveTabKey] = useState<string>('');
    const [temporaryId, setTemporaryId] = useState<string>('[]') // 临时ID
    const [tableData, setTableData] = useState<any[]>([])
    const [tableColumns, setTableColumns] = useState<any[]>([])
    const [loading, setLoading] = useState(false);
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const [detailIsEdit, setDetailIsEdit] = useState(false)
    const [openDetail, setOpenDetail] = useState(false) // 打开详情
    const [detailData, setDetailData] = useState<any>({}) // 详情数据

    useEffect(() => {
        if (columns?.length > 0) {
            let lastColumn = {
                title: '',
                dataIndex: 'operate',
                render: (text: string, current: any) => {
                    return <span className="detail" onClick={() => { setOpenDetail(true); setDetailData(current) }}>{current.comment ? '详情' : ''}</span>
                },
                width: 100,
                align: 'center'
            }
            setTableColumns([...columns, lastColumn])
        }
    }, [columns])

    useEffect(() => {
        setTabList(questionList) // 初始化tableList
        questionList?.length > 0 && setTemporaryId(questionList[0]?.id)
    }, [questionList])

    useEffect(() => {
        if (temporaryId) {
            setActiveTabKey(temporaryId)
        }
    }, [temporaryId])

    useEffect(() => {
        if (activeTabKey && tabList?.length > 0) {
            const currentTab: any = tabList.find((item) => item.id == activeTabKey)
            setTableData(currentTab?.studentAnswer || [])
        }
    }, [activeTabKey, tabList, questionList])

    //引用
    const handleReference = () => {
        if (btnLoading) {
            message.info('请等待AI生成完成')
            return
        }
        if (selectedRowKeys?.length == 0) {
            message.info('请选择要引用的数据')
            return
        }
        Modal.warning({
            title: '提示',
            content: '一键引用将自动引用勾选学生的所有题目的AI评分及评语内容，是否确认操作？',
            okText: '确定',
            closable: true,
            onOk() {
                setLoading(true)
                const exactlySelectKeys = selectedRowKeys?.map((item: any) => item?.split('-')[0]) // 勾选的人员
                // 一个学生调用一次接口
                const handleSubmit = async () => {
                    try {
                        for (const element of exactlySelectKeys) {
                            let params = {
                                courseSemester: '',
                                courseType: ids.courseType
                            }
                            const selectedRow: any = [] // 拿到当前人员所有题的数据
                            let subId = ''
                            tabList?.forEach((item: any) => {
                                item.studentAnswer?.forEach((ele: any) => {
                                    if (element == ele.stuCode) {
                                        subId = ele.subId
                                        selectedRow.push({ ...ele, questionId: item.id })
                                    }
                                })
                            })

                            let detailData: any = {}
                            await getSubmissionDetail2(subId, params).then(res => { // 获取该成员所有题的详情
                                if (res.status === 200) {
                                    detailData = res.data
                                }
                            })

                            if (detailData?.stuCode) {
                                const question_comments = {
                                    ...detailData.question_comments,
                                    ...selectedRow?.reduce((acc: any, item: any) => {
                                        acc[item.questionId] = item.comment || ''; // 单个题目的平论
                                        return acc;
                                    }, {})
                                }
                                const answers = {
                                    ...detailData.answers,
                                    ...selectedRow?.reduce((acc: any, item: any) => {
                                        acc[item.questionId] = [item.answer || '']; // 单个题目的答案
                                        return acc;
                                    }, {})
                                }
                                const checkScores = {
                                    ...detailData.scores,
                                    ...selectedRow?.reduce((acc: any, item: any) => {
                                        acc[item.questionId] = Number(item.score) || 0; // 单个题目的分数
                                        return acc;
                                    }, {})
                                }
                                let data = {
                                    homeworkId: ids.homeworkId, // 作业ID
                                    parentId: ids.parentId, // 所属章/节ID
                                    courseId: ids.courseId, // 课程ID
                                    question_comments,
                                    checkScores,
                                    answers,
                                    courseSemester: '', // 学期
                                    orderType: "desc", // 排序方式
                                    checkNext: false, // 是否批改下一份
                                    // order: null, // 排序字段
                                    comment: '', // 教师对整个作业的评论
                                    hasAttachments: {},
                                    teacherAttachment: {},
                                }
                                const res = await submitScoreCorrection(subId, data, params);
                                if (res.status !== 200) {
                                    throw new Error(res.message);
                                }
                            }
                        }
                        message.success('引用成功');
                        setSelectedRowKeys([]);
                    } catch (error: any) {
                        message.error(error.message || '引用失败');
                    } finally {
                        setLoading(false);
                    }
                }

                handleSubmit();
            }
        });
    }

    // 编辑分数
    const handleChangeInputNumber = (value: any, item: any, detailData: any) => {
        setDetailData({ ...detailData, score: value })
    }

    // 编辑评论
    const handleChangeComment = (e: any, item: any, detailData: any) => {
        const value = e.target.value;
        setDetailData({ ...detailData, comment: value })
    }

    const back = () => {
        setOpenDetail(false);
        setDetailData({});
        setDetailIsEdit(false)
    }

    const changeTab = (key: string) => {
        setActiveTabKey(key);
        setOpenDetail(false);
        setDetailData({})
    }

    // 切换tab保持勾选
    useEffect(() => {
        let selectKeys: string[] = []
        selectedRowKeys?.forEach((item: any) => {
            tableData?.forEach((ele: any) => {
                if (item?.split('-')[0] == ele.stuCode) {
                    selectKeys.push(ele.stuCode + '-' + ele.unconfirmId)
                }
            })
        })
        setSelectedRowKeys(selectKeys)
    }, [tableData])

    const toSaveEdit = (item: any, detailData: any) => {
        if (btnLoading) {
            message.info('请等待AI生成完成')
            return
        }
        setDetailIsEdit(!detailIsEdit)
        if (detailIsEdit) { // 代表保存
            const newTabList = tabList?.map((ele: any) => {
                if (item.id == ele.id) {
                    const studentAnswer = ele.studentAnswer.map((element: any) => {
                        if (element.stuCode == detailData.stuCode) {
                            return { ...element, score: detailData.score, comment: detailData.comment }
                        }
                        return element
                    })
                    return { ...ele, studentAnswer }
                }
                return ele
            })
            changeQuestionList(newTabList)
        }
    }

    return <div className="content-right">
        <Tabs activeKey={activeTabKey} onChange={changeTab}>
            {
                tabList?.map((item) => {
                    return <Tabs.TabPane tab={`第${item.index}题`} key={item.id}>
                        {
                            !openDetail ? <div className="item-wrap">
                                <div className="item">
                                    <div className="top">
                                        <div className="title-wrap">
                                            <div className="dot"></div>
                                            <div className="title"><span> 第{item?.index}题</span>（主观题{item?.score}分） </div>
                                        </div>
                                        <div className="content"> {item?.questions_content} </div>
                                    </div>
                                    {
                                        item?.questions_analysis && <div className="des">
                                            <div className="title-wrap">
                                                <div className="dot"></div>
                                                <span className="title">题目解析：</span>
                                            </div>
                                            <div className="anaysis">{item?.questions_analysis}</div>
                                        </div>
                                    }

                                    <div className="table-wrap">
                                        <Table
                                            rowSelection={{
                                                type: 'checkbox',
                                                selectedRowKeys,
                                                onChange: (selectedRowKeys: React.Key[]) => {
                                                    setSelectedRowKeys(selectedRowKeys);
                                                },
                                            }}
                                            columns={tableColumns}
                                            dataSource={tableData}
                                            pagination={false}
                                            rowKey={(record: any) => record.stuCode + '-' + record.unconfirmId}
                                        />
                                    </div>
                                </div>
                                <Button loading={loading} onClick={handleReference}>一键引用</Button>
                            </div> : <div className="item-detail">
                                <div className="item">
                                    <div className="back" onClick={back}>
                                        <img src={require("@/assets/imgs/teachingPlan/backItem.png")} />
                                        <span>返回</span>
                                    </div>

                                    <div className="top">
                                        <div className="title-wrap">
                                            <div className="dot"></div>
                                            <div className="title"><span> 第{item?.index}题</span>（主观题{item?.score}分） </div>
                                        </div>
                                        <div className="content"> {item?.questions_content} </div>
                                    </div>

                                    {
                                        item?.questions_analysis && <div className="des">
                                            <span className="title">题目解析：</span>
                                            <div className="anaysis">{item?.questions_analysis}</div>
                                        </div>
                                    }

                                    <div className="studentDetail">
                                        <div className="stuName detail">
                                            <div className="detail-left">答题者</div>
                                            <span>：</span>
                                            <div className="detail-right"><span className="name">学生名</span>{detailData?.stuName}</div>
                                        </div>
                                        <div className="answer detail">
                                            <div className="detail-left">解答</div>
                                            <span>：</span>
                                            <div className="detail-right">{detailData?.answer}</div>
                                        </div>
                                        <div className="score detail">
                                            <div className="detail-left">AI评分</div>
                                            <div className="detail-right">
                                                {detailIsEdit ? <InputNumber onChange={(e) => handleChangeInputNumber(e, item, detailData)} min={0} addonAfter='分' value={detailData?.score} /> :
                                                    (detailData?.isLoading ? <span style={{ fontSize: 12, color: '#6251FE' }}>AI批改中...</span> : <span>{detailData?.score} 分</span>)
                                                }
                                            </div>
                                        </div>
                                        <div className="comment detail">
                                            <div className="detail-left">AI评语</div>
                                            <div className="detail-right">
                                                {
                                                    detailData?.isLoading ? <span style={{ fontSize: 12, color: '#6251FE' }}>AI批改中...</span> : <Input.TextArea readOnly={!detailIsEdit} className={detailIsEdit ? 'showborder' : 'notShowborder'}
                                                        onChange={(e) => handleChangeComment(e, item, detailData)} value={detailData?.comment} />
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <Button onClick={() => toSaveEdit(item, detailData)}>{detailIsEdit ? '保存' : '编辑'}</Button>
                            </div>
                        }
                    </Tabs.TabPane>
                })
            }
        </Tabs>
    </div>
}

export default ContentRight;
