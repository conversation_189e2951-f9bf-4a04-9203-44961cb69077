// 自定义文件上传插件
tinymce.PluginManager.add('import_file', function(editor) {
  // 创建一个对话框
  var showDialog = function() {
    return editor.windowManager.open({
      title: '选择文件',
      body: {
        type: 'panel',
        items: [
          {
            type: 'urlinput',
            name: 'file',
            filetype: 'file',
            label: '选择文件',
          },
        ],
      },
      buttons: [
        {
          type: 'cancel',
          text: '取消',
        },
        {
          type: 'submit',
          text: '确认',
          primary: true,
        },
      ],
      onSubmit: function(api) {
        // 获取到的值受 file_picker_callback 影响， 此处应用是已经被 file_picker_callback 处理后的值
        const filePath = api?.getData()?.file?.value;
        const fileName = api?.getData()?.file?.meta?.text;
        editor.insertContent(
          `<a href="${filePath}" data-mce-href="${filePath}">${fileName}</a>`,
        );
        api.close();
      },
    });
  };

  // 添加按钮到工具栏
  editor.ui.registry.addButton('import_file', {
    icon: 'browse',
    tooltip: '导入文件',
    onAction: function() {
      showDialog();
    },
  });

  // 添加菜单项
  editor.ui.registry.addMenuItem('import_file', {
    icon: 'browse',
    // text: '上传',
    onAction: function() {
      showDialog();
    },
  });

  return {
    getMetadata: function() {
      return {
        name: '文件上传插件',
        url: 'https://example.com/fileupload',
      };
    },
  };
});
