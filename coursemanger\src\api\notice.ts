import HTTP from '@/api/index';

namespace NoticeService {
  /**
   * 获取公告列表
   * @param data
   */
  export const fetchNoticeList = (data: {
    courseId: string;
    isPublish?: boolean;
  }) =>
    HTTP('/learn/v1/placard/search', {
      method: 'GET',
      params: { ...data, publishStatus: 2 },
    })
      .then((res) => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch((error) => {
        console.error(error);
      });
  /**
   * 添加公告
   * @param data
   */
  export const addNotice = (data: Notice.Query) =>
    HTTP('/learn/v1/placard', {
      method: 'POST',
      data,
    })
      .then((res) => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch((error) => {
        console.error(error);
      });
  /**
   * 更新公告
   * @param data
   */
  export const updateNotice = (data: Notice.Query) =>
    HTTP('/learn/v1/placard/update', {
      method: 'POST',
      data,
    })
      .then((res) => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch((error) => {
        console.error(error);
      });
  /**
   * 删除公告
   * @param id
   */
  export const deleteNotice = (id: string) =>
    HTTP(`/learn/v1/placard/remove?id=${id}`, {
      method: 'POST',
    })
      .then((res) => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch((error) => {
        console.error(error);
      });
  /**
   * 发布公告
   * @param id
   */
  export const publishNotice = (id: string) =>
    HTTP(`/learn/v1/placard/publish?id=${id}`, {
      method: 'POST',
    })
      .then((res) => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch((error) => {
        console.error(error);
      });
  /**
   * 取消发布公告
   * @param id
   */
  export const unPublishNotice = (id: string) =>
    HTTP(`/learn/v1/placard/unpublish?id=${id}`, {
      method: 'POST',
    })
      .then((res) => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch((error) => {
        console.error(error);
      });
  export const updateLiveCourse = (data: any) => 
    HTTP(`/learn/v1/course/live/save`, {
      method: 'POST',
      data: JSON.stringify(data),
    })
      .then((res) => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch((error) => {
        console.error(error);
      });
  export const deleteLiveCourse = (courseId: string) => 
    HTTP(`/learn/v1/course/live/delete`, {
      method: 'POST',
      params: { courseId },
    })
      .then((res) => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch((error) => {
        console.error(error);
      });

  export const fetchLiveCourseList = (params: any) =>
    HTTP('/learn/v1/course/live/query/page', {
      method: 'GET',
      params,
    })
      .then((res) => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch((error) => {
        console.error(error);
      });
  export const fetchLiveCourseDetail = (courseId: string) =>
    HTTP('/learn/v1/course/live/info', {
      method: 'POST',
      params: { courseId },
    })
      .then((res) => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch((error) => {
        console.error(error);
      });
  export const exportLiveCourses = () =>
    HTTP('/learn/v1/course/live/download/info', {
      method: 'GET',
    })
      .then((res) => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch((error) => {
        console.error(error);
      });
}
export default NoticeService;
