import HTTP from './index';

// 获取微专业课程列表
export const getmicromajorCourseList = (data: any, params?: any) =>
    HTTP.get(`/learn/v1/teaching/course/get/courses/list?${data}`, {
        params,
      })
        .then(res => {
          if (res.status === 200) {
            return res.data;
          }
        })
        .catch(error => {
          console.error(error);
        });

// 获取微专业全部列表数据
export const getmicromajorALLlist = (data: any) =>
    HTTP(`/learn/v1/micro/profession/list`, {
      method: 'GET',
      params: data,
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        return error;
      });