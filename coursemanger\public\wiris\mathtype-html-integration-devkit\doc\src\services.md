This npm module uses services remotely hosted to render MathML data. However, it is strongly recommended to install these services in your backend. This will allow you, among other things, to customize the backend service and store locally the images generated by MathType.

MathType integration services are available for the following technologies: Java, PHP, .NET and Ruby on Rails. You can download the appropiate services from the [Wiris MathType store](https://store.wiris.com/en/products/downloads/mathtype/integrations).