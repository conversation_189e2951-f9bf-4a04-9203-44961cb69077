.common-header {
  margin-bottom: 10px;
}
.common-header .homework-header-style {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.common-header .title {
  display: inline-block;
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 5px;
  color: var(--primary-color);
}
.common-header .title.preview {
  cursor: pointer;
  text-decoration: underline;
}
.common-header .no-show-from-box {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.common-header .no-show-from-box .info {
  margin: 0;
}
.common-header .no-show-from-box .grade-time {
  margin-right: 0;
}
.common-header .info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 28px;
}
.common-header .from {
  flex: 1;
  color: #333;
  font-size: 14px;
  display: flex;
  gap: 40px;
  color: rgba(0, 0, 0, 0.5);
}
.common-header .from p {
  margin: 0 !important;
  word-wrap: break-word;
}
.common-header .grade-time {
  background: #F3F7FF;
  border-radius: 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  padding: 0 18px;
  color: #333;
}
.common-header .grade-time span {
  font-weight: 500;
  color: #3C3C3C;
}
@media screen and (max-width: 768px) {
  .common-header .from {
    color: #333;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 10px;
    color: rgba(0, 0, 0, 0.5);
    flex-wrap: wrap;
  }
  .common-header .grade-time {
    background: #F3F7FF;
    border-radius: 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    font-size: 16px;
    padding: 0 20px;
    color: #333;
  }
  .common-header .grade-time span {
    font-weight: 500;
    color: #3C3C3C;
  }
}
