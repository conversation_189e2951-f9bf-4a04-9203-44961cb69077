import HTTP from './index';

namespace statisticsApi {
  export function getTotalClass(courseId: String) {
    // /cvod/v1/statistics/62acad2f3be94d40bcdb5b06e3b80afa/students
    return HTTP.get(`/learn/v1/statistics/${courseId}/students`).then(res => {
      return res.data;
    });
  }
  export function getTotalTime(courseId: String) {
    return HTTP.get(`/learn/v1/statistics/${courseId}/learntime`).then(
      res => res.data,
    );
  }
  export function getStatus(courseId: String) {
    return HTTP.get(`/learn/v1/statistics/${courseId}/learningstatus`).then(
      res => res.data,
    );
  }
  // export function getLearnRate(courseId: String) {
  //     return HTTP.get(`/cvod/v1/statistics/${courseId}/monthlylearntime`).then(res => res.data)
  // }
  // /cvod/v1/statistics/12324/progressrate
  export function getLearnRate(courseId: String) {
    return HTTP.get(`/learn/v1/statistics/${courseId}/progressrate`).then(
      res => res.data,
    );
  }
  export function getLearnRank(courseId: String) {
    return HTTP.get(`/learn/v1/statistics/${courseId}/ranklearntime`).then(
      res => res.data,
    );
  }

  export function getacademic(data: any) {
    return HTTP.get(`/learn/v1/statistics/academic/statistics`, {
      params: data,
    }).then(res => res.data);
  }

  export function getstudent(data: any) {
    return HTTP.get(`/learn/v1/statistics/student/learning`, {
      params: data,
    }).then(res => res.data);
  }

  export function getresource(data: any) {
    return HTTP.get(`/learn/v1/statistics/resource/learning`, {
      params: data,
      timeout: 60000,
    }).then(res => res.data);
  }
  //上交资源学习情况
  export function getSjResource(data: any) {
    return HTTP.post(`/learn/m1/statistics/resource/learning`, data).then(res => res.data);
  }
  //上交资源学习情况导出
  export function SjResourceExport(data: any) {
    return HTTP.get(`/learn/m1/statistics/resource/learning/export`, {params: data,responseType: 'blob'}).then(res => res.data);
  }
  //上交 学情统计 资源学习情况详情列表
  export function getDetailsSjList(data: any) {
    return HTTP.post(`/learn/m1/statistics/resource/learning/detail`, data).then(res => res.data);
  }
  export function exportResources(data: any) {
    return HTTP.get(`/learn/v1/statistics/resource/learning/export`, {
      params: data,
      timeout: 60000,
      responseType: 'blob',
    }).then(res => res.data);
  }

  export function getResourceDetail(params: any) {
    return HTTP.get(`/learn/v1/statistics/resource/learning/detail`, {
      params,
    }).then(res => res.data);
  }

  export function getProgressDistribute(params: any) {
    return HTTP.get('/learn/v1/statistics/learning/progress/distributed', {
      params,
    }).then(res => res.data);
  }

  export function getCollegeDistribute(params: any) {
    return HTTP.get(
      '/learn/v1/statistics/learning/progress/distributed/college',
      {
        params,
      },
    ).then(res => res.data);
  }
  //获取个人学习进度分布
  export function getPersonDistribute(params: any) {
    return HTTP.get(
      '/learn/v1/statistics/learning/progress/distributed/student',
      {
        params,
      },
    ).then(res => res.data);
  }
  //查询资源学习情况统计
  export function getResourceStudyData(params: any) {
    return HTTP.get('/learn/v1/statistics/resource/learning/graphics', {
      params,
    }).then(res => res.data);
  }
  //查询个人资源资源学习情况-图形统计
  export function getPersonResourceData(params: any) {
    return HTTP.get(
      '/learn/v1/statistics/resource/learning/graphics/personal',
      { params },
    ).then(res => res.data);
  }
  export function getMicroAcademic(params: any) {
    return HTTP.get('/learn/v1/statistics/academic/statistics/micro', {
      params,
    }).then(res => res.data);
  }
  export function getMicroLearning(params: any) {
    return HTTP.get('/learn/v1/statistics/student/learning/micro', {
      params,
    }).then(res => res.data);
  }
  export function getMicroDistributed(params: any) {
    return HTTP.get('/learn/v1/statistics/micro/visit/distributed', {
      params,
    }).then(res => res.data);
  }
  export function exportMicro(params: any) {
    return HTTP.get('/learn/v1/statistics/student/learning/export/micro', {
      params,
    }).then(res => res.data);
  }
  export function exportResource(params: any) {
    return HTTP.get('/learn/v1/statistics/micro/resource/learning/export', {
      params,
    }).then(res => res.data);
  }
  export function getMicroResource(params: any) {
    return HTTP.get('/learn/v1/statistics/micro/resource/learning', {
      params,
    }).then(res => res.data);
  }
  export function exportStudyResource(params: any) {
    return HTTP.get('/learn/v1/statistics/resource/learning/detail/export', {
      params,
    }).then(res => res.data);
  }
  export function getHomeworkStat(params: any) {
    return HTTP.get('/exam-api/resource/homework/list', { params }).then(
      res => res.data,
    );
  }
  export function getResourceStat(params: any) {
    return HTTP.get('/learn/v1/statistics/student/resource/learning', {
      params,
    }).then(res => res.data);
  }

  //查询个人学生学习情况统计
  export function getPersonalStudyInfo(params: any) {
    return HTTP.get('/learn/v1/statistics/student/learning/personal', {
      params,
    }).then(res => res.data);
  }

  //获取个人学习进度分布
  export function getPersonalLearnProgress(params: any) {
    return HTTP.get(
      `/learn/v1/statistics/learning/progress/distributed/personal`,
      { params },
    ).then(res => res.data);
  }

  //获取作业提交率、得分率饼图
  export function getHomeworkScore(id: any) {
    return HTTP.get(`/exam-api/statistics/course/${id}`).then(res => res.data);
  }
  //获取课程作业提交人数
  export function getHomeworkSubmitData(params: any, courseId: any) {
    return HTTP.post(
      `/exam-api/statistics/course/lechery/${courseId}`,
      params,
    ).then(res => res.data);
  }
  //根据课程ID查询对应作业基本信息
  export function getHomeworkStatisticsData(params: any, courseId: any) {
    return HTTP.post(
      `/exam-api/statistics/course/map-info/${courseId}`,
      params,
    ).then(res => res.data);
  }
  //获取学生作业详情折线图数据
  export function getStudentHomeworkStatisticsData(params: any) {
    return HTTP.post(`/exam-api/statistics/course/lechery/detail`, params).then(
      res => res.data,
    );
  }
  // 查询知识点
  export function getKnowledgestatistics(params: any) {
    return HTTP.get(`/learn/map/resource/study/knowledge/learning`, {
      params,
    });
  }
  // 查询学生的知识点统计
  export function getStudentKnowledgestatistics(params: any) {
    return HTTP.get(
      `/learn/map/resource/study/current/course/knowledge/learning`,
      {
        params,
      },
    );
  }

  // 查询学生的掌握率和完成率
  export function getStudentKnowledgerate(params: any) {
    return HTTP.get(`/learn/map/resource/study/current/course/learning/rate`, {
      params,
    });
  }

  // 查询总掌握率和完成率
  export function getKnowledgerate() {
    return HTTP.get(`/learn/map/resource/study/node/rate`);
  }

  // 查询知识点统计饼图
  export function getKnowledgePie(params: any) {
    return HTTP.get(`/learn/map/resource/study/knowledge/learning/info`, {
      params,
    });
  }

  // 查询学员知识点学习情况
  export function getStudentKnowledgePie(params: any) {
    return HTTP.get(`/learn/map/resource/study/node/student/study/info`, {
      params,
    });
  }

  // 查询教师或学生的 单个知识点 完成率和掌握率
  export function getTeacherOrStudentRate(params: any) {
    return HTTP.get(
      `/learn/map/resource/study/current/node/individual/statistics/rate`,
      {
        params,
      },
    );
  }

  // 查询教师或学生的 所有知识点 完成率和掌握率
  export function getTeacherOrStudentAllRate(params: any) {
    return HTTP.get(
      `/learn/map/resource/study/all/node/individual/statistics/rate`,
      {
        params,
      },
    );
  }

  // 查询学生端知识点学习情况
  export function getStudentKnowledge(params: any) {
    return HTTP.get(
      `/learn/map/resource/study/all/node/individual/statistics`,
      {
        params,
      },
    );
  }
  // 查询学生评论
  export function reqComments(params: any) {
    return HTTP.get(`/learn/v1/evaluation`, {
      params,
    }).then((res: any) => {
      if (res.status === 200) {
        return res.data;
      } else {
        throw new Error();
      }
    });
  }
  export function reqCommentSum(courseId: string) {
    return HTTP.get(`/learn/v1/evaluation/${courseId}`).then((res: any) => {
      if (res.status === 200) {
        return res.data;
      } else {
        throw new Error();
      }
    });
  }
  export function deleteComment(courseId: string, ids: number[]) {
    return HTTP.post(`/learn/v1/evaluation/delete/${courseId}`, ids).then(
      (res: any) => {
        if (res.status === 200) {
          return res.data;
        } else {
          throw new Error();
        }
      },
    );
  }
  export function reqRunSum(courseId: string) {
    return HTTP.get(`/learn/v1/statistics/running/overview`, {
      params: { courseId },
    }).then((res: any) => {
      if (res.status === 200) {
        return res.data;
      } else {
        throw new Error();
      }
    });
  }
  export function reqRunHistogram(courseId: string) {
    return HTTP.get(`/learn/v1/statistics/running/semester/histogram`, {
      params: { courseId },
    }).then((res: any) => {
      if (res.status === 200) {
        return res.data;
      } else {
        throw new Error();
      }
    });
  }
  export function reqRunCollection(courseId: string) {
    return HTTP.get(`/learn/v1/learningsituation/count/size`, {
      params: { courseId },
    }).then((res: any) => {
      if (res.status === 200) {
        return res.data;
      } else {
        throw new Error();
      }
    });
  }

  // export function getOverView(params: any) {
  //   return HTTP.get(`/learn/m1/statistics/course/overview`, {
  //     params,
  //   });
  // }
  export function getOverView(data: { courseId: string,courseType: number,type: number }) {
    return HTTP.post(`/learn/m1/statistics/ai/course/situation/statistics`, data);
  }
  export function getDistributed(params: any) {
    return HTTP.get('/learn/m1/statistics/learning/progress/distributed', {
      params,
    }).then(res => res.data);
  }

  export function getCollegeDistributed(params: any) {
    return HTTP.get(
      '/learn/m1/statistics/learning/progress/distributed/college',
      {
        params,
      },
    ).then(res => res.data);
  }
  export function getPersonDistributed(params: any) {
    return HTTP.get(
      '/learn/m1/statistics/learning/progress/distributed/student',
      {
        params,
      },
    ).then(res => res.data);
  }

  export function getKnowledgeCompletion(params: any) {
    return HTTP.get(`/learn/m1/statistics/knowledge/learning/info`, {
      params,
    });
  }
  export function getstudentStudy(params: any) {
    return HTTP.get(`/learn/m1/statistics/student/learning`, {
      params,
    });
  }
  export function getKnowlegeStudy(params: any) {
    return HTTP.get(`/learn/m1/statistics/knowledge/learning`, {
      params,
    });
  }

  export function getLearningPersonal(params: any) {
    return HTTP.get(`/learn/m1/statistics/student/learning/personal`, {
      params,
    });
  }

  export function getCourseTips(params: any) {
    return HTTP.get(`/pyfa/v1/course`, {
      params,
    });
  }

  export function getPeopleList(params: any) {
    return HTTP.get(`/learn/m1/statistics/course/target/student/page`, {
      params,
    });
  }
  export function getStudentLearning(params: any) {
    return HTTP.get(`/learn/m1/statistics/knowledge/student/learning`, {
      params,
    });
  }
  export function getCourseCode(params: any) {
    return HTTP.get(`/learn/v1/teaching/course/get/course/details`, {
      params,
    });
  }

  // export function getMapId (params: any){
  //   return HTTP.post(`learn/m1/course/get/map?courseId=${params.courseId}&isShow=${params.isShow}&courseSemester=${params.courseSemester}`);
  // }

  export function getMapId(params: any) {
    return HTTP(`/learn/m1/course/get/map`, {
      method: 'POST',
      params,
    });
  }

  export function getPersonalOverview(params: any) {
    return HTTP.get(`/learn/map/resource/study/knowledge/personal/overview`, {
      params,
    });
  }
  export function getoutline(params: any) {
    return HTTP.get(`/learn/map/resource/study/knowledge/personal/outline`, {
      params,
    });
  }
  /** 各个目标达成度 */
  export function getGoalAchievementData(params: any) {
    return HTTP(
      `/learn/v1/associate/student/course/detail/${params.userCode}/${params.courseId}`,
      { method: 'POST', params: params.param },
    );
  }

  /** 学生目标的达成详情 */
  export function getGoalAchievemetDetailById(
    userCode: string,
    courseId: string,
    params: any,
  ) {
    return HTTP(
      `/learn/v1/associate/student/course/target/detail/${userCode}/${courseId}`,
      {
        method: 'POST',
        params,
      },
    );
  }

  /** 单个目标下得课程情况 */
  export function getCourseDetailByGoalId(courseId: string, params: any) {
    return HTTP(`/learn/v1/associate/student/course/target/list/${courseId}`, {
      method: 'POST',
      params,
    });
  }
  export function getCourseDetailBytotal(
    userCode: string,
    courseId: string,
    params: any,
  ) {
    return HTTP(
      `/learn/v1/associate/student/course/total/${userCode}/${courseId}`,
      {
        method: 'POST',
        params,
      },
    );
  }

  /**
   * 课程目标达成度人数分布
   */
  export function getCourseGoalDistribution(courseId: string, params: any) {
    return HTTP(`/learn/v1/associate/student/course/target/student/distribution/${courseId}`, {
      method: 'POST',
      params
    })
  }

  export interface IQueryTarget {
    /** 课程大纲id */
    courseCode?: string
    /**  */
    courseSemester?: number
    /** 适用专业code */
    applicableMajorCode?: string
    grade?: string
    trainingPlanId?: string
    searchType?: 1 | 2
    userCode?: string
  }
  /**
   * （正常课程）新版本 单个课程里面的目标达成度 （只展示子指标）
   */
  export function queryAchievementByTarget(courseId: string, params:IQueryTarget) {
    return HTTP(`/learn/v1/associate/graduate/course/target/list/${courseId}`, {
      method: 'POST',
      params
    })
  }
  /**
   * 新版本 普通课程里面 目标id查询 根据规则分组/最大值最小值完成数量
   */
  export function queryTargetDetailById(params: {courseCode?: string,courseId?: string, courseSemester?: number, targetId?: string, userCode?: string }) {
    return HTTP(`/learn/v1/associate/student/get/gruop/target/list`, {
      method: 'POST',
      params
    })
  }

  export interface  IQUeryHistogram{
    /** 所属章节id */
    chapterId: string
    /** 课程id */
    courseId: string
    /** 课程期数 默认1 */
    courseSemester?: number
    /** 排序方式 0：降序、1：升序（默认0） */
    sortBy?: number
    /** 排序字段：0：章节、1：知识点数量（默认0） */
    sortTarget?: number
  }
  /** 查询课程资源重难点-图形统计 */
  export function queryCourseHistogram(params: IQUeryHistogram) {
    return HTTP.get(`/learn/v1/statistics/resource/learning/histogram`, {params}).then((res: any) => {
      if (res.status === 200) {
        return res.data
      } else {
        throw new Error()
      }
    })
  }
  /** 查询课程资源重难点-左边数据 */
  export function queryCourseDifficultiesTotal(params: IQUeryHistogram) {
    return HTTP.get(`/learn/v1/statistics/resource/learning/total`, {params}).then((res: any) => {
      if (res.status === 200) {
        return res.data
      } else {
        throw new Error()
      }
    })
  }

  // 查询微专业的模块
  export function queryMicroModules(params: {courseId : string}) {
    return HTTP(`/learn/v1/teaching/course/get/microProfession/teacher/permission`, {
      method: 'GET',
      params
    })
  }

  // 微专业达成度统计接口
  export function queryMicroModuleStatics(data: { courseId: string,courseSemester: number, nodeId?: string }) {
    return HTTP(`/learn/m1/reachStatistics/course/module/reach`, {
      method: 'POST',
      data,
      timeout: 60000
    })
  }

  // 微专业按模块分析学生人数
  export function queryStudentAchieveByModule(data: {courseId: string,courseSemester: number, nodeId?: string}) {
    return HTTP(`/learn/m1/reachStatistics/course/module/stuReach`, {
      method: 'POST',
      data,
      timeout: 60000
    })
  }

  //微专业查询学生列表
  export function queryStudentListByMicro(params: {courseId: string,courseSemester: number,keyWord?: string, page: number, size: number  }) {
    return HTTP(`/learn/m1/reachStatistics/student/list`, {
      method: 'GET',
      params,
      timeout: 60000
    })
  }

  // 微专业按模块达成度分析
  export function queryReachByMicroMajorModule(data: {courseId: string, courseSemester: number, nodeId?: string, userCode?: string}) {
    return HTTP(`/learn/m1/reachStatistics/course/module/reach`, {
      method: 'POST',
      data,
      timeout: 60000
    })
  }

  //微专业-查询模块
  export function queryMicroMajorModuleList(params: {courseId : string, courseSemester : string}) {
    return HTTP(`/learn/course-grades/teaching/module/list`, {
      method: 'GET',
      params
    })
  }

  //微专业-作业统计数量
  export function queryMicroMajorHomeworkNums(params: {courseId : string,courseSemester?: number}) {
    return HTTP(`/exam-api/microSpecial/course/homeWork/nums`, {
      method: 'GET',
      params
    }).then(res => {
      if (res?.status == 200) {
        return res?.data
      }
    }).catch(e => {
      throw Error(e)
    })
  }

  //微专业-提交率、得分率
  export function queryMicroMajorScoreAndSub(id: string, params: {courseSemester: number}) {
    return HTTP(`/exam-api/statistics/course/${id}`, {
      method: 'GET',
      params
    }).then(res => {
      if (res?.status == 200) {
        return res?.data
      }
    }).catch(e => {
      throw Error(e)
    })
  }

}

export default statisticsApi;
