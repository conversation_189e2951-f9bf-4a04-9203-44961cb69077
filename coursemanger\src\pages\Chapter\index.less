.chapter_box {
  height: 100%;

  .template_info {
    height: 30px;
    text-align: right;
    ul {
      text-align: right;
    }
    a {
      text-decoration: underline;
    }
  }
}

.chapter {
  background-color: #fff;
  height: calc(100%);
  overflow-y: auto;
  padding: 30px 40px;
  &.has-tpl {
    height: calc(100% - 30px);
  }
  .ant-btn {
    padding: 4px 10px;
  }

  .select-box {
    display: flex;
    justify-content: space-between;

    > .select-item {
      margin-right: 70px;
    }
    .right-button {
      display: flex;
      align-items: center;
      a {
        margin-left: 10px;
        text-decoration: underline;
      }
      .anticon {
        font-size: 14px;
        margin-right: 6px;
      }
      .has-template-wrp {
        span {
          cursor: pointer;
          &:hover {
            color: var(--primary-color);
          }
        }
      }
    }
  }
  .switch-wrp {
    display: flex;
    align-items: center;
    margin-left: 38px;
    .ant-switch-checked .ant-switch-handle {
      left: calc(100% - 14px - 2px);
    }
    .ant-switch {
      height: 18px;
      min-width: 41px;
      width: 41px;
      .ant-switch-handle {
        height: 14px;
        width: 14px;
      }
    }
  }
  #tree-container {
    margin-top: 30px;
    .ant-tree {
      .ant-tree-treenode {
        padding-bottom: 0;
        position: relative;
        z-index: 1;
      }
      .ant-tree-draggable-icon {
        margin-top: 8px;
      }
      .ant-tree-treenode::before,
      .type-chapter::after,
      .type-section::after {
        content: '';
        z-index: 0;
        position: absolute;
        top: 2px;
        bottom: 0;
        left: 0;
        right: 0;
        // background-color: rgba(240, 242, 244, 0.3);
      }
      .type-chapter.ant-tree-treenode-switcher-open {
        margin-bottom: 46px;
      }
      .type-chapter::after,
      .type-section::after {
        z-index: -1;
        // background-color: rgba(240, 242, 244, 0.3);
      }
      .type-chapter::before {
        background: #F6F7FC;
      }
      .ant-tree-treenode-selected::before {
        background-color: var(--second-color);
        height: 36px;
      }
      .ant-tree-treenode:hover {
        .ant-tree-title {
          color: var(--primary-color);
        }
      }
      .type-chapter .ant-tree-switcher,
      .type-section .ant-tree-switcher {
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .ant-tree-node-content-wrapper:hover,
      .ant-tree-node-content-wrapper.ant-tree-node-selected {
        background-color: transparent;
      }
    }
  }

  .action_buttom {
    margin-left: 20px;
    // width: 50px;
    // height: 25px;
    color: #777777;
    // line-height: 18px;
    background: #f0f2f4;
    border: 1px solid #f0f2f4;

    span {
      font-size: 14px;
    }
  }

  .chapter-header,
  .section-header {
    display: flex;
    justify-content: space-between;

    .left {
      display: flex;
      align-items: center;
      line-height: 32px;
    }
  }

  .chapter-title {
    font-size: 16px;
    font-weight: bold;
    // margin-left: 50px;
  }

  .section-title {
    font-size: 15px;
    font-weight: bold;
    margin-left: 50px;
    // margin-left: 50px;
  }

  .ant-collapse
    > .ant-collapse-item
    > .ant-collapse-header
    .ant-collapse-arrow {
    top: 22px;
  }

  .ant-collapse-content > .ant-collapse-content-box {
    padding: 0px;
  }

  .ant-collapse-ghost.chapter-collapse > .ant-collapse-item {
    border-bottom: 1px solid #d2d2d2;
  }

  .course-list {
    padding-left: 16px;

    .course-item {
      height: 32px;
      line-height: 32px;
      font-size: 14px;
      display: flex;
      justify-content: space-between;
      padding-right: 16px;
      margin-top: 5px;

      .left {
        display: flex;
        align-items: center;
        line-height: 32px;

        .status_sign {
          margin-right: 117px;
        }
      }

      .course-checkbox {
        margin-right: 90px;
      }

      .course-title {
        margin: 0 12px;
        cursor: pointer;
      }
    }
  }

  .status_sign {
    margin-right: 20px;
    font-size: 12px;

    .status_item {
      display: inline-block;
      width: 47px;
      height: 23px;
      text-align: center;
      line-height: 23px;
      border-radius: 3px;
    }

    .gree {
      border: 1px solid #5cb531;
      color: #5cb531;
    }

    .bule {
      border: 1px solid #549cff;
      color: #549cff;
    }

    .grey {
      border: 1px solid #c7cbd1;
      color: #c7cbd1;
    }

    .switch {
      display: flex;
      flex-direction: column;
      align-items: center;

      .switch_item {
        line-height: 18px;
      }
    }
  }
}
.popover-content-left {
  .pop-item {
    text-align: left;
  }
}


