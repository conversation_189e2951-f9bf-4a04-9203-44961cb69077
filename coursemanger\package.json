{"private": true, "license": "MIT", "scripts": {"start": "npm-run-all -p start_actual watch_env", "watch_env": "tsx ./scripts/env.watcher.cts", "start_actual": "node theme && cross-env NODE_OPTIONS=--openssl-legacy-provider --max-old-space-size=6096 umi dev", "build:cvodweb": "cross-env NODE_OPTIONS=--openssl-legacy-provider COMPRESS=none BUILD_TYPE=cvodweb umi build", "build:publishinghouse": "cross-env NODE_OPTIONS=--openssl-legacy-provider COMPRESS=none BUILD_TYPE=publishinghouse umi build", "postinstall": "cross-env NODE_OPTIONS=--openssl-legacy-provider umi generate tmp", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "test": "umi-test", "test:coverage": "umi-test --coverage", "translate": "node ./translate", "analyze": "cross-env NODE_OPTIONS=--openssl-legacy-provider COMPRESS=none BUILD_TYPE=cvodweb ANALYZE=1 umi build"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,less,md,json}": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "dependencies": {"@ant-design/charts": "^1.2.14", "@ant-design/pro-components": "2.4.16", "@antv/hierarchy": "^0.6.11", "@antv/layout": "^0.3.23", "@antv/x6": "^1.34.1", "@antv/x6-react-components": "^1.1.15", "@antv/x6-react-shape": "^1.6.1", "@microsoft/fetch-event-source": "^2.0.1", "@onlyoffice/document-editor-react": "^1.1.0", "@types/echarts": "^4.9.7", "@types/sha1": "^1.1.3", "@umijs/plugin-esbuild": "^1.4.2", "@umijs/preset-react": "1.x", "@umijs/test": "^3.2.28", "@zumer/snapdom": "^1.8.0", "ahooks": "3.7.7", "antd": "4.24.16", "antd-img-crop": "^3.13.2", "antd-mobile": "^2.3.4", "antd-theme-generator": "^1.2.8", "array-move": "^4.0.0", "axios": "^0.21.0", "b64-to-blob": "^1.2.19", "crypto-js": "^4.1.1", "dompurify": "^3.1.6", "echarts": "^5.0.2", "echarts-for-react": "^3.0.1", "echarts-wordcloud": "^2.1.0", "file-loader": "^6.2.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "immer": "^10.0.2", "immutability-helper": "^3.1.1", "jquery": "^3.6.0", "jsplumb": "^2.15.6", "jszip": "^3.6.0", "konva": "^9.2.3", "lint-staged": "^10.0.7", "localforage": "^1.10.0", "mind-elixir": "^0.19.0", "pinch-zoom-js": "^2.3.4", "prettier": "^1.19.1", "pubsub-js": "^1.9.4", "qrcode": "^1.4.4", "qrcode.react": "^1.0.1", "qs": "^6.9.6", "react": "^16.14.0", "react-bus": "^3.0.0", "react-contextmenu-lite": "^1.0.9", "react-copy-to-clipboard": "^5.0.4", "react-cropper": "^2.1.8", "react-dom": "^16.12.0", "react-dropzone": "^11.3.0", "react-html-parser": "^2.0.2", "react-infinite-scroll-component": "^6.1.0", "react-infinite-scroller": "^1.2.4", "react-rnd": "^10.3.7", "react-sortable-hoc": "^2.0.0", "sha1": "^1.1.1", "swiper": "^11.1.4", "umi": "^3.5.17", "url-loader": "^4.1.1", "use-immer": "^0.9.0", "xgplayer": "^2.31.6", "xgplayer-flv": "^2.5.1", "xgplayer-hls": "^2.5.2", "xgplayer-mp4": "^2.0.3", "xss": "^1.0.11", "yorkie": "^2.0.0"}, "devDependencies": {"@types/lodash": "^4.14.168", "@types/pubsub-js": "^1.8.6", "@types/qrcode.react": "^1.0.1", "@types/qs": "^6.9.7", "cross-env": "^7.0.3", "dotenv": "^16.4.5", "npm-run-all": "^4.1.5", "tsx": "^4.19.2", "umi-webpack-bundle-analyzer": "^4.4.2"}}