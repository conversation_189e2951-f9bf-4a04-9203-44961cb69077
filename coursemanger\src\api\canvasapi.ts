import HTTP from './index';

// 查询我的个人资源
export function GetUserFolders() {
    return HTTP.get(`/canvas-lms-adapter/Graph/UserFolders`)
      .then(res => {
        if (res.status === 200) {
          return res;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }

//   查询我的课程资源 
export function GetUserCourses() {
    return HTTP.get(`/canvas-lms-adapter/Graph/UserCourses`)
      .then(res => {
        if (res.status === 200) {
          return res;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }

//   查询课程的下一级
export function GetCoursesFoldersChild(id:any) {
    return HTTP.get(`/canvas-lms-adapter/Graph/CoursesFolders?course_id=${id}`)
  }

//   获取文件夹下的文件
export function GetFoldersFiles(id:any) {
    return HTTP.get(`/canvas-lms-adapter/Graph/FolderFiles?folder_id=${id}`)
      .then(res => {
        if (res.status === 200) {
          return res;
        }
      })
      .catch(error => {
        console.error(error);
      });
}

// 获取文件夹下的子文件夹
export function GetFolderFolders(id:any) {
    return HTTP.get(`/canvas-lms-adapter/Graph/FolderFolders?folder_id=${id}`)
      .then(res => {
        if (res.status === 200) { 
            return res;
        }
      })
      .catch(error => {
        console.error(error);
      });
}


// 导入第三方资源
export function importThirdResource(data: any) {
  return HTTP.post('/rman/v1/upload/third-resource/import', data)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}