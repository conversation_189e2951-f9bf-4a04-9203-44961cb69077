import HTTP from './index';
const PREFIX = '/rman';
export namespace RmanService {
  export function voiceSelect(data: VoiceSelectBody) {
    return HTTP(`${PREFIX}/v1/smart/voice/select`, {
      method: 'post',
       data,
    }) as unknown as Promise<{data:{data:{
      data:VoiceSelectData[]
    }}}>
  }
  export type VoiceSelectBody =Partial< {
    contentId:    string;
    metadataType: string;
    inpoint:      number;
    outpoint:     number;
}>
export interface VoiceSelectData {
  type:     string;
  name:     string;
  metadata: Metadatum[];
}

export interface Metadatum {
  guid_:   string;
  _in:     number;
  text:    string;
  text_en: null;
  _out:    number;
  delete:  number;
}

}
