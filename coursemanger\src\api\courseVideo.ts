import HTTP from './index';

const reqVideo = (contentId: string) =>
  HTTP.get('/rman/v1/entity/video/entity', { params: { contentId } })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });

const saveVideo = (data: any) =>
  HTTP.post('/rman/v1/entity/import/video', data)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });

const publishVideo = (data: string[], status: 0 | 1) =>
  HTTP.post('/rman/v1/entity/publish/video', data, {
    params: { publishStatus: status },
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });

const reqVideos = (data: any) =>
  HTTP.post('/rman/v1/search/entity', data)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });

export default {
  reqVideo,
  saveVideo,
  publishVideo,
  reqVideos,
};
