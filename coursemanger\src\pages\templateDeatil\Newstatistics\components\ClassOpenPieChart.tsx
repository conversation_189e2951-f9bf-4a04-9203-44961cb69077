import React, { FC, useState, useEffect } from 'react';
import ReactEcharts from 'echarts-for-react';
import './PieChartTwo.less';
import useLocale from '@/hooks/useLocale';
import { color } from 'echarts';
import { TEST_ONEW } from '../testData';
type PointType = {
  name: string;
  value: number;
  finishNotExamine?: any;
  masterNotExamine?: any;
};
type ScatterLegend = '100%-71%' | '70%-31%' | '30%-1%' | '0%' | '免考核';

const ClassOpenPieChart = ({
  dataSource,
  leftActiveTab,
  height,
  chartsDes,
}: any) => {
  const [option, setOption] = useState<any>({});
  // const [dataVal, setDataVal] = useState<PointType[] | undefined>(undefined);
  const [legendActived, setLegendActived] = useState<Set<ScatterLegend>>(
    new Set(),
  );
  useEffect(() => {
    let PointData: PointType[] = [];
    // const { finishRateList, masterRateList, questionList } = dataSource;
    console.log('dataSourcedataSourcedataSourcedataSource', dataSource);
    if (leftActiveTab === 'finishRateList') {
      dataSource.forEach((el: any) => {
        console.log(el,'333')
        PointData.push({
          name: el.nodeName,
          value: Number(el.finishRate),
          finishNotExamine: el.finishNotExamine,
        });
      });
      // setDataVal(PointData);
    } else if (leftActiveTab == 'masterRateList') {
      dataSource.forEach((el: any) => {
        PointData.push({
          name: el.nodeName,
          value: Number(el.masterRate),
          masterNotExamine: el.masterNotExamine,
        });
      });
    }
    // else if (leftActiveTab == 'questionList') {
    //   questionList.forEach((el: any) => {
    //     PointData.push({ name: el.nodeName, value: Number(el.questionCount) });
    //   });
    // }
    const newOption = getOption(PointData);
    setOption(newOption);
  }, [dataSource, leftActiveTab, legendActived]);
  const getOption = (dataVal: PointType[]) => {
    // const total = dataSource.reduce((prev: any, curr: any) => (prev?.value || 0) + (curr?.value || 0));
    let data: any[] = [];
    if (dataVal && dataVal.length) {
      data = dataVal;
      // data = TEST_ONEW;
    }
    let total = data
      .map((el: { value: any }) => el.value)
      .reduce((a: any, b: any) => a + b, 0);

    let rate = total * 0.02;
    data = data.map((el: { value: number }, index: number) => {
      let start = 0;
      let end = 0;
      if (index == 0) {
        start = 0;
        end = el.value;
      } else {
        for (let i = 0; i <= index; i++) {
          if (i < index) {
            start += data[i].value;
          }
          end += data[i].value;
        }
      }
      return Object.assign(el, { start, end });
    });
    return {
      polar: {
        center: ['50%', '50%'],
      },
      tooltip: {
        show: true,
        trigger: 'item',
        formatter: function(params) {
          // params.value 是数值
          // params.name 是名称
          // 只对 scatter 类型加 %
          if (params.seriesType === 'scatter') {
            return `${params.seriesName}<br/>${params.data.name}：${params.data.value}%`;
          }
          // 其他类型按默认
          return params.name;
        }
      },
      angleAxis: {
        type: 'value',
        min: 0,
        max: total,
        interval: 1,
        axisLine: false,
        axisLabel: false,
        axisTick: false,
      },
      radiusAxis: {
        show: false,
      },
      animationDuration: 500,
      series: [
        ...data
          .map((el: any, index: any) => {
            console.log('点数据', el, legendActived);
            const v = el.value;
            const colorPick = [
              '#62C20E',
              '#00B6F8',
              '#FFAB15',
              '#ffffff',
              '#52C6D4',
            ];
            let color;
            if (
              (leftActiveTab === 'finishRateList' &&
                el.finishNotExamine === 1) ||
              (leftActiveTab === 'masterRateList' && el.masterNotExamine === 1)
            ) {
              if (
                (legendActived.has('100%-71%') && v <= 100 && v >= 71) ||
                (legendActived.has('70%-31%') && v <= 70 && v >= 31) ||
                (legendActived.has('30%-1%') && v <= 30 && v >= 1) ||
                (legendActived.has('0%') && v === 0)
              )
                return null;
              if (legendActived.has('100%-71%') && v <= 100 && v >= 71)
                color = colorPick[0];
              else if (legendActived.has('70%-31%') && v <= 70 && v >= 31)
                color = colorPick[1];
              else if (legendActived.has('30%-1%') && v <= 30 && v >= 1)
                color = colorPick[2];
              else if (legendActived.has('0%') && v === 0) color = colorPick[3];
            } else {
              if (legendActived.has('免考核')) return null;
              color = colorPick[4];
            }
            return {
              coordinateSystem: 'polar',
              name: 'scatter',
              type: 'scatter',
              data: [
                {
                  value: el.value,
                  name: el.name,
                  itemStyle: {
                    color,
                    ...(color === '#ffffff'
                      ? {
                          borderWidth: 1,
                          borderColor: '#a8a8a8',
                        }
                      : {}),
                  },
                },
              ],
            };
          })
          .filter(i => i),
        ,
        {
          name: '外圆盘',
          type: 'pie',
          radius: '90%',
          center: 'center',
          itemStyle: {
            color: '#e4eefa',
          },
          data: [0],
          animation: false,
          labelLine: false,
          tooltip: false,
          hoverAnimation: false,
          silent: true,
        },
        {
          name: '内圆盘',
          type: 'pie',
          radius: ['25%', '85%'],
          center: 'center',
          data: data,
          label: {
            color: '#000',
          },
          itemStyle: {
            color: '#fff',
            borderColor: '#e4eefa',
            borderWidth: 10,
          },
          silent: true,
          animation: false,
          tooltip: false,
          animationType: false,
          hoverAnimation: false,
        },
      ],
    };
  };
  const handleClick = (params: any) => {
    console.log(params);
    // onClick(params.name);
    const clickedName = params.name;
  };
  const handleLegend = (v: any) => {
    if (legendActived.has(v)) {
      setLegendActived(prevSet => {
        const newSet = new Set(prevSet);
        newSet.delete(v);
        return newSet;
      });
    } else {
      setLegendActived(prevSet => new Set(prevSet).add(v));
    }
  };
  return (
    <div className="study_pie_chart">
      {/* <span>课程笔记数目</span> */}
      <ReactEcharts
        style={{ height: height || '300px' }}
        option={option}
        onEvents={{ click: handleClick }}
        className="chart"
      />
      <div className="pie_title">
        {chartsDes && <div className="charts_des">{chartsDes}</div>}
        <div className="legend">
          <span onClick={() => handleLegend('100%-71%')}>
            <span
              className="dot"
              style={{
                backgroundColor: legendActived.has('100%-71%')
                  ? '#e6e6e6'
                  : '#62C20E',
              }}
            ></span>{' '}
            100%-71%
          </span>
          <span onClick={() => handleLegend('70%-31%')}>
            <span
              className="dot"
              style={{
                backgroundColor: legendActived.has('70%-31%')
                  ? '#e6e6e6'
                  : '#00B6F8',
              }}
            ></span>{' '}
            70%-31%
          </span>
          <span onClick={() => handleLegend('30%-1%')}>
            <span
              className="dot"
              style={{
                backgroundColor: legendActived.has('30%-1%')
                  ? '#e6e6e6'
                  : '#FFAB15',
              }}
            ></span>{' '}
            30%-1%
          </span>
          <span onClick={() => handleLegend('0%')}>
            <span
              className="dot"
              style={
                legendActived.has('0%')
                  ? { backgroundColor: '#e6e6e6' }
                  : { backgroundColor: '#fff', border: '1px solid #a8a8a8' }
              }
            ></span>{' '}
            0%
          </span>
          <span onClick={() => handleLegend('免考核')}>
            <span
              className="dot"
              style={{
                backgroundColor: legendActived.has('免考核')
                  ? '#e6e6e6'
                  : '#52C6D4',
              }}
            ></span>{' '}
            免考核
          </span>
        </div>
      </div>
    </div>
  );
};

export default ClassOpenPieChart;
