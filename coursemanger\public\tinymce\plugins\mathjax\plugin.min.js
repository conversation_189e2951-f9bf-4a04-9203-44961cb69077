tinymce.PluginManager.add('mathjax', function (t, e) {
  let n = t.getParam('mathjax'),
    a = n.className || 'math-tex',
    l = a + '-original',
    i = n.symbols || { start: '\\(', end: '\\)' },
    o = n.lib || null,
    r = n.configUrl || e + '/config.js';
  n.className && (r += '?class=' + n.className);
  let s = [r];
  o && s.push(o),
    t.on('init', function () {
      let e = t.getDoc().getElementsByTagName('script');
      for (let n = 0; n < s.length; n++) {
        let a = t.dom.uniqueId(),
          l = t.dom.create('script', {
            id: a,
            type: 'text/javascript',
            src: s[n],
          }),
          i = !1;
        for (let t = 0; t < e.length; t++)
          if (e[t].src == l.src || e[t].src == s[n]) {
            i = !0;
            break;
          }
        i || t.getDoc().getElementsByTagName('head')[0].appendChild(l);
      }
    }),
    t.on('GetContent', function (e) {
      let n = t.dom.create('div');
      n.innerHTML = e.content;
      let l = n.querySelectorAll('.' + a);
      for (let t = 0; t < l.length; t++) {
        let e = l[t].querySelectorAll('span');
        for (let t = 0; t < e.length; t++) e[t].remove();
        let n = l[t].getAttribute('data-latex');
        l[t].removeAttribute('contenteditable'),
          l[t].removeAttribute('style'),
          l[t].removeAttribute('data-latex'),
          (l[t].innerHTML = n);
      }
      e.content = n.innerHTML;
    });
  let c = function (e) {
    if (2 != e.childNodes.length) {
      e.setAttribute('contenteditable', !1), (e.style.cursor = 'pointer');
      let n = e.getAttribute('data-latex') || e.innerHTML;
      e.setAttribute('data-latex', n), (e.innerHTML = '');
      let a = t.dom.create('span');
      (a.innerHTML = n), a.classList.add(l), e.appendChild(a);
      let i = t.dom.create('span');
      i.classList.add('dummy'),
        (i.innerHTML = 'dummy'),
        i.setAttribute('hidden', 'hidden'),
        e.appendChild(i);
    }
  };
  t.on('BeforeSetContent', function (e) {
    let n = t.dom.create('div');
    n.innerHTML = e.content;
    let l = n.querySelectorAll('.' + a);
    for (let t = 0; t < l.length; t++) c(l[t]);
    e.content = n.innerHTML;
  }),
    t.on('SetContent', function (e) {
      t.getDoc().defaultView.MathJax &&
        t.getDoc().defaultView.MathJax.typesetPromise();
    }),
    t.on('Change', function (e) {
      if (
        ((elements = t.dom.getRoot().querySelectorAll('.' + a)),
        elements.length)
      ) {
        for (let t = 0; t < elements.length; t++) c(elements[t]);
        t.getDoc().defaultView.MathJax &&
          t.getDoc().defaultView.MathJax.typesetPromise();
      }
    }),
    t.ui.registry.addToggleButton('mathjax', {
      text: 'Latex',
      tooltip: 'Mathjax',
      onAction: function () {
        let e = t.selection.getNode(),
          n = void 0;
        e.classList.contains(a) && (n = e), d(n);
      },
      onSetup: function (e) {
        return t.selection.selectorChangedWithUnbind('.' + a, e.setActive)
          .unbind;
      },
    }),
    t.on('click', function (t) {
      let e = t.target.closest('.' + a);
      e && d(e);
    });
  let d = function (e) {
    let n = t.id + '_' + t.dom.uniqueId(),
      o = '';
    e &&
      ((latex_attribute = e.getAttribute('data-latex')),
      latex_attribute.length >= (i.start + i.end).length &&
        (o = latex_attribute.substr(
          i.start.length,
          latex_attribute.length - (i.start + i.end).length,
        ))),
      t.windowManager.open({
        title: 'Mathjax',
        width: 600,
        height: 300,
        body: {
          type: 'panel',
          items: [
            { type: 'textarea', name: 'title', label: 'LaTex' },
            {
              type: 'htmlpanel',
              html: '<div style="text-align:right"><a href="https://wikibooks.org/wiki/LaTeX/Mathematics" target="_blank" style="font-size:small">LaTex</a></div>',
            },
            {
              type: 'htmlpanel',
              html:
                '<iframe id="' +
                n +
                '" style="width: 100%; min-height: 50px;"></iframe>',
            },
          ],
        },
        buttons: [{ type: 'submit', text: 'OK' }],
        onSubmit: function (n) {
          let l = n.getData().title.trim();
          if (e) (e.innerHTML = ''), e.setAttribute('data-latex', h(l)), c(e);
          else {
            let e = t.getDoc().createElement('span');
            (e.innerHTML = h(l)),
              e.classList.add(a),
              c(e),
              t.insertContent(e.outerHTML);
          }
          t.getDoc().defaultView.MathJax.typesetPromise(), n.close();
        },
        onChange: function (t) {
          var e = t.getData().title.trim();
          e != o && (p(e, document.getElementById(n)), (o = e));
        },
        initialData: { title: o },
      });
    let r = document.getElementById(n),
      d = r.contentWindow || r.contentDocument.document || r.contentDocument,
      m = d.document,
      u = m.getElementsByTagName('head')[0],
      g = m.getElementsByTagName('body')[0],
      h = function (t, e) {
        return e || (e = i), e.start + ' ' + t + ' ' + e.end;
      },
      p = function (t) {
        let e = d.MathJax,
          n = g.querySelector('div');
        n ||
          ((n = m.createElement('div')), n.classList.add(l), g.appendChild(n)),
          (n.innerHTML = h(t, { start: '$$', end: '$$' })),
          e && e.typesetPromise();
      };
    p(o);
    for (let t = 0; t < s.length; t++) {
      let e = d.document.createElement('script');
      (e.src = s[t]),
        (e.type = 'text/javascript'),
        (e.async = !1),
        (e.charset = 'utf-8'),
        u.appendChild(e);
    }
  };
});
