import React from 'react';
import './index.less';
import icon1 from '../../assets/imgs/homePage2/icon1.png';
import icon2 from '../../assets/imgs/homePage2/icon2.png';
import icon3 from '../../assets/imgs/homePage2/icon3.png';
import icon4 from '../../assets/imgs/homePage2/icon4.png';
import bg from '../../assets/imgs/homePage2/bg2.png';
import {CUSTOMER_NPU} from "@/permission/moduleCfg";
import NPUHeader from "@/components/NPUHeader";
import Header from "@/components/Header";

const HomePage2 = () => {
  const modules = [
    {
      title: '资源采集',
      description: '模块文字介绍模块文字介绍模块文字介绍模块文字介绍模块文字介绍学习模块',
      icon: '📥',
      bg: icon1,
      backImg: bg,
      url: '/rman/#/basic/rmanCenterList'
    },
    {
      title: '资源编辑',
      description: '模块文字介绍模块文字介绍模块文字介绍模块文字介绍模块文字介绍学习模块',
      icon: '✏️',
      bg: icon4,
      backImg: bg,
      url: '/learn/workbench/#/courseEdit'
    },
    {
      title: '教学运行',
      description: '模块文字介绍模块文字介绍模块文字介绍模块文字介绍模块文字介绍学习模块',
      icon: '💻',
      bg: icon2,
      backImg: bg,
      url: '/learn/workbench/#/course'
    },
    {
      title: '教学管理',
      description: '模块文字介绍模块文字介绍模块文字介绍模块文字介绍模块文字介绍学习模块',
      icon: '📊',
      bg: icon3,
      backImg: bg,
      url: '/learn/workbench/#/teachManage'

    }
  ];

  return (
    <div className="app">
      <Header />
      <main className="app-main">
        <div className="modules-container">
          {modules.map((module, index) => (
            <a style={{color: '#000'}} href={module.url} key={index}>
            <div key={index} className="module-card" style={{ backgroundImage: `url(${module.backImg})` }}>
              <div className="module-icon-container">
                {/*<span className="module-icon">{module.icon}</span>*/}
                <img src={module.bg}  />
              </div>
              <div className="module-icon-bottom">
                <h2>{module.title}</h2>
                <p>{module.description}</p>
              </div>
            </div>
            </a>
          ))}
        </div>
      </main>
    </div>
  );
}

export default HomePage2;
