// @ts-nocheck
import { Component } from 'react';
import { ApplyPluginsType } from 'umi';
import dva from 'dva';
// @ts-ignore
import createLoading from 'D:/公司项目/云上川大/sc-learn-web/coursemanger/node_modules/dva-loading/dist/index.esm.js';
import { plugin, history } from '../core/umiExports';
import ModelConfig0 from 'D:/公司项目/云上川大/sc-learn-web/coursemanger/src/models/config.ts';
import ModelCourse1 from 'D:/公司项目/云上川大/sc-learn-web/coursemanger/src/models/course.ts';
import ModelCoursemap2 from 'D:/公司项目/云上川大/sc-learn-web/coursemanger/src/models/coursemap.ts';
import ModelDownload3 from 'D:/公司项目/云上川大/sc-learn-web/coursemanger/src/models/download.ts';
import ModelEditor4 from 'D:/公司项目/云上川大/sc-learn-web/coursemanger/src/models/editor.ts';
import ModelGlobal5 from 'D:/公司项目/云上川大/sc-learn-web/coursemanger/src/models/global.ts';
import ModelHomework6 from 'D:/公司项目/云上川大/sc-learn-web/coursemanger/src/models/homework.ts';
import ModelJurisdiction7 from 'D:/公司项目/云上川大/sc-learn-web/coursemanger/src/models/jurisdiction.ts';
import ModelMicroCourse8 from 'D:/公司项目/云上川大/sc-learn-web/coursemanger/src/models/microCourse.ts';
import ModelMoocCourse9 from 'D:/公司项目/云上川大/sc-learn-web/coursemanger/src/models/moocCourse.ts';
import ModelTest10 from 'D:/公司项目/云上川大/sc-learn-web/coursemanger/src/models/test.ts';
import ModelThemes11 from 'D:/公司项目/云上川大/sc-learn-web/coursemanger/src/models/themes.ts';
import ModelUpdata12 from 'D:/公司项目/云上川大/sc-learn-web/coursemanger/src/models/updata.ts';
import ModelUpload13 from 'D:/公司项目/云上川大/sc-learn-web/coursemanger/src/models/upload.ts';
import dvaImmer, { enableES5, enableAllPlugins } from 'D:/公司项目/云上川大/sc-learn-web/coursemanger/node_modules/dva-immer/dist/index.js';

let app:any = null;

export function _onCreate(options = {}) {
  const runtimeDva = plugin.applyPlugins({
    key: 'dva',
    type: ApplyPluginsType.modify,
    initialValue: {},
  });
  app = dva({
    history,
    
    ...(runtimeDva.config || {}),
    // @ts-ignore
    ...(typeof window !== 'undefined' && window.g_useSSR ? { initialState: window.g_initialProps } : {}),
    ...(options || {}),
  });
  
  app.use(createLoading());
  app.use(dvaImmer());
  (runtimeDva.plugins || []).forEach((plugin:any) => {
    app.use(plugin);
  });
  app.model({ namespace: 'config', ...ModelConfig0 });
app.model({ namespace: 'course', ...ModelCourse1 });
app.model({ namespace: 'coursemap', ...ModelCoursemap2 });
app.model({ namespace: 'download', ...ModelDownload3 });
app.model({ namespace: 'editor', ...ModelEditor4 });
app.model({ namespace: 'global', ...ModelGlobal5 });
app.model({ namespace: 'homework', ...ModelHomework6 });
app.model({ namespace: 'jurisdiction', ...ModelJurisdiction7 });
app.model({ namespace: 'microCourse', ...ModelMicroCourse8 });
app.model({ namespace: 'moocCourse', ...ModelMoocCourse9 });
app.model({ namespace: 'test', ...ModelTest10 });
app.model({ namespace: 'themes', ...ModelThemes11 });
app.model({ namespace: 'updata', ...ModelUpdata12 });
app.model({ namespace: 'upload', ...ModelUpload13 });
  return app;
}

export function getApp() {
  return app;
}

/**
 * whether browser env
 * 
 * @returns boolean
 */
function isBrowser(): boolean {
  return typeof window !== 'undefined' &&
  typeof window.document !== 'undefined' &&
  typeof window.document.createElement !== 'undefined'
}

export class _DvaContainer extends Component {
  constructor(props: any) {
    super(props);
    // run only in client, avoid override server _onCreate()
    if (isBrowser()) {
      _onCreate()
    }
  }

  componentWillUnmount() {
    let app = getApp();
    app._models.forEach((model:any) => {
      app.unmodel(model.namespace);
    });
    app._models = [];
    try {
      // 释放 app，for gc
      // immer 场景 app 是 read-only 的，这里 try catch 一下
      app = null;
    } catch(e) {
      console.error(e);
    }
  }

  render() {
    let app = getApp();
    app.router(() => this.props.children);
    return app.start()();
  }
}
