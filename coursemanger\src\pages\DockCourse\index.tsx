import { publishCourse } from '@/api/course';
import { deleteHomeworks } from '@/api/homework';
import {
  deleteCourse,
  fetchOtherCourse,
  fetchSemeter,
  getCourse_floor,
  offShelfThirdCourse,
  queryUserDetailInfo,
  updataDockCourse,
  showDockCourseMode,
  sortDockCourse,
} from '@/api/mooclist';
import CourseBlockForOther from '@/components/CourseBlock/CourseBlockForOther';
import DeleteProcess from '@/components/DeleteProcessModal';
import { IconFont } from '@/components/iconFont';
import MobileSearch from '@/components/SearchForm/mobileSearch';
import AddThirdcourseModal from '@/components/AddThirdcourseModal';
import useLocale from '@/hooks/useLocale';
import { IGlobal, IGlobalModelState } from '@/models/global';
import { COURSE_TYPE } from '@/permission/moduleCfg';
import { PlusCircleFilled, ReloadOutlined } from '@ant-design/icons';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { CSS } from '@dnd-kit/utilities';
import {
  Button,
  Checkbox,
  DatePicker,
  Empty,
  Form,
  Image,
  Input,
  Modal,
  Pagination,
  Popover,
  Select,
  Space,
  Table,
  Tooltip,
  message,
} from 'antd';
import moment from 'moment';
import React, {
  FC,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useDispatch, useLocation, useRouteMatch, useSelector } from 'umi';
import './index.less';
import { ColumnsType } from 'antd/es/table';
import { SyntheticListenerMap } from '@dnd-kit/core/dist/hooks/utilities';
import { DndContext, DragEndEvent } from '@dnd-kit/core';

const { Option } = Select;
const { RangePicker } = DatePicker;

const ellipsisSetting = (width: Number) => {
  return {
    style: {
      maxWidth: width,
      overflow: 'hidden',
      whiteSpace: 'nowrap',
      textOverflow: 'ellipsis',
      cursor: 'pointer',
    },
  };
};

interface RowContextProps {
  setActivatorNodeRef?: (element: HTMLElement | null) => void;
  listeners?: SyntheticListenerMap;
}
interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  'data-row-key': string;
}
const RowContext = React.createContext<RowContextProps>({});
const DragHandle: React.FC<{ title: string }> = ({ title }) => {
  const { setActivatorNodeRef, listeners } = useContext(RowContext);
  return (
    <Tooltip title={title}>
      <Button
        type="text"
        size="small"
        icon={<IconFont type="iconmove1" />}
        style={{ cursor: 'move' }}
        ref={setActivatorNodeRef}
        {...listeners}
      />
    </Tooltip>
  );
};
const Row: React.FC<RowProps> = props => {
  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: props['data-row-key'] });

  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Translate.toString(transform),
    transition,
    ...(isDragging ? { position: 'relative', zIndex: 9999 } : {}),
  };

  const contextValue = useMemo<RowContextProps>(
    () => ({ setActivatorNodeRef, listeners }),
    [setActivatorNodeRef, listeners],
  );

  return (
    <RowContext.Provider value={contextValue}>
      <tr {...props} ref={setNodeRef} style={style} {...attributes} />
    </RowContext.Provider>
  );
};
const pushList = [
  { label: '四川省平台', value: 1 },
  { label: '国家平台', value: 2 },
];

const DockCourseList: FC = () => {
  const { t } = useLocale();
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const [selectKey, setSelectKey] = useState<string[]>([]);
  const [dataSource, setDataSource] = useState<any>([]);
  const [tableLoading, setTableLoading] = useState(false);
  const [total, setTotal] = useState<number>(0);
  const [newSelectedRows, setNewSelectedRows] = useState<any>([]);
  const [releaseDis, setReleaseDis] = useState(true);
  const [offShelfDis, setOffShelfDis] = useState(true);
  const [oneOrBatch, setOneOrBatch] = useState<boolean>(true);
  const [operationData, setOperationData] = useState<any>();
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [deleteProcessModalVisible, setDeleteProcessModalVisible] = useState(
    false,
  );
  const [processId, setProcessId] = useState<string>('');
  const [releaseOrNot, setReleaseOrNot] = useState<boolean>(false);
  const [release, setRelease] = useState<boolean>(false);
  const [offlineCourse, setOfflineCourse] = useState<any[]>([]);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const offlineId = useRef('');
  const [contentId, setContentId] = useState<string>('');
  const [indeterminate, setIndeterminate] = useState<boolean>(false);
  const [checkAll, setCheckAll] = useState<boolean>(false);
  const [query, setQuery] = useState({
    pageIndex: 1,
    pageSize: 24,
    courseType: 2,
  });
  // const history = useHistory();
  const { mobileFlag } = useSelector<{ config: any }, any>(
    ({ config }) => config,
  );

  const [operatMenuVisible, setOpreatMenuVisible] = useState<boolean>(false);
  const { userInfo } = useSelector<any, IGlobal>(state => state.global);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const isSuper =
    userInfo.roles
      ?.map((item: any) => item.roleCode)
      ?.includes('r_sys_manager') || //是否是系统管理员
    userInfo.roles
      ?.map((item: any) => item.roleCode)
      ?.includes('r_course_manager') || //是否是课程管理员
    userInfo.roles
      ?.map((item: any) => item.roleCode)
      ?.includes('r_second_manager') || //第二权限
    userInfo.roles?.map((item: any) => item.roleCode)?.includes('admin_S1'); // admin
  const [showSwitchMode, setShowSwitchMode] = useState<boolean>(false);
  const [isCardMode, setIsCardMode] = useState<boolean>(true);

  useEffect(() => {
    // 初始化
    if (localStorage.getItem('docklist_size')) {
      setQuery({
        ...query,
        pageSize: Number(localStorage.getItem('docklist_size')),
      });
    }
    showDockCourseMode().then((res: any) => {
      if (res.status === 200) {
        setShowSwitchMode(res.data?.data && !mobileFlag && isSuper);
      }
    });
  }, []);

  const [createForm] = Form.useForm();
  const { parameterConfig } = useSelector<
    { global: IGlobalModelState },
    IGlobalModelState
  >(state => state.global);
  // table column
  const columns: ColumnsType<any> = [
    {
      title: '',
      dataIndex: 'check',
      key: 'check',
      align: 'center',
      width: 50,
      fixed: 'left',
      render: (_text: any, record: any) => (
        <Checkbox value={record.contentId_} />
      ),
    },
    {
      title: t('课程封面'),
      align: 'center',
      width: 120,
      dataIndex: 'cover',
      key: 'cover',
      render: (text: any) => <Image width={'100%'} src={text} />,
    },
    {
      title: t('课程名称'),
      dataIndex: 'name',
      align: 'center',
      width: 270,
      key: 'name',
      ellipsis: {
        showTitle: true,
      },
      onCell: () => ellipsisSetting(270),
    },
    {
      title: t('教师'),
      dataIndex: 'teacher_names',
      align: 'center',
      key: 'teacher_names',
      width: 180,
      ellipsis: {
        showTitle: true,
      },
      onCell: () => ellipsisSetting(180),
    },
    {
      title: t('开课时间'),
      dataIndex: '',
      align: 'center',
      width: 180,
      key: 'start_time',
      ellipsis: {
        showTitle: false,
      },
      onCell: () => ellipsisSetting(180),
      render: (text: number, record: any): React.ReactNode => {
        return (
          <Tooltip
            placement="topLeft"
            title={
              moment(record.start_time).format('YYYY年MM月DD日') +
              ' - ' +
              moment(record.end_time).format('YYYY年MM月DD日')
            }
            mouseEnterDelay={0.8}
          >
            {moment(record.start_time).format('YYYY年MM月DD日') +
              ' - ' +
              moment(record.end_time).format('YYYY年MM月DD日')}
          </Tooltip>
        );
      },
    },
    {
      title: t('发布状态'),
      dataIndex: 'publishStatus',
      key: 'publishStatus',
      width: 150,
      align: 'center',
      render: value => {
        const flag = value === 0;
        // '未发布' : '已发布';
        return (
          <span style={{ color: flag ? '#B5B5B5' : 'rgba(0, 0, 0, 0.85)' }}>
            {flag ? t('未发布') : value === 2 ? t('待审核') : t('已发布')}
          </span>
        );
      },
    },
    {
      title: t('课程状态'),
      dataIndex: 'courseStatus',
      width: 100,
      key: 'courseStatus',
      align: 'center',
      render: text => {
        const flag = text === 1;
        return (
          <span
            style={{
              color: flag ? '#FF913D' : text === 2 ? '#6BBF51' : '#B5B5B5',
            }}
          >
            {flag ? t('未开始') : text === 2 ? t('进行中') : t('已结束')}
          </span>
        );
      },
    },
    {
      title: t('创建人'),
      dataIndex: 'createUser_',
      align: 'center',
      width: 180,
      render: (text: Array<{ code: string; value: string }>) => {
        return text.length > 0 ? text[0].value : '';
      },
    },
    {
      title: t('操作'),
      dataIndex: 'action',
      align: 'center',
      width: 200,
      key: 'action',
      fixed: 'right',
      render: (text, record: any) => {
        return (
          <>
            <DragHandle title={t('排序')} />
            <Tooltip title={t('置顶')}>
              <Button
                type="text"
                icon={<IconFont type="icontop" />}
                onClick={() => toTop([record.key])}
              />
            </Tooltip>
            <Tooltip title={t('预览')}>
              <Button
                type="text"
                icon={<IconFont type="iconviews" />}
                onClick={() => {
                  if (record.courseType === 4) {
                    window.open(
                      `#/mapv4?sm=1&type=${COURSE_TYPE[record.courseType]}&id=${
                        record.contentId_
                      }&preview=1`,
                    );
                  } else {
                    window.open(
                      `/learn/course/preview/${
                        COURSE_TYPE[record.courseType]
                      }/${record.contentId_}?preview=1&show=1`,
                    );
                  }
                }}
              />
            </Tooltip>
          </>
        );
      },
    },
  ];

  // 置顶
  const defaultSortGap = 8;
  const toTop = async (ids: string[]) => {
    try {
      setTableLoading(true);
      let courseType = params.type;
      const response = await fetchOtherCourse(courseType, {
        pageIndex: 1,
        pageSize: 1,
        teacherName: userInfo.nickName,
      });
      if (response.status !== 200) {
        setTableLoading(false);
        return message.error('置顶失败！');
      }
      const sort = Math.min(
        response.data?.results?.[0]?.courseInsideSort || 0,
        0,
      );
      let offset = 1;
      const req = ids.map(item => ({
        contentId: item,
        sort: sort - offset++ * defaultSortGap,
      }));
      const res = await sortDockCourse(req);
      if (res.status === 200) {
        fetchDataList();
        setTableLoading(false);
        return message.success('置顶成功！');
      }
      setTableLoading(false);
      return message.error('置顶失败！');
    } catch (err) {
      message.error('置顶失败！');
      throw err;
    }
  };
  const handleToTop = () => {
    toTop(selectKey);
  };

  // 排序
  const onDragEnd = async ({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id) {
      setTableLoading(true);
      setDataSource((prevState: any) => {
        const activeIndex = prevState.findIndex(
          (record: any) => record.contentId_ === active?.id,
        );
        const overIndex = prevState.findIndex(
          (record: any) => record.contentId_ === over?.id,
        );
        return arrayMove(prevState, activeIndex, overIndex);
      });
      const activeIndex = dataSource.findIndex(
        (item: any) => active.id === item.contentId_,
      );
      const overIndex = dataSource.findIndex(
        (item: any) => over?.id === item.contentId_,
      );
      const sourceId = dataSource[activeIndex].contentId_;
      const isMoveUp = activeIndex > overIndex;

      const isMoveToHead = () => overIndex === 0 && query.pageIndex === 1;
      const isMoveToEnd = () =>
        query.pageIndex * query.pageSize + overIndex + 1 === total;

      try {
        let minSort;
        let maxSort;
        // 得出minSort和maxSort
        if (isMoveUp) {
          maxSort = dataSource[overIndex].courseInsideSort;
          // 单页的上边界
          if (overIndex === 0 && query.pageIndex !== 1) {
            let courseType = params.type;
            const response = await fetchOtherCourse(courseType, {
              pageIndex: query.pageIndex - 1,
              pageSize: query.pageSize,
              teacherName: userInfo.nickName,
            });
            if (response.status !== 200) {
              setTableLoading(false);
              return message.error('排序失败！');
            }
            const tmp = response.data?.results || [];
            minSort = tmp[tmp.length - 1]?.courseInsideSort;
          } else {
            minSort = dataSource[overIndex - 1]?.courseInsideSort;
          }
        } else {
          minSort = dataSource[overIndex].courseInsideSort;
          // 排序基于min。如果min未定义，直接跳出
          if (minSort !== undefined) {
            // 单页的下边界
            if (overIndex !== dataSource.length - 1) {
              maxSort = dataSource[overIndex + 1].courseInsideSort;
            } else if (!isMoveToEnd()) {
              let courseType = params.type;
              const response = await fetchOtherCourse(courseType, {
                pageIndex: query.pageIndex + 1,
                pageSize: query.pageSize,
                teacherName: userInfo.nickName,
              });
              if (response.status !== 200) {
                setTableLoading(false);
                return message.error('排序失败！');
              }
              maxSort = response.data?.results?.[0]?.courseInsideSort;
            }
          }
        }

        let reqBodys;
        // 全局数据上边界
        if (isMoveToHead()) {
          reqBodys = [
            {
              contentId: sourceId,
              sort: maxSort !== undefined ? maxSort - defaultSortGap : 0,
            },
          ];
        }
        // 全局数据下边界
        else if (isMoveToEnd() && minSort !== undefined) {
          reqBodys = [
            {
              contentId: sourceId,
              sort: minSort + defaultSortGap,
            },
          ];
        }
        // 有minSort排序值情况
        else if (minSort !== undefined) {
          reqBodys = [
            {
              contentId: sourceId,
              sort:
                maxSort !== undefined
                  ? minSort + (maxSort - minSort) / 2
                  : minSort + defaultSortGap,
            },
          ];
        }
        // 没有minSort排序值的情况
        else {
          const courseType = params.type;
          const len =
            (query.pageIndex - 1) * query.pageSize +
            overIndex +
            (isMoveUp ? 0 : 1);
          const response = await fetchOtherCourse(courseType, {
            pageIndex: 1,
            pageSize: len,
            teacherName: userInfo.nickName,
          });
          if (response.status !== 200) {
            setTableLoading(false);
            return message.error('排序失败！');
          }
          const queue = [...response.data.results, { contentId_: sourceId }];
          let offset = 0;
          reqBodys = queue.map((item: any) => ({
            contentId: item.contentId_,
            sort: defaultSortGap * offset++,
          }));
        }

        const res = await sortDockCourse(reqBodys);
        if (res.status === 200) {
          fetchDataList();
          setTableLoading(false);
          return message.success('排序成功！');
        }
        setTableLoading(false);
        return message.error('排序失败！');
      } catch (err) {
        message.error('排序失败！');
        throw err;
      }
    }
  };

  const onSearch = (value: any) => {
    setQuery({
      ...query,
      ...value,
      startTime: value.startTime ? value.startTime[0].format('x') : undefined,
      endTime: value.startTime ? value.startTime[1].format('x') : undefined,
    });
  };
  const onReset = () => {
    form.resetFields();
    setQuery({
      pageIndex: 1,
      pageSize: query.pageSize,
      courseType: 3,
    });
  };
  const { params }: any = useRouteMatch();
  useEffect(() => {
    fetchDataList();
  }, [params.type, query]);
  useEffect(() => {
    setIsCardMode(true);
  }, [params.type]);
  const location = useLocation();
  // 使用useRef保存原始标题，防止被路由配置覆盖
  const originalTitle = useRef(document.title);

  useEffect(() => {
    // 保存原始标题
    if (!originalTitle.current) {
      originalTitle.current = document.title;
    }

    // 从localStorage获取自定义标题
    const storedTitle = JSON.parse(
      window.sessionStorage.getItem('title') || '""',
    );

    if (storedTitle) {
      // 使用setTimeout确保我们的标题设置在路由系统之后执行
      setTimeout(() => {
        document.title = storedTitle;
      }, 0);
    }

    // 创建MutationObserver监听title变化
    const titleObserver = new MutationObserver(() => {
      const currentTitle = document.title;
      const customTitle = JSON.parse(
        window.sessionStorage.getItem('title') || '""',
      );

      // 如果当前标题不是我们想要的自定义标题，则重新设置
      if (customTitle && currentTitle !== customTitle) {
        document.title = customTitle;
      }
    });

    // 监听document.title的变化
    titleObserver.observe(document.querySelector('title'), { childList: true });

    return () => {
      titleObserver.disconnect();
      // 恢复原始标题
      if (originalTitle.current) {
        document.title = originalTitle.current;
      }
    };
  }, [location.pathname]);

  const fetchDataList = () => {
    setTableLoading(true);
    // const pathname = history.location.pathname;
    let courseType = params.type;
    // if (pathname.includes("/dockcourse")) {
    //   courseType = "爱课堂";
    // } else if (pathname.includes("/rainCourse")) {
    //   courseType = "雨课堂";
    // } else if (pathname.includes("/superstarCourse")) {
    //   courseType = "超星";
    // } else if (pathname.includes("/schoolMooc")) {
    //   courseType = "中国大学MOOC";
    // } else if (pathname.includes("/pmphmoocCourse")) {
    //   courseType = "人卫慕课";
    // } else if (pathname.includes("/umoocCourse")) {
    //   courseType = "优慕课";
    // } else if (pathname.includes("/zhihuishuCourse")) {
    //   courseType = "智慧树";
    // } else if (pathname.includes("/schoolOnline")) {
    //   courseType = "学堂在线";
    // } else if (pathname.includes("/silverLearning")) {
    //   courseType = "学银在线";
    // }
    fetchOtherCourse(courseType, { ...query, teacherName: userInfo.nickName })
      .then(res => {
        dispatch({
          type: 'config/changeShowLoading',
          payload: {
            value: false,
          },
        });
        setTableLoading(false);
        console.log(res);
        if (res.message === 'OK') {
          const { results, total } = res.data;
          setTotal(total);
          const data = results.map((item: any) => {
            let {
              name,
              contentId_,
              subject,
              end_time,
              start_time,
              publishStatus,
              teacher,
              publishTime,
              createDate_,
            } = item;
            let subjectName: string[] = [];
            let teacherName: string[] = [];
            if (subject) {
              subject.forEach((element: any) => {
                subjectName.push(element.value);
              });
            }
            if (teacher && teacher instanceof Array) {
              teacher.forEach((element: any) => {
                teacherName.push(element.value);
              });
            } else if (teacher) {
              teacherName.push(teacher);
            }
            return {
              ...item,
              name,
              key: contentId_,
              subject: subjectName && subjectName.join(),
              opentime: end_time
                ? `${moment(start_time).format('YYYY-MM-DD')}undefined${moment(
                    end_time,
                  ).format('YYYY-MM-DD')}`
                : '',
              publishStatus,
              teacher: teacherName && teacherName.join(),
              updatetime: publishTime
                ? moment(publishTime).format('YYYY-MM-DD HH:mm:ss')
                : createDate_,
            };
          });
          setDataSource(data);
        }
      })
      .catch(() => setTableLoading(false));
  };
  useEffect(() => {
    if (createModalVisible && offlineCourse.length === 0) {
      queryUserDetailInfo().then(res => {
        console.log(res);
        //查询当前学期
        fetchSemeter().then(ress => {
          console.log(ress);
          getCourse_floor(
            res.extendMessage.user_code === 'admin' ||
              res.extendMessage.user_code === 'sys'
              ? {
                  page: 1,
                  size: 50,
                  course_form: t('班级课'),
                  semester: ress.extendMessage.name,
                }
              : {
                  page: 1,
                  size: 50,
                  course_form: t('班级课'),
                  teacher: res.extendMessage.user_code,
                  semester: ress.extendMessage.name,
                  // teacher:'zj2018326050004'
                },
          ).then(res => {
            if (res.error_code === 'cloud_sc.0000.0000' && res.extend_message) {
              setOfflineCourse(res.extend_message.results);
            }
          });
        });
      });
    }
  }, [createModalVisible]);

  useEffect(() => {
    let offshelf = newSelectedRows.some((item: any) => {
      return item.publishStatus === 0;
    });
    setOffShelfDis(offshelf);
    let release = newSelectedRows.some((item: any) => {
      return item.publishStatus !== 0;
    });
    setReleaseDis(release);
  }, [newSelectedRows]);

  //记住该页码大小
  const pageChange = (pageIndex: any, pageSize: any) => {
    setQuery({ ...query, pageIndex, pageSize });
    localStorage.setItem('docklist_size', pageSize);
  };
  //爱课堂跳转
  const openDockCourse = () => {
    // window.open('')
  };
  const handleOffCourse = () => {
    if (!oneOrBatch) {
      const params = {
        contentId: operationData.contentId_,
        publishStatus: releaseOrNot ? 1 : 0,
      };
      offShelfThirdCourse(params).then(res => {
        if (res.errorCode === 'success') {
          // fetchDataList();
          const data = dataSource.map((item: any) => {
            if (item.contentId_ === operationData.contentId_) {
              return {
                ...item,
                publishStatus: releaseOrNot ? 1 : 0,
              };
            } else {
              return item;
            }
          });
          setOperationData(null);
          setDataSource(data);
          setRelease(false);
          message.success(
            `${t('课程')}${releaseOrNot ? t('发布') : t('下架')}${t('成功')}`,
          );
        } else {
          message.error(
            `${t('课程')}${releaseOrNot ? t('发布') : t('下架')}${t('失败')}`,
          );
        }
      });
    } else if (selectKey.length > 0) {
      publishCourse(selectKey, releaseOrNot ? 1 : 0).then(res => {
        if (res.data?.status === 200) {
          const data = dataSource.map((item: any) =>
            selectKey.includes(item.contentId_)
              ? { ...item, publishStatus: releaseOrNot ? 1 : 0 }
              : item,
          );
          setSelectKey([]);
          setNewSelectedRows([]);
          setDataSource(data);
          setRelease(false);
          message.success(
            `${t('课程')}${releaseOrNot ? t('发布') : t('下架')}${t('成功')}`,
          );
        } else {
          message.error(
            res.data?.message ||
              `${t('课程')}${releaseOrNot ? t('发布') : t('下架')}${t('失败')}`,
          );
        }
      });
    }
  };
  const batchDelete = () => {
    if (selectKey.length) {
      setOneOrBatch(false);
      setIsDeleteModalVisible(true);
    } else {
      message.info(t('请选择课程'));
    }
  };
  const handleDeleteOk = () => {
    setDeleteLoading(true);
    let param: any = [];
    if (oneOrBatch) {
      param.push(operationData.key);
    } else {
      param = [...selectKey];
    }
    console.log(
      'xxxxxxxxxxxxxxxxxxxxx',
      param,
      oneOrBatch,
      operationData,
      selectKey,
    );
    deleteCourse(param).then(res => {
      if (res && res.message === 'OK') {
        deleteHomeworks(param);
        setProcessId(res.data);
        setDeleteProcessModalVisible(true);
      } else {
        message.error(t('删除失败'));
      }
      // setTimeout(() => getlist(), 500)
      setSelectKey([]);
      setDeleteLoading(false);
      setIndeterminate(false);
      setIsDeleteModalVisible(false);
    });
  };

  const onCheckGroupChange = (check: any[]) => {
    setSelectKey(check);
    setNewSelectedRows(
      [...dataSource].filter((d: any) => check.includes(d.contentId_)),
    );
    // console.log([...dataSource].filter((d: any) => check.includes(d.contentId_)).filter((d:any)=>d.publishStatus == 1))
    setIndeterminate(!!check.length && check.length < dataSource.length);
    setCheckAll(check.length === dataSource.length);
  };
  const onCheckAllChange = (e: any) => {
    setSelectKey(
      e.target.checked ? dataSource.map((item: any) => item.contentId_) : [],
    );
    setNewSelectedRows([...dataSource]);
    setIndeterminate(false);
    setCheckAll(e.target.checked);
    setReleaseDis(false);
  };
  //按钮列表
  let btn_list: any = [];
  btn_list.push({
    title: t('发布'),
    disabled: releaseDis || !selectKey.length,
    func: () => {
      setOneOrBatch(true);
      setReleaseOrNot(true);
      setRelease(true);
    },
    dom: (
      <Button
        type="text"
        ghost
        icon={<IconFont type="iconrelease" />}
        onClick={() => {
          setOneOrBatch(true);
          setReleaseOrNot(true);
          setRelease(true);
        }}
        disabled={releaseDis || !selectKey.length}
      >
        {t('发布')}
      </Button>
    ),
  });
  btn_list.push({
    title: t('下架'),
    disabled: offShelfDis || !selectKey.length,
    func: () => {
      setOneOrBatch(true);
      setReleaseOrNot(false);
      setRelease(true);
    },
    dom: (
      <Button
        type="text"
        ghost
        icon={<IconFont type="iconoffShelf" />}
        onClick={() => {
          setOneOrBatch(true);
          setReleaseOrNot(false);
          setRelease(true);
        }}
        disabled={offShelfDis || !selectKey.length}
      >
        {t('下架')}
      </Button>
    ),
  });
  btn_list.push({
    title: t('删除'),
    disabled: releaseDis || !selectKey.length,
    func: () => batchDelete,
    dom: (
      <Button
        type="text"
        ghost
        onClick={batchDelete}
        icon={<IconFont type="icondelete" />}
        disabled={releaseDis || !selectKey.length}
      >
        {t('删除')}
      </Button>
    ),
  });

  // 处理表单提交
  const handleSubmit = async (values: MicroCourse.FormValues & { isAdd: boolean }) => {
    try {
      let courseType = params.type;
      const response = await fetchOtherCourse(courseType, {
        pageIndex: 1,
        pageSize: 1,
        teacherName: userInfo.nickName,
      });
      const sort = Math.min(
        response.data?.results?.[0]?.courseInsideSort || 0,
        0,
      );
      const data = {
        ...values,
        courseInsideSort: sort - defaultSortGap,
        platform_name: params.type,
        contentId: !values.isAdd ? operationData.contentId_ : null,
      };
      const res = await updataDockCourse(data);
      if (res?.status === 200) {
        fetchDataList();
        return res.data;
      } else {
        throw new Error('请求失败');
      }
    } catch (error) {
      throw error;
    }
  };

  return (
    <div className="dock-page-wrapper">
      <div className="sp-page-content">
        {mobileFlag ? (
          <MobileSearch
            resourceSearch={onSearch}
            selected={'other'}
            form={form}
            reset={onReset}
          />
        ) : (
          <Form layout="inline" name="basic" form={form} onFinish={onSearch}>
            <Form.Item name="courseName">
              <Input
                placeholder={t('请输入课程名')}
                autoComplete="off"
                onPressEnter={() => {
                  form.submit();
                }}
              />
            </Form.Item>

            {/* <Form.Item name="teacherName">
             <Input placeholder="请输入教师名" autoComplete="off" />
            </Form.Item> */}
            <div className="reset-wrp" onClick={onReset}>
              <span>{t('清空')}</span>
              <ReloadOutlined />
            </div>
            <Button type="primary" htmlType="submit">
              {t('搜索')}
            </Button>
          </Form>
        )}

        <div className="splitLine"></div>
        <div className="button_box">
          <Space size={0}>
            <Checkbox
              indeterminate={indeterminate}
              onChange={onCheckAllChange}
              checked={checkAll}
            >
              {t('全部')}
            </Checkbox>
            {!mobileFlag && (
              <Button
                shape="round"
                type="primary"
                icon={<PlusCircleFilled />}
                onClick={() => setModalVisible(true)}
              >
                {t('新建')}
              </Button>
            )}
            {!mobileFlag && showSwitchMode && (
              <Button
                type="text"
                ghost
                icon={<IconFont type="icontop" />}
                onClick={handleToTop}
                disabled={!selectKey.length}
              >
                {t('置顶')}
              </Button>
            )}
            {!mobileFlag ? (
              btn_list.map((item: any, index: number) => {
                return (
                  <div
                    onClick={() => item.func()}
                    className={`item_${item.disabled ? ' disabled' : ''}`}
                    key={index}
                  >
                    {item.dom}
                  </div>
                );
              })
            ) : (
              //移动端取前两个展示即可
              <>
                {btn_list.slice(0, 4).map((item: any, index: number) => {
                  return (
                    <div
                      className={`item_${item.disabled ? ' disabled' : ''}`}
                      key={index}
                    >
                      {item.dom}
                    </div>
                  );
                })}

                {btn_list.slice(4, btn_list.length).length > 0 && (
                  <Popover
                    className="mobile_btns_popover"
                    onOpenChange={(newOpen: boolean) =>
                      setOpreatMenuVisible(newOpen)
                    }
                    open={operatMenuVisible}
                    content={
                      <div className="mobile_btns">
                        {btn_list
                          .slice(4, btn_list.length)
                          .map((item: any, index: number) => {
                            return (
                              <div
                                key={index}
                                className={item.disabled ? 'disabled' : ''}
                                onClick={() => {
                                  if (!item.disabled) {
                                    setOpreatMenuVisible(false);
                                    item.func();
                                  }
                                }}
                              >
                                {item.dom}
                                {item.title}
                              </div>
                            );
                          })}
                      </div>
                    }
                  >
                    <Button
                      onClick={(e: any) => {
                        e.preventDefault();
                        e.stopPropagation();
                        setOpreatMenuVisible(!operatMenuVisible);
                      }}
                    >
                      <IconFont type="iconziyuanku1" />
                    </Button>
                  </Popover>
                )}
              </>
            )}
          </Space>
          {showSwitchMode && (
            <div className="mode_switch_wrapper">
              <div onClick={() => setIsCardMode(true)} className="mode_switch">
                <Tooltip title={t('图例模式')}>
                  <IconFont
                    type="iconhebingxingzhuangfuzhi2"
                    className={isCardMode ? 'active' : ''}
                  />
                </Tooltip>
              </div>
              <div onClick={() => setIsCardMode(false)} className="mode_switch">
                <Tooltip title={t('列表模式')}>
                  <IconFont
                    type="iconliebiao"
                    className={isCardMode ? '' : 'active'}
                  />
                </Tooltip>
              </div>
            </div>
          )}
        </div>
        <Checkbox.Group
          value={selectKey}
          onChange={onCheckGroupChange}
          style={{ width: '100%' }}
        >
          {isCardMode ? (
            <div className="data_wrapper">
              {dataSource && dataSource.length > 0 ? (
                dataSource.map((item: any) => (
                  <CourseBlockForOther
                    key={item.contentId_}
                    item={item}
                    courseClick={() => window.open(item.jump_address)}
                    onPublish={() => {
                      setOneOrBatch(false);
                      setOperationData(item);
                      setReleaseOrNot(true);
                      setRelease(true);
                    }}
                    onUnPublish={() => {
                      setOneOrBatch(false);
                      setOperationData(item);
                      setReleaseOrNot(false);
                      setRelease(true);
                    }}
                    onPreview={() =>
                      window.open(
                        `/learn/course/preview/${
                          COURSE_TYPE[item.courseType]
                        }/${item.contentId_}?preview=1&show=1&type=released`,
                      )
                    }
                    onDelete={() => {
                      setOneOrBatch(true);
                      setOperationData(item);
                      setIsDeleteModalVisible(true);
                    }}
                    onEdit={() => {
                    setOperationData(item);
                    setModalVisible(true)
                  }}
                  />
                ))
              ) : (
                <Empty style={{ width: '100%' }} />
              )}
            </div>
          ) : (
            <DndContext
              modifiers={[restrictToVerticalAxis]}
              onDragEnd={onDragEnd}
              autoScroll={{
                threshold: {
                  x: 0, // 禁用x
                  y: 0.2,
                },
                acceleration: 10, // 滚动速度
              }}
            >
              <SortableContext
                // rowKey array
                items={dataSource.map(i => i.contentId_)}
                strategy={verticalListSortingStrategy}
              >
                <Table
                  components={{
                    body: { row: Row },
                  }}
                  scroll={{ x: 'max-content', y: 'calc(100vh - 320px)' }}
                  // rowSelection={rowSelection}
                  rowKey="contentId_"
                  columns={columns}
                  size="small"
                  dataSource={dataSource}
                  loading={tableLoading}
                  pagination={false}
                />
              </SortableContext>
            </DndContext>
          )}
        </Checkbox.Group>
        {total > 0 && (
          <Pagination
            style={{ textAlign: 'center', marginTop: 10 }}
            {...{
              current: query.pageIndex,
              pageSize: query.pageSize,
              total: total,
              onChange: pageChange,
              showQuickJumper: true,
              defaultCurrent: 1,
              size: 'small',
              showTotal: total => t('共{name}条', String(total)),
              showSizeChanger: true,
              pageSizeOptions: ['24', '36', '48', '60'],
            }}
          />
        )}
      </div>
      <Modal
        title={releaseOrNot ? t('发布') : t('下架')}
        visible={release}
        onOk={handleOffCourse}
        onCancel={() => setRelease(false)}
      >
        {t('确定要')}
        {releaseOrNot ? t('发布') : t('下架')}
        {t('该课程吗？')}
      </Modal>
      <Modal
        title={t('删除')}
        visible={isDeleteModalVisible}
        onOk={handleDeleteOk}
        onCancel={() => setIsDeleteModalVisible(false)}
        confirmLoading={deleteLoading}
      >
        {t('确定要删除该课程吗？')}
      </Modal>
      <DeleteProcess
        title={t('删除进度')}
        visible={deleteProcessModalVisible}
        processId={processId}
        closeModal={() => setDeleteProcessModalVisible(false)}
        callback={fetchDataList}
      />

      {/*  第三方课程新增课程*/}
      <AddThirdcourseModal
        modalVisible={modalVisible}
        modalClose={() => {
          setModalVisible(false);
          setOperationData(undefined);
        }}
        onSubmit={handleSubmit}
        params={operationData || null}
      />
    </div>
  );
};

export default DockCourseList;
