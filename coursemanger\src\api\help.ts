import HTTP from './index'
// 查询当前页面的指引
export function getguidedirectory(pageCode: string) {
    return HTTP.get(`/unifiedplatform/v1/newhandguide/guidedirectory?pageCode=${pageCode}&isAll=false`).then(res => {
        if (res.status === 200) {
            return res.data
        }
    }).catch(error => {
        console.error(error)
    })
}


// 查询系统seo配置
export function getSeoConfig() {
    return HTTP.get(`/unifiedplatform/v1/setting/seo`).then(res => {
        if (res.status === 200) {
            return res.data
        }
    }).catch(error => {
        console.error(error)
    })
}
