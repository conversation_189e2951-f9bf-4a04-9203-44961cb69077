<?xml version="1.0" encoding="UTF-8"?>
<svg width="28px" height="28px" viewBox="0 0 28 28" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>视频</title>
    <defs>
        <linearGradient x1="16.3004225%" y1="0%" x2="100%" y2="111.263027%" id="linearGradient-1">
            <stop stop-color="#9ECDFF" offset="0.281359266%"></stop>
            <stop stop-color="#4B97FD" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="切图" transform="translate(-78.000000, -179.000000)">
            <g id="编组-10备份-2" transform="translate(78.000000, 179.000000)">
                <circle id="椭圆形" fill="#E1EFFF" cx="14" cy="14" r="14"></circle>
                <g id="小视频，影视，48" transform="translate(6.000000, 6.000000)" fill="url(#linearGradient-1)" fill-rule="nonzero">
                    <path d="M8,0 C12.4181818,0 16,3.58181818 16,8 C16,10.9465455 14.4069091,13.5210909 12.0349091,14.9090909 L14.7272727,14.9090909 C15.028519,14.9090909 15.2727273,15.1532992 15.2727273,15.4545455 C15.2727273,15.7557917 15.028519,16 14.7272727,16 L8,16 C3.58181818,16 0,12.4181818 0,8 C0,3.58181818 3.58181818,0 8,0 Z M6.53818182,4.72727272 C6.14036363,4.72727272 5.81818181,5.03781819 5.81818181,5.42109091 L5.81818181,5.42109091 L5.81818181,10.5781818 C5.81818181,10.7055929 5.85422924,10.8304041 5.92218181,10.9381818 C6.12836363,11.2658182 6.57127272,11.3705454 6.91127273,11.1716364 L6.91127273,11.1716364 L11.2894545,8.61381819 C11.3872727,8.55709092 11.4694545,8.47854546 11.5290909,8.38472728 C11.7378182,8.05854546 11.6323636,7.63127273 11.2938182,7.43018182 L11.2938182,7.43018182 L6.91563636,4.83054546 C6.80218182,4.76290909 6.67127273,4.72727272 6.53818182,4.72727272 Z" id="形状结合"></path>
                </g>
            </g>
        </g>
    </g>
</svg>