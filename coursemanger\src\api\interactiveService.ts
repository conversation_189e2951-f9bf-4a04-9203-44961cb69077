import HTTP from '@/api/index';
import { message } from 'antd';

import { AxiosRequestConfig } from 'axios';

type HttpType = <T = any>(
  url: string,
  config?: AxiosRequestConfig,
) => Promise<API.IResponse2<T> | undefined>;

const prefix = '/classroom';

const http: HttpType = (url, config) =>
  HTTP(prefix + url, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...config,
  })
    .then((res) => res.data)
    .then((res) => {
      if ( res && res.status !== 200) {
        message.error(res.message);
      }
      return res;
    })
    .catch((err) => {
      message.error(err.toString());
    });

namespace InteractiveService {
  export const fetchInteractive = (params: {
    anserType?: string;
    courseId: string;
    questionTitle?: string;
  }) =>
    http<Interactive.ICategory[]>(`/webCh/courseTree`, {
      params,
    });
  /**
   * 创建课程
   * @param params
   */
  export const createClass = (params: { chName: string; classId: string }) =>
    http('/chSchedule/add', {
      method: 'POST',
      data: JSON.stringify(params),
    });
  export const editClass = (params: { chName: string; classId: string, chid: string, }) =>
    http('/chSchedule/update', {
      method: 'PUT',
      data: JSON.stringify(params),
    });
  export const addWeb = (params: Interactive.ICreatedInteraction) =>
    http('/web/add', {
      method: 'POST',
      data: JSON.stringify(params),
    });
  export const editWeb = (params: Interactive.ICreatedInteraction) =>
    http('/web/update', {
      method: 'POST',
      data: JSON.stringify(params),
    });
  export const imageUpload = (formData: any) =>
    http('/web/upload', {
      method: 'POST',
      data: formData,
    });
  export const studentChoseStList = (id: string) =>
    http(`/mini/choseStList?questionId=${id}`, {
      method: 'GET',
    });

  export const studentResponseNumber = (id: string) =>
    http(`/base/responseNumber?questionId=${id}`, {
      method: 'GET',
    });
  export const chScheduleDelete = (id: string) =>
    http(`/chSchedule/delete?chid=${id}`, {
      method: 'DELETE',
    });
  export const webDelete = (params: { questionId: string }[]) =>
    http(`/web/delete`, {
      method: 'DELETE',
      data: JSON.stringify(params),
    });
  export const answerAnalysis = (questionId: string) =>
    http(`/web/answerAnalysis?questionId=${questionId}`, {
      method: 'GET'
    });
  export const getSubmitData = (params: {
    questionId: string;
    type?: string;
  }) =>
    http(`/web/submitData`, {
      params,
    });
}

export default InteractiveService;
