.homeworkobejct-container {
  font-family: PingFangSC-Medium, PingFang SC;
}
.homeworkobejct-container .header-container {
  height: 64px;
  display: flex;
  align-items: center;
  padding-left: 30px;
  border-bottom: 1px solid #D8D8D8;
}
.homeworkobejct-container .header-container .back-btn {
  color: #333;
  font-size: 18px;
  cursor: pointer;
}
.homeworkobejct-container .content-container {
  padding: 15px 30px;
}
.homeworkobejct-container .content-container .header_boxs {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}
.homeworkobejct-container .content-container .timu {
  color: #549cff;
  font-size: 16px;
}
.homeworkobejct-container .content-container .grade-time {
  background: #F3F7FF;
  border-radius: 25px;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-align: center;
  align-items: center;
  font-size: 16px;
  padding: 0 18px;
  color: #333;
}
.homeworkobejct-container .content-container .grade-time .grade {
  margin-right: 10px;
}
.homeworkobejct-container .content-container .table_btn {
  display: flex;
  justify-content: flex-end;
  margin: 20px 0;
}
.homeworkobejct-container .content-container .ant-tabs-tab-active {
  font-weight: 700;
}
.homeworkobejct-container .content-container .team_name {
  max-width: 100%;
}
.homeworkobejct-container .content-container .team_name:hover {
  cursor: pointer;
  color: var(--primary-color);
}
.homeworkobejct-container .content-container .search-container {
  display: flex;
  justify-content: space-between;
}
.homeworkobejct-container .content-container .search-container .left {
  display: flex;
  margin-bottom: 16px;
}
.homeworkobejct-container .content-container .search-container .text {
  line-height: 32px;
  color: #333333;
  font-weight: 600;
  font-size: 18px;
  margin-right: 39px;
}
.homeworkobejct-container .content-container .search-container .ant-input-search {
  width: 230px;
}
.right {
  display: flex;
}
.team-person-popverstyle .ant-popover-title {
  color: rgba(0, 0, 0, 0.85);
  border-bottom: 1px solid #f0f0f0;
  background: #f6f6f6;
  font-family: '宋体';
  padding: 6px 16px;
  font-weight: bold;
}
.team-person-popverstyle .team-person-name {
  font-family: '宋体';
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding-left: 10px;
}
.grade-upload-modal .ant-modal-body {
  padding: 20px 50px;
}
.grade-upload-modal .no-file-wrp p {
  display: flex;
}
.grade-upload-modal .no-file-wrp p a {
  text-decoration: underline;
}
.grade-upload-modal .file-upload-wrp {
  text-align: center;
}
.grade-upload-modal .file-upload-wrp p {
  margin-bottom: 35px;
}
.table_box .ant-table-body {
  min-height: 200px;
}
