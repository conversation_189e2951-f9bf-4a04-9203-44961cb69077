import HTTP from './index'

namespace baseInfo {

    export function getCourseDetails(id: number | string, courseSemester?: number) {
        return HTTP.get(`/learn/v1/teaching/course/get/course/details?contentId=${id}&courseSemester=${courseSemester ?? 1}`).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            console.error(error)
        })
    }

    export function getCourseDetailsduration(id: number | string) {
        return HTTP.get(`/rman/v1/entity/base/${id}`).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            console.error(error)
        })
    }



    // 无
    // export function getCourseOption(page: number, size: number) {
    //     return HTTP.post('/cvod/v1/teaching/course/get/offline/course', {
    //         page,
    //         size
    //     }).then(res => {
    //         if (res.status === 200) {
    //             return res.data
    //         }
    //     }).catch(error => {
    //         console.error(error)
    //     })
    // }
    export function changeBasicInfo(data: any,query:any) {
        return HTTP.post('/learn/v1/teaching/course/update/'+query.Check , data).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            console.error(error)
        })
    }
    export function getactivities(data: any, courseSemester?: number) {
        return HTTP.get(`/learn/v1/teaching/get/activities/details?courseId=${data}&courseSemester=${courseSemester}`).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            console.error(error)
        })
    }
    // 无
    // export function getpeople(data: any) {
    //     return HTTP.get(`/cvod/v1/teaching/course/get/course/people/count?courseId=${data}`).then(res => {
    //         if (res.status === 200) {
    //             return res.data
    //         }
    //     }).catch(error => {
    //         console.error(error)
    //     })
    // }
    export function getpeopleAuthority(data: any) {
        return HTTP.get(`/learn/v1/teaching/get/course/authority?course_id=${data}`).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            console.error(error)
        })
    }
    export function updateMenuSetting(data: any) {
      return HTTP.post(`/learn/v1/teaching/menu/setting`, data).then(res => {
          if (res.status === 200) {
              return res.data
          }
      }).catch(error => {
          console.error(error)
      })
  }
  export function reqAgent(params: any) {
    return HTTP.get(`/terminator/api/v1/application/`, { params }).then(res => {
        if (res.status === 200) {
            return res.data
        }
    }).catch(error => {
        console.error(error)
    })
    }
    
    export function getSchoolList() {
        return HTTP.get(`/unifiedplatform/v1/organization/child/edu?onlyDirectSub=true&homeDisplay=true&courseUse=true`).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            console.error(error)
        })
      }
      
      export function getMajorList(schoolId: string) {
        return HTTP.get(`/unifiedplatform/v1/organization/child/${schoolId}?onlyDirectSub=true`).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            console.error(error)
        })
      } 
}

export default baseInfo;
