.content-right {
    width: 100%;
    height: 100%;
    background-color: #fff;
    border-radius: 10px;
    padding: 40px 8px 20px 40px;

    .ant-tabs {
        height: 100%;

        .ant-tabs-nav {
            padding-right: 32px;
            width: calc(100% - 32px);

            .ant-tabs-nav-wrap {
                padding-left: 30px;
            }

            .ant-tabs-tab+.ant-tabs-tab {
                margin: 0 0 0 70px;
            }

            .ant-tabs-tab-btn {
                font-weight: 600;
                font-size: 18px;
                color: #6C6C6C;
            }

            .ant-tabs-tab-active .ant-tabs-tab-btn {
                font-weight: 600;
                font-size: 18px;
                color: #2E2E2E;
            }

            .ant-tabs-ink-bar {
                background: linear-gradient(319deg, #5C43FF 0%, #7C91FC 100%);
                height: 3px;
            }
        }

        .ant-tabs-content-holder {
            .ant-tabs-content {
                height: 100%;
            }

            .ant-tabs-tabpane {
                height: 100%;

                &::-webkit-scrollbar {
                    width: 5px;
                }

                /* 设置滚动条轨道 */
                &::-webkit-scrollbar-track {
                    background: transparent;
                }

                /* 设置滚动条滑块 */
                &::-webkit-scrollbar-thumb {
                    background-color: #d1d0d0;
                    border-radius: 3px;
                }
            }

            .item-wrap {
                height: 100%;
                display: flex;
                padding-right: 12px;
                flex-direction: column;

                .item {
                    flex: 1;
                    overflow: auto;
                    padding-right: 20px;

                    &::-webkit-scrollbar {
                        width: 5px;
                    }

                    /* 设置滚动条轨道 */
                    &::-webkit-scrollbar-track {
                        background: transparent;
                    }

                    /* 设置滚动条滑块 */
                    &::-webkit-scrollbar-thumb {
                        background-color: #d1d0d0;
                        border-radius: 3px;
                    }

                    .top {
                        margin-bottom: 10px;

                        .title-wrap {
                            display: flex;
                            align-items: center;
                            margin-bottom: 4px;

                            .dot {
                                width: 4px;
                                height: 4px;
                                background: linear-gradient(319deg, #7A43FF 0%, #7C91FC 100%);
                                margin-right: 5px;
                                border-radius: 50%;
                            }

                            .title {
                                font-weight: 400;
                                font-size: 16px;
                                color: #2E2E2E;
                                font-family: PingFangSC, PingFang SC;

                                span {
                                    font-weight: 600;
                                }
                            }
                        }

                        .content {
                            font-weight: 400;
                            font-size: 16px;
                            color: #2E2E2E;
                            padding-left: 10px;
                            font-family: PingFangSC, PingFang SC;
                        }
                    }

                    .des {
                        margin-bottom: 20px;

                        .title-wrap {
                            display: flex;
                            align-items: center;
                            margin-bottom: 4px;

                            .dot {
                                width: 4px;
                                height: 4px;
                                background: linear-gradient(319deg, #7A43FF 0%, #7C91FC 100%);
                                margin-right: 5px;
                                border-radius: 50%;
                            }

                            .title {
                                font-family: PingFangSC, PingFang SC;
                                font-weight: 400;
                                font-size: 16px;
                                color: #2E2E2E;
                            }
                        }

                        .anaysis {
                            font-weight: 400;
                            font-size: 16px;
                            font-family: PingFangSC, PingFang SC;
                            color: #2E2E2E;
                            padding-left: 10px;
                        }
                    }

                    .table-wrap {
                        background: #FFFFFF;
                        box-shadow: 2px 2px 11px 0px rgba(192, 192, 192, 0.5);
                        border-radius: 10px;
                        padding: 4px;

                        .ant-table-content {
                            .ant-table-thead {
                                border-radius: 10px;
                                th {
                                    background: #ECEFFE;
                                    font-size: 16px;
                                    font-weight: 500;
                                    color: #2E2E2E;
                                    border-right: 1px dashed #D4CEFF;
                                    .ant-checkbox-indeterminate {
                                        .ant-checkbox-inner {
                                            background: #fff;
                                            border-color: #7a53fe;
                                        }
                                        .ant-checkbox-inner::after {
                                            background-color: #7a53fe;
                                        }
                                    }
                                    .ant-checkbox-checked {
                                        .ant-checkbox-inner {
                                            background: #7a53fe;
                                            border-color: #7a53fe;
                                        }
                                        .ant-checkbox-inner::after {
                                            background-color: #7a53fe;
                                        }
                                    }
                                }
                                th::before {
                                    height: 0 !important;
                                }
                                th:nth-child(5), th:nth-child(6){
                                    color: #6251FE;
                                }
                                th:nth-child(1){
                                    border-top-left-radius: 10px;
                                    border-bottom-left-radius: 10px;
                                }
                                th:nth-child(7){
                                    border-top-right-radius: 10px;
                                    border-bottom-right-radius: 10px;
                                }
                                th:nth-child(4), th:nth-child(6){
                                    text-align: center;
                                }
                                th:last-child {
                                    border: none;
                                }
                            }
                            .ant-table-tbody {
                                tr:nth-child(2n) {
                                    background: #ECEFFE;
                                    td:nth-child(1){
                                        border-top-left-radius: 10px;
                                        border-bottom-left-radius: 10px;
                                    }
                                    td:nth-child(7){
                                        border-top-right-radius: 10px;
                                        border-bottom-right-radius: 10px;
                                    }
                                }
                                tr td {
                                    border-right: 1px dashed #D4CEFF;
                                    vertical-align: middle;
                                    .ant-checkbox-checked {
                                        .ant-checkbox-inner {
                                            background: #7a53fe;
                                            border-color: #7a53fe;
                                        }
                                    }
                                }
                                tr td:last-child {
                                    border-right: none;
                                }
                            }

                            .stuName,
                            .stuCode,
                            .score {
                                font-size: 16px !important;
                                color: #2E2E2E;
                            }

                            .answer,
                            .comment {
                                display: inline-block;
                                min-width: 100px;
                                font-size: 14px;
                                color: #2E2E2E;
                            }


                            .detail {
                                font-size: 16px;
                                color: #6453FE;
                                cursor: pointer;
                            }
                        }
                    }
                }

                button {
                    width: 139px;
                    height: 50px;
                    margin-right: 20px;
                    background: linear-gradient(319deg, #5C43FF 0%, #7C91FC 100%);
                    border-radius: 25px;
                    align-self: flex-end;
                    margin-top: 20px;
                    flex-shrink: 0;

                    span {
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 600;
                        font-size: 18px;
                        color: #FFFFFF;
                    }
                }
            }

            .item-detail {
                height: 100%;
                display: flex;
                padding-right: 12px;
                flex-direction: column;

                .item {
                    flex: 1;
                    overflow: auto;
                    padding-right: 20px;
                    &::-webkit-scrollbar {
                        width: 5px;
                    }

                    /* 设置滚动条轨道 */
                    &::-webkit-scrollbar-track {
                        background: transparent;
                    }

                    /* 设置滚动条滑块 */
                    &::-webkit-scrollbar-thumb {
                        background-color: #d1d0d0;
                        border-radius: 3px;
                    }
                    .back {
                        display: flex;
                        align-items: center;
                        cursor: pointer;;
                        img {
                            margin-right: 8px;
                        }
                        margin-bottom: 20px;
                        font-size: 16px;
                        color: #6456FE;
                    }

                    .top {
                        margin-bottom: 10px;

                        .title-wrap {
                            display: flex;
                            align-items: center;
                            margin-bottom: 4px;

                            .dot {
                                width: 4px;
                                height: 4px;
                                background: linear-gradient(319deg, #7A43FF 0%, #7C91FC 100%);
                                margin-right: 5px;
                                border-radius: 50%;
                            }

                            .title {
                                font-weight: 400;
                                font-size: 16px;
                                color: #2E2E2E;
                                font-family: PingFangSC, PingFang SC;

                                span {
                                    font-weight: 600;
                                }
                            }
                        }

                        .content {
                            font-weight: 400;
                            font-size: 16px;
                            color: #2E2E2E;
                            padding-left: 10px;
                            font-family: PingFangSC, PingFang SC;
                        }
                    }

                    .des {
                        background: #F3F3F3;
                        border-radius: 10px;
                        padding: 20px 16px 20px 20px;
                        margin-bottom: 20px;
                        .title, .anaysis {
                            font-size: 16px;
                            color: #2E2E2E;
                        }
                    }

                    .studentDetail {
                        background: #FFFFFF;
                        box-shadow: 2px 2px 11px 0px rgba(192,192,192,0.5);
                        border-radius: 10px;
                        padding: 20px;
                        .detail {
                            display: flex;
                            align-items: center;
                            margin-bottom: 20px;
                        }
                        .detail-left {
                            width: 51px;
                            font-weight: 500;
                            flex-shrink: 0;
                            font-size: 16px;
                            text-align: justify;
                            text-align-last: justify;
                            /* 兼容部分浏览器 */
                            -moz-text-align-last: justify;
                            -webkit-text-align-last: justify;
                        }
                        .detail-right {
                            margin-left: 15px;
                        }
                        .stuName {
                            border-bottom: 1px dashed #D4CEFF;
                            color: #2E2E2E;
                            font-weight: 500;
                            font-size: 16px;
                            padding-bottom: 13px;
                            .name {
                                margin-right: 10px;
                            }
                        }
                        .answer {
                            .detail-right {
                                font-weight: 400;
                                font-size: 16px;
                                color: #2E2E2E;
                            }
                        }
                        .score {
                            font-weight: 500;
                            color: #6251FE;
                            font-size: 16px;
                            .detail-right {
                                width: 100%;
                                margin-left: 30px;
                                .ant-input-number:hover, .ant-input-number-focused {
                                    border-color: #d9d9d9;
                                    box-shadow: none;
                                }
                            }
                        }
                        .comment {
                            .detail-left {
                                color: #6251FE;
                            }
                            .detail-right {
                                width: 100%;
                                margin-left: 30px;
                                .ant-input {
                                    min-height: 200px;
                                    padding: 20px;
                                    border-radius: 10px;
                                    background: #ECEFFE;
                                }
                                .notShowborder, .notShowborder.ant-input:focus, .notShowborder.ant-input:hover {
                                    border-color: #ECEFFE;
                                    box-shadow: none;
                                }
                                .showborder, .showborder.ant-input:focus, .showborder.ant-input:hover {
                                    border-color: #7a53fe;
                                    box-shadow: none;
                                }
                            }
                        }
                    }
                }

                button {
                    width: 139px;
                    height: 50px;
                    margin-right: 20px;
                    background: linear-gradient(319deg, #5C43FF 0%, #7C91FC 100%);
                    border-radius: 25px;
                    align-self: flex-end;
                    margin-top: 20px;
                    flex-shrink: 0;
                    span {
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 600;
                        font-size: 18px;
                        color: #FFFFFF;
                    }
                }
            }

        }
    }
}
