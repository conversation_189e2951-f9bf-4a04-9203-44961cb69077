var axios = require('axios');
import { message } from 'antd';
import { fetchEventSource } from '@microsoft/fetch-event-source';

// 智能详解 带上请求头token 如果带上了token就使用heard的token 反之使用服务器配置的token
// export function chatsteme(obj:any) {
//   var data = JSON.stringify(obj);
//   var config = {
//     method: 'post',
//     url: 'https://gpt.sobeydev.com/api/chat',
//     headers: {
//       'content-type': 'application/json',
//       // 'token': '***************************************************',
//       'X-CSRF-Token': '123123123'
//     },
//     data : data
//   };
//   return axios(config)
//     .then(function (response:any) {
//       return response.data.choices;
//     })
//     .catch(function (error:any) {
//       message.info('智能解析服务异常，请稍后再试！')
//     });

// }

export async function getSmartExplain2(label:any,callback:any,overback:any){

  try {
    const abortController = new AbortController();
    const signal = abortController.signal;
    let responseText = "";
    fetchEventSource(`/unifiedplatform/llm/v1/chat/ability?llmAbilityTag=4`, {
      method: 'POST',
      headers: {
        "Content-Type": "application/json"
      },
      signal: signal,
      body: JSON.stringify({
        "messages": [
          {
            role: "assistant",
            content: `我是一个知识图谱专家，我会根据用户的提示词生成知识图谱节点详解，我会严格遵循以下规则：
            1. 不要使用任何markdown的标签
            2. 直接返回html富文本
            3. 严格检查返回的内容是否符合html语法`
          },
          {
            "role": "user",
            "content": `知识点详解：${label}`
          }
        ],
        "stream": true
      }),
      onmessage(event) {
        const { data } = event;
        console.log(JSON.parse(data));
        let content =  JSON.parse(data).choices[0].delta.content || '';
        responseText = responseText + content;
        callback(responseText);
        if(JSON.parse(data).choices[0].finish_reason === 'stop') {
          overback(responseText);
          abortController.abort();
        }
      },
      onclose() {
        console.log('close');
      },
      onerror(err) {
        console.log('error', err);
        abortController.abort();
      }
    });
  } catch (err) {
    console.error("NetWork Error", err);
    message.info('智能解析网络异常，请稍后再试！')
    overback();
  }
}


export async function getSmartExplain(label:any,callback:any,overback:any){
  try {
    const controller = new AbortController();
    // const res = await fetch("/unifiedplatform/llm/v1/chat/ability?llmAbilityTag=4", {
    //   method: "POST",
    //   headers: {
    //     "Content-Type": "application/json"
    //   },
    //   body: JSON.stringify({
    //     "messages": [
    //       {
    //         "role": "user",
    //         "content": label
    //       }
    //     ],
    //     "stream": true
    //   }),
    //   signal: controller.signal,
    // });

    fetch("/unifiedplatform/llm/v1/chat/ability?llmAbilityTag=4", {
      method: 'POST',
      signal:controller.signal,
      body: JSON.stringify({
        "messages": [
          {
            role: "assistant",
            content: `我是一个知识图谱专家，我会严格遵循以下规则：
            1. 直接返回html富文本
            2. 富文本内容不使用h标签，标题后面带上换行符`
          },
          {
            "role": "user",
            "content": `知识点详解：${label}`
          }
        ],
        "stream": true
      }),
      headers: {
        "Content-Type": "application/json"
      },
  }).then((res:any)=>{
    let responseText = "";
    let uUint8arr: any = [];
    const reader = res.body?.getReader();
    let chat_info: any = null;
    let isthink =  false;
      // 处理流数据
      const write = getWrite(
          reader,
          res.headers.get('Content-Type') !== 'application/json',
          (content: string, config: any, finish: boolean, firstChatInfo) => {
            if(finish){
              overback(responseText);
            }
            if(content.indexOf('<think>')>-1){
              isthink = true;
            }

            if(isthink){
              if(content.indexOf('</think>')>-1){
                isthink = false;
              }
            }else{
              responseText += content;
              callback(responseText);
            }
          }
      )
  })



    // if (res.ok) {
    //   const reader = res.body?.getReader();
    //   // const decoder = new TextDecoder();

    //   // while (true) {
    //   //   const content = await reader?.read();
    //   //   if (content?.value) {
    //   //     uUint8arr.push(...content.value);
    //   //     const responseJSON = decoder.decode(new Uint8Array(uUint8arr));
    //   //     let json = JSON.parse(responseJSON);
    //   //     debugger
    //   //   }
    //   //   debugger
    //   //   const text = decoder.decode(content?.value);
    //   //   responseText += text;
    //   //   callback(responseText);
    //   //   const done = !content || content.done;
    //   //   if (done) {
    //   //     overback(responseText);
    //   //     break;
    //   //   }
    //   // }


    // } else if (res.status === 401) {
    //   console.error("Anauthorized");
    //   message.info('智能解析服务未授权，请稍后再试！')
    //   overback();

    // } else {
    //   console.error("Stream Error");
    //   message.info('智能解析流异常，请稍后再试！')
    //   overback();
    // }
  } catch (err) {
    console.error("NetWork Error", err);
    message.info('智能解析网络异常，请稍后再试！')
    overback();
  }
}


export const getWrite = (reader: any, stream: boolean, callback: (content: any, config: any, finish: boolean, newChatInfo?: any) => void) => {
  let tempResult = '';
  let responseText = '';
  let configTools: any = null;
  let firstChatInfo: any = null;
  /**
   *
   * @param done  是否结束
   * @param value 值
   */
  const write_stream = ({ done, value }: { done: boolean; value: any }) => {
      try {
          if (done) {
              return
          }
          const decoder = new TextDecoder('utf-8')
          let str = decoder.decode(value, { stream: true })
          // 这里解释一下 start 因为数据流返回流并不是按照后端chunk返回 我们希望得到的chunk是data:{xxx}\n\n 但是它获取到的可能是 data:{ -> xxx}\n\n 总而言之就是 fetch不能保证每个chunk都说以data:开始 \n\n结束
          tempResult += str
          const split = tempResult.match(/data:.*}\n\n/g)
          if (split) {
              str = split.join('')
              tempResult = tempResult.replace(str, '')
          } else {
              return reader.read().then(write_stream)
          }
          // 这里解释一下 end
          if (str && str.startsWith('data:')) {
              if (split) {
                  for (const index in split) {
                      let chunk = JSON?.parse(split[index].replace('data:', ''));
                      if (chunk?.choices) { // 为了适配upfrom大模型测试对话接口的结构数据
                          chunk = {
                              content:chunk?.choices?.[0]?.delta?.content,
                              event: chunk?.choices?.[0]?.finish_reason === 'stop' ? 'finish' : 'add',
                              role: chunk?.choices?.[0]?.delta?.role,
                              record_id: chunk?.id,
                              upform: true,
                          }
                      }
                      // const content = chunk?.role === 'assistant' ? chunk?.content || '' : '';
                      const content = chunk?.content || '';

                      // console.info('chunk', 'chunk', {chunk})
                      if (chunk && (!chunk?.role)) {
                          responseText += content;
                      }

                      configTools = chunk;

                      if (chunk && chunk?.role === 'tools' && chunk.content?.type === "chat_info") {
                          firstChatInfo = chunk.content.chat_info || {};
                      }
                      chunk && callback(content, configTools, false, firstChatInfo);
                      if (chunk.event === 'finish') {
                          // 流处理成功 返回成功回调
                          callback(responseText || '', configTools, true, firstChatInfo);
                          return Promise.resolve()
                      }
                  }
              }
          }
      } catch (e) {
          return Promise.reject(e)
      }
      return reader.read().then(write_stream)
  }
  /**
   * 处理 json 响应
   * @param param0
   */
  const write_json = ({ done, value }: { done: boolean; value: any }) => {
      if (done) {
          const result_block = JSON.parse(tempResult)
          if (result_block.code === 500) {
              return Promise.reject(result_block.message)
          } else {
              if (result_block.content) {
                  // console.info('content', 'content', result_block.content)
              }
          }
          return
      }
      if (value) {
          const decoder = new TextDecoder('utf-8')
          tempResult += decoder.decode(value)
      }
      return reader.read().then(write_json)
  }
  return stream ?  reader.read().then(write_stream) : write_json
}
