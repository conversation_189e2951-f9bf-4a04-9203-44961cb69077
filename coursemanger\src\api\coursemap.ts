import HTTP from './index';

//获取 学院 专业 学科 基础数据
export function getdatabaseconfig() {
  return HTTP.get(`/unifiedplatform/v1/base/data/database/config`).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  });
}
// 添加地图弹窗 查询知识图谱
export const getknowledgeAll = (data: any) =>
  HTTP(`/learn/knowledge/queryPage`, {
    method: 'GET',
    params: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

/**
 * 课程知识图谱添加关系
 * @param params
 */
export const addRelation = (courseId: string, data: any) =>
  HTTP(`/learn/knowledgeCourse/addRelation?courseId=${courseId}`, {
    method: 'POST',
    data: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 查询知识图谱所有节点 2层异步加载
export const queryCourseNode = (nodeId: number, type: number) =>
  HTTP(`/learn/course/queryCourseNode?nodeId=${nodeId}&type=${type}`, {
    method: 'GET',
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 查询知识图谱所有节点
export const queryCourseAllNode = (nodeId: number) =>
  HTTP(`/learn/course/initAll?nodeId=${nodeId}`, {
    method: 'GET',
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 查询知识图谱所有节点
export const queryNodeinfo = (nodeId: string) =>
  HTTP(`/learn/knowledgeNode/info/${nodeId}`, {
    method: 'GET',
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 检索节点 类型：0分类节点 1知识点,全部 2
export const querybytype = (inputtext: string, nodeId: string, type: string) =>
  HTTP(`/learn/course/query?name=${inputtext}&nodeId=${nodeId}&type=${type}`, {
    method: 'GET',
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 检索关联节点
export const queryrelation = (nodeId: string) =>
  HTTP(`/learn/knowledgeNode/relation?nodeId=${nodeId}`, {
    method: 'GET',
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 查询对应节点的所有对比辨析,不传id查所有的对比辨析
export const queryAll = (data: any) =>
  HTTP(`/learn/discriminate/queryAll`, {
    method: 'GET',
    params: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 查询对应节点的所有对比辨析,不传id查所有的对比辨析
export const updatenode = (data: any) =>
  HTTP(`/learn/knowledgeCourse/updateShow`, {
    method: 'post',
    data: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 取消知识点素材绑定
export const removeVideo = (data: any) =>
  HTTP(`/learn/knowledgeNode/removeVideo`, {
    method: 'GET',
    params: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 删除地图
export const cancelBinding = (data: any) =>
  HTTP(`/learn/knowledgeCourse/cancelBinding`, {
    method: 'GET',
    params: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 更新节点绑定素材信息
export const updateNode = (data: any) =>
  HTTP(`/learn/knowledgeNode/update`, {
    method: 'POST',
    data: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 批量移除知识点绑定的资源
export const removeresource = (data: any) =>
  HTTP(`/learn/knowledgeNode/batch/remove/resource`, {
    method: 'POST',
    data: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 批量设置疑难点
export const updatebatchDifficulty = (flage: any, nodeidarr: any) =>
  HTTP(`/learn/knowledgeNode/batchDifficulty`, {
    method: 'POST',
    data: {
      difficulty: flage,
      knowlegeId: nodeidarr,
    },
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 查询我的地图
export const queryMymap = (params: any) =>
  HTTP(`/learn/m1/knowledge/query/my/page`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

  export const queryminemap = (params: any) =>
    HTTP(`/learn/m1/knowledge/query/page`, {
      method: 'GET',
      params,
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        return error;
      });

// 这里是改了查询接口 之前的接口好像废弃了
export const querymap = (params: any) =>
  HTTP(`/learn/m1/knowledge/query/page`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });


// 查询知谱空间
export const queryKnowledgeList = (params: any) => {
  return HTTP(`/KnowledgeGraph4py/ChinaDidac/getGraphList`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};
//查询知谱空间地图的详细信息
export const queryKnowledgeDetailById = (params: any) => {
  return HTTP(`/KnowledgeGraph4py/ChinaDidac/getGraphInfo`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};
// 查询专业地图
export const quermarjormap = (params: any) => {
  return HTTP(`/learn/m1/knowledge/get/knowledge/graph`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};

// 添加地图
export const addmap = (data: any) =>
  HTTP(`/learn/m1/knowledge/add`, {
    method: 'POST',
    data: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 删除地图
export const deletemap = (data: any,params?:any) =>
  HTTP(`/learn/m1/knowledge/batch/delete`, {
    method: 'POST',
    data: data,
    params
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 复制地图
export const copymap = (data: any) =>
  HTTP(`/learn/m1/knowledge/batch/copy`, {
    method: 'POST',
    data: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 复制地图
export const copyselectmap = (data: any) =>
  HTTP(`/learn/m1/knowledge/copy/node`, {
    method: 'get',
    params: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 发布 下架 共享
export const updatamapstatus = (data: any, status: any) =>
  HTTP(`/learn/m1/knowledge/batch/publish?status=${status}`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 批量引用
export const quotemap = (data: any) =>
  HTTP(`/learn/m1/knowledge/batch/quote`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 根据id查询地图基本信息
export const querymapinfo = (params: any) =>
  HTTP(`/learn/m1/knowledge/info`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 根据id查询地图 管理员
export const querymapteacher = (params: any) =>
  HTTP(`/learn/m1/manager/list/teacher`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 批量添加修改
export const addmapteacher = (data: any) =>
  HTTP(`/learn/m1/manager/batch/add`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

export const deletemapteacher = (data: any) =>
  HTTP(`/learn/m1/manager/batch/delete`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

export const updatemapinfo = (data: any) =>
  HTTP(`/learn/m1/knowledge/update`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 批量添加修改
export const savemap = (data: any) =>
  HTTP(`/learn/m1/knowledge/course/save/nodes`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 根据id查询地图 管理员
export const querymapbyid = (params: any) =>
  HTTP(`/learn/m1/knowledge/course/find/nodes`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 下载excel模板
export function downloadtemplate(type: number) {
  return HTTP(`/learn/m1/knowledge/course/download/template/${type}`, {
    method: 'GET',
    responseType: 'blob',
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// word导入
export function importwordbymap(id: string, courseId: any, data: any) {
  return HTTP(
    `/learn/m1/knowledge/course/import/catalogue?mapId=${id}&courseId=${courseId}`,
    {
      method: 'POST',
      data,
    },
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// excel导入
export function importexcel(id: string, courseId: any, data: any) {
  return HTTP(`/learn/m1/knowledge/course/upload/${id}?courseId=${courseId}`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// word导入
export function importword(
  id: string,
  data: any,
) {
  return HTTP(
    `/learn/m1/knowledge/course/import/catalogue?mapId=${id}`,
    {
      method: 'POST',
      data,
    },
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// xmind导入
export function importxmind(
  id: string,
  flowName: string,
  generationmode: string,
  data: any,
) {
  return HTTP(
    `/rman/v1/upload/outline/to/map/not/import?mapId=${id}&flowName=${flowName}&node_type=${generationmode}`,
    {
      method: 'POST',
      data,
    },
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 导入大纲
export function importcatalogue(
  id: string,
  flowName: string,
  generationmode: string,
  data: any,
) {
  return HTTP(
    `/rman/v1/upload/outline/to/map/not/import?mapId=${id}&flowName=${flowName}&node_type=${generationmode}`,
    {
      method: 'POST',
      data,
    },
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
      return error;
    });
}

// excel 导出
export function exportexcel(mapId: any) {
  return HTTP(`/learn//m1/knowledge/course/download/${mapId}`, {
    method: 'GET',
    responseType: 'blob',
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 添加地图到课程
export function addmaptocourse(data: any) {
  return HTTP(`/learn/m1/course/add/relation`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 课程里面引用地图把试题绑定
export function addquestiontocourse(data: any) {
  return HTTP(`/exam-api/knowledge/bind`, {
    method: 'POST',
    data,
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  });
}

// 地图工具端引用地图把试题绑定
export function knowledgequote(data: any) {
  return HTTP(`/exam-api/knowledge/quote`, {
    method: 'POST',
    data,
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  });
}
// 课程地图推荐试题
export function knowledrecommend(data: any) {
  return HTTP(`/exam-api/knowledge/recommend`, {
    method: 'POST',
    data,
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  });
}

// 查询课程绑定的图谱
export function querymapbycourse(params: any) {
  return HTTP(`/learn/m1/course/get/map`, {
    method: 'POST',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 删除课程绑定的图谱
export function deletemapbycourse(params: any) {
  return HTTP(`/learn/m1/course/cancel/binding`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 修改图谱是否显示
export function updatemapshow(data: any) {
  return HTTP(`/learn/m1/course/update/show`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 查询对比辨析
export function getdiscriminate(params: any) {
  return HTTP(`/learn/discriminate/find/all`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 添加对比辨析
export function addDiscriminate(data: any) {
  return HTTP(`/learn/discriminate/add`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 删除对比辨析
export function deleteDiscriminate(courseId: string, mapId: string, data: any) {
  return HTTP(
    `/learn/discriminate/batch/delete?courseId=${courseId}&mapId=${mapId}`,
    {
      method: 'POST',
      data,
    },
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 删除对比辨析
export function updateDiscriminate(data: any) {
  return HTTP(`/learn/discriminate/update`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 查询对比辨析
export function getdiscriminateinfo(params: any) {
  return HTTP(`/learn/discriminate/find/discriminate`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 查询当前图谱编辑状态
export function getmapedit(params: any) {
  return HTTP(`/learn/m1/knowledge/get/edit`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 加锁
export function lockmap(params: any) {
  return HTTP(`/learn/m1/knowledge/edit`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 上传图片不入库
export function uploadFile(data: any) {
  return HTTP.post(`/rman/v1/upload/save/image`, data)
    .then(res => {
      if (res?.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

//导入演示数据
export function importDemoData(mapId: any) {
  return HTTP(`/learn/m1/knowledge/course/demonstrate/${mapId}`, {
    method: 'GET',
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 通过mapId获取课程图谱历史数据
export function getHistoryData(mapId: any) {
  return HTTP(`/learn/history/courseMap/find/node?mapId=${mapId}`, {
    method: 'GET',
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 通过id删除记录
export function deleteHistoryData(id: any) {
  return HTTP(`/learn/history/courseMap/delete?id=${id}`, {
    method: 'DELETE',
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.log(error);
    });
}

// 通过id恢复记录
export function recoverHistoryData(id: any) {
  return HTTP(`/learn/history/courseMap/get/${id}`, {
    method: 'GET',
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.log(error);
    });
}

// 自动识别知识点
export function autoRecognizeKnowledge(
  contentId: string,
  mapId: string,
  generationmode: any,
) {
  return HTTP(
    `/rman/v1/intelligent/pdf/flow?contentId=${contentId}&mapId=${mapId}&node_type=${generationmode}`,
    {
      method: 'POST',
    },
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.log(error);
    });
}

// 加锁
export function unlockmap(params: any) {
  return HTTP(`/learn/m1/knowledge/process`, {
    method: 'GET',
    params,
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  });
}

// 绑定试题到节点
export function bindQuestionToNode(data: any) {
  return HTTP(`/exam-api/knowledge/add/questions`, {
    method: 'POST',
    data,
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  });
}

// 删除节点绑定的试题
export function deleteQuestionToNode(data: any) {
  return HTTP(`/exam-api/knowledge/delete/questions`, {
    method: 'POST',
    data,
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  });
}

// 获取当前知识点绑定的试题
export function getknowledgelist(data: any) {
  return HTTP(`/exam-api/knowledge/v2/list`, {
    method: 'POST',
    data,
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  });
}

// 提交课程地图知识点试题修改答案(更新正确率) id为绑定这个知识点绑定试题记录得id
export function questionsanswers(id: any, courseId: any = '', data: any) {
  return HTTP(
    `/exam-api/knowledge/v2/update/questions/${id}?courseId=${courseId}`,
    {
      method: 'POST',
      data,
    },
  ).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  });
}
export function recommendanswers(mapId: any, courseId: any = '',  id: any, data: any) {
  return HTTP(
    `/exam-api/knowledge/recommend/reply?courseId=${courseId}&mapId=${mapId}&id=${id}`,
    {
      method: 'POST',
      data,
    },
  ).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  });
}

// 完成知识点的资源学习
export function finishKnowledge(data: any) {
  return HTTP(`/learn/map/resource/study/add`, {
    method: 'POST',
    data,
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  });
}

// 获取学习资源列表
export function getStudyResourceList(params: any) {
  return HTTP(`/learn/map/resource/study/node/study`, {
    method: 'GET',
    params,
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  });
}

// 根据资源id查询知识点
export function getKnowledgeByResourceIds(data: any) {
  return HTTP(`/rman/v1/cata/sequencemeta/batch/select`, {
    method: 'POST',
    data,
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  });
}

// 根据地图id查询关联的资源id
export function getResourceIdsByMapId(params: any) {
  return HTTP(`/learn/m1/knowledge/course/find/resourceId`, {
    method: 'GET',
    params,
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  });
}

// 转换资源失败提示-已读消息
export function redmessage(mapId: any) {
  return HTTP(`/learn/m1/knowledge/read?mapId=${mapId}`, {
    method: 'GET',
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  });
}

// 通过课程号查询地图id
export function getMapIdByCourseId(params: any) {
  return HTTP(`/learn/m1/knowledge/find/course/number/nodes`, {
    method: 'GET',
    params,
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  });
}

// 查询所有专业
export function getAllMajor(params: any) {
  return HTTP(`/unifiedplatform/v1/organization/collegemajor`, {
    method: 'GET',
    params,
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  });
}

// 查询所有课程
export function getAllCourse(data: any) {
  return HTTP(`/pyfa/v1/course/search`, {
    method: 'POST',
    data,
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  });
}

//查询课程基本信息
export function getCourseInfo(params: any) {
  return HTTP(`/pyfa/v1/course/detail`, {
    method: 'GET',
    params,
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  });
}

// 根据专业code查询课程
export function getCourseByMajorCode(params: any) {
  return HTTP(`/pyfa/v1/training_plan/detail`, {
    method: 'GET',
    params,
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  });
}

// 通过课程号查询图谱数据
export function getMapByCourseNumber(data: any) {
  return HTTP(`/learn/m1/knowledge/course/number/batch/query`, {
    method: 'POST',
    data,
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  });
}

// 复制地图
export function copyMap(params: any) {
  return HTTP(`/learn/m1/knowledge/copy/node`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

// 查询跨课的地图数据
export function getbrokenMap(data: any) {
  return HTTP(`/learn/m1/knowledge/course/find/nodes/broken/classes`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

// 删除资源学习记录
export function deleteResourceStudyRecord(mapId: any, nodeId: any, data: any) {
  return HTTP(
    `/learn/map/resource/study/batch/delete?mapId=${mapId}&nodeId=${nodeId}`,
    {
      method: 'POST',
      data,
    },
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

// 自动一键绑定试题
export function autoBindQuestion(data: any) {
  return HTTP(`/exam-api/knowledge/v2/bind/question`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

// 解除所有绑定的试题
export function unbindAllQuestion(data: any) {
  return HTTP(`/exam-api/knowledge/secure/bind/question`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

// 校验编号
export function checkNumber(params: any) {
  return HTTP(`/learn/m1/knowledge/course/verificationSerialNum`, {
    method: 'POST',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

// 修改知识点编号
export function updateNumber(params: any) {
  return HTTP(`/learn/m1/knowledge/course/uploadSerialNum`, {
    method: 'POST',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res;
      }
    })
    .catch(error => {
      return error;
    });
}

// 查询个人知识点学习 掌握率 和完成率
export function getKnowledgeStudyRate(data: any) {
  return HTTP(`/learn/map/resource/study/knowledge/personal/learning`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

// 查询知识点学习记录
export function getKnowledgeStudyRecord(params: any) {
  return HTTP(`/learn/map/resource/study/knowledge/personal/current/learning`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

// 查询图谱当前学习统计
export function getKnowledgeoverview(params: any) {
  return HTTP(`/learn/map/resource/study/knowledge/personal/overview`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

// 查询课程详情
export function getCoursedetail(params: any) {
  return HTTP(`/learn/v1/teaching/course/get/course/details`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

// 查询课程评分
export function getCourseScore(courseid: string) {
  return HTTP(`/learn/v1/evaluation/${courseid}`, {
    method: 'GET',
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

// 加入图谱课
export function joinMapCourse(data: any) {
  return HTTP(`/learn/map/resource/study/join`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

// 查询是否加入
export function getJoinMapCourse(params: any) {
  return HTTP(`/learn/map/resource/study/check/join`, {
    method: 'POST',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

// 记录当前学习到那个节点
export function recordCurrentNode(data: any) {
  return HTTP(`/learn/map/resource/study/knowledge/personal/current/learning`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

// 记录当前学习到那个节点
export function examinationAdd(data: any) {
  return HTTP(`/exam-api/examination/add/batch`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

// 复制节点把试题同步复制接口
export function copyNodeknowledge(data: any) {
  return HTTP(`/exam-api/knowledge/copy`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}


// 查询课程评分
export function checkjoin(params:any) {
  return HTTP(`/learn/map/resource/study/check/join`, {
    method: 'POST',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

// 查询能力图谱
export function getAbilityMap(params: any) {
  return HTTP(`/learn/v1/associate/course/graph/capability/list`, {
    method: 'POST',
    data:{
      grade:params.grade,
      majorCode:params.majorCode
    },
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

// 查询所需能力
export function getNeedAbility(params: any) {
  return HTTP(`/learn/v1/micro/profession/ability/require`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}


// 查询线下答疑
export function getOfflineAnswer(params: any) {
  return HTTP(`/learn/course-group/page`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

/** 查询微专业地图节点对应的能力要求 */
export function queryRequiresByMicromajor(params: {mapId?: string,mapContentId?: string }) {
  return HTTP(`/pyfa/v1/micro_major/map_content_ability_require`, {
    method: 'GET',
    params
  })
}

// 按照知识点获取能力要求权重
export function queryKnowledgeWeightByMicaro(data: {planId: string,nodeId?: string,courseId: string,courseSemester: number|string }) {
  return HTTP(`/learn/m1/reachStatistics/course/node/reachDetail`, {
    method: 'POST',
    data
  })
}

// 知识点绑定案例
export function addcaseinvoke(data: any) {
  return HTTP(`/medicalcaselib/caseinvoke/v1/addcaseinvoke`, {
    method: 'POST',
    data
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

// 查询当前登录人拥有的模块权限
export function getModulePermission(params: any) {
  return HTTP(`/learn/v1/teaching/course/get/microProfession/teacher/permission`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
}

// 查询当前登录人的待提交作业数量
export function getSubmitHomeworkCount(params: any) {
  return HTTP(`/exam-api/microSpecial/reviewed/nums`, {
    method: 'GET',
    params,
  })
}


// 查询回收站列表
export function getRecycleList(params: any) {
  return HTTP(`/learn/m1/knowledge/query/recycle/bin/page`, {
    method: 'GET',
    params
  })
}

// 还原地图
export function restoreMap(data: any) {
  return HTTP(`/learn/m1/knowledge/batch/restore`, {
    method: 'POST',
    data,
  })
}

// 清空回收站
export function clearRecycle() {
  return HTTP(`/learn/m1/knowledge/clear/data`, {
    method: 'GET'
  })
}

// 同步教学大纲内容
export function syncTeachingPlan(params: any) {
  return HTTP(`/pyfa/v1/thirdcourseoutline/getthirdcourseoutlinebycourseid`, {
    method: 'GET',
    params
  })
}

// 检索资源
export function searchResource(data: any) {
  return HTTP(`/rman/v1/search/bind/resources`, {
    method: 'POST',
    data
  })
}

// 检索知识点
export function searchKnowledge(data: any) {
  return HTTP(`/rman/v1/search/knowledge/point`, {
    method: 'POST',
    data,
  })
}

// 检索文档知识点
export function searchDocumentKnowledge(data: any) {
  return HTTP(`/rman/v1/search/document/knowledge/point`, {
    method: 'POST',
    data,
  })
}

// 检索知识要点
export function searchKnowledgepoint(data: any) {
  return HTTP(`/rman/v1/search/key/knowledge/point`, {
    method: 'POST',
    data,
  })
}

//批量查询删除的知识点对比辨析
export function getCompareAnalyze(mapId:string|number,data: any) {
  return HTTP(`/learn/discriminate/find/have/discriminate?mapId=${mapId}`, {
    method: 'POST',
    data
  })
}

// 查询canvas课程的教师和助教
export function getCanvasCourseTeam(mapid: string) {
  return HTTP(`/canvas-lms-adapter/Graph/GetCourseUsersByMapId?mapid=${mapid}`, {
    method: 'GET'
  })
}

// 查询canvas课程的学生
export function getCanvasCourseStudent(mapid: string) {
  return HTTP(`/canvas-lms-adapter/Graph/GetCourseStudents?course_id=${mapid}`, {
    method: 'GET'
  })
}

// 更新图谱的管理团队
export function updateMapTeamoverlay(data: any) {
  return HTTP(`/learn/v1/teaching/map/course/add/teacher/student`, {
    method: 'POST',
    data
  })
}

//查询资源知识要点
export const getRoucosePoint = (data: any) => {
  return HTTP(`/rman/v1/search/course/knowledge/graph`, {
    method: 'POST',
    data
  }) 
}

// 查询我的录播课程
export const getMyVideoCourse = (params: any) => {
  return HTTP(`/rman/v1/search/my/video`, {
    method: 'GET',
    params
  })
}

// 查询图谱关联的课程
export const getMapCourse = (params: any) => {
  return HTTP(`/learn/m1/course/map/get/course`, {
    method: 'GET',
    params
  })
}