{"name": "@wiris/mathtype-html-integration-devkit", "version": "1.7.1", "description": "Allows to integrate Mathtype Web into any JavaScript HTML WYSIWYG rich text editor.", "keywords": ["chem", "chemistry", "chemtype", "editor", "equation", "latex", "math", "mathml", "maths", "mathtype", "wiris"], "repository": "https://github.com/wiris/html-integrations/tree/stable/packages/mathtype-html-integration-devkit", "homepage": "https://www.wiris.com/", "bugs": {"email": "<EMAIL>"}, "license": "MIT", "author": "WIRIS Team (https://www.wiris.com)", "main": "core.src.js", "scripts": {"prebuild-jsdoc": "rm -rf doc/templates/mathtype-integration-jsdoc-theme && git clone https://github.com/wiris/mathtype-integration-jsdoc-theme.git doc/templates/mathtype-integration-jsdoc-theme", "build-jsdoc": "jsdoc -c doc/conf.json --verbose", "build": "webpack --mode production", "build-dev": "webpack --mode development", "clean": "shx rm -f core.js"}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/preset-env": "^7.15.0", "babel-loader": "^8.2.2", "coveralls": "^3.1.0", "css-loader": "^5.2.4", "jsdoc": "^3.6.6", "jsdoc-export-default-interop": "^0.3.1", "shx": "^0.3.3", "style-loader": "^3.3.0", "terser-webpack-plugin": "^5.3.0", "url-loader": "^4.1.1", "webpack": "^5.50.0", "webpack-cli": "^4.8.0"}, "dependencies": {"raw-loader": "^4.0.2", "uuid": "^8.3.2"}, "gitHead": "91307cd78406db4b4e4f53cc4828361162c9c405"}