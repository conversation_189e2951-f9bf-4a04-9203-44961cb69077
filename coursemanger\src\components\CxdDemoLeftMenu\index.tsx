import React, {useEffect, useState} from 'react';
import { Menu } from 'antd';
import './index.less';
import { IconFont } from '@/components/iconFont';
interface NavItem {
  key: string;
  icon: React.ReactNode;
  label: string;
  url: string;
  hash?: string;
  test?: string
}

const primaryNavItems: NavItem[] = [
  { key: 'home', icon:<IconFont type="iconmenu_home" className="icon" />, label: '主页', url: '/learn/workbench', hash: '/newHome'},
  { key: 'collection', icon: <IconFont type="iconmenu_rman" className="icon" />, label: '资源采集', url: '/rman', hash: '/basic/rmanCenterList' },
  { key: 'editing', icon: <IconFont type="iconmenu_joveone" className="icon" />, label: '资源编辑',url: '/learn/workbench', hash: '/courseEdit' },
  { key: 'run', icon: <IconFont type="iconmenu_course" className="icon" />, label: '教学运行',url: '/learn/workbench', hash: '/course' },
  { key: 'management', icon: <IconFont type="iconmenu_coursemap" className="icon" />, label: '教学管理', url: '/learn/workbench', hash: '/teachManage'
  },
];

const CxdDemoLeftMenu = () => {
  const [activePrimaryKey, setActivePrimaryKey] = useState('run');
  const jump = (url: string, key: string, hash?: string) => {
    // location.href = url;

    if (key === 'management' || key === 'editing') {
      console.log('新创了')
      const fullUrl = hash ? `${url}#${hash}` : url;
      window.open(fullUrl,'_blank');
    } else {
      setActivePrimaryKey(key)
      const fullUrl = hash ? `${url}#${hash}` : url;
      window.location.href = fullUrl;
    }
  }
  // function getKry() {
  //   if (location.href === '/course') {
  //     // setActivePrimaryKey('run')
  //     return 'run'
  //   } else if (location.href.includes('/basic/rmanCenterList')) {
  //     // setActivePrimaryKey('collection')
  //     return 'collection'
  //   }
  // }
  useEffect(() => {
    console.log(location.href)
    if (location.href === '/course') {
      setActivePrimaryKey('run')
    } else if (location.href.includes('/basic/rmanCenterList')) {
      setActivePrimaryKey('collection')
    }
  },[])
  return (
    <div className="cxd-composite-menu">
      <div className="cxd-primary-nav">
        {primaryNavItems.map((item) => (
          <div
            key={item.key}
            className={`nav-item ${activePrimaryKey === item.key ? 'active' : ''}`}
            onClick={() =>jump(item.url,item.key,item.hash) }
          >
            <div className="nav-icon">
              {item.icon}
            </div>
            <div className="nav-label">{item.label}</div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CxdDemoLeftMenu;
