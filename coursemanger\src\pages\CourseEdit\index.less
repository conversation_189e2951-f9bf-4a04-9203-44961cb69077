.resource-editor-bg {
  width: 100%;
  min-height: calc(100vh - 52px);
  //background: linear-gradient(135deg, #eaf1fb 0%, #f5f8fd 100%);
  padding: 40px 0;
  box-sizing: border-box;
  background: url("../../assets/imgs/homePage2/bg.png") no-repeat center center fixed;
  background-size: cover;
  display: flex;
  justify-content: center;
  align-items: center;
}

.resource-editor-title {
  position: absolute;
  left: 14px;
  top: -40px;
  font-size: 18px;
  font-weight: 600;
  color: #222;
  cursor: pointer;
}

.resource-editor-main {
  display: flex;
  justify-content: center;
  align-items: stretch; // 这里由 flex-start 改为 stretch
  gap: 32px;
  max-width: 1200px;
  margin: 0 auto;
  flex-wrap: wrap;
  //margin-top: 100px;
  padding: 16px;
}

.resource-editor-card {
  position: relative;
  flex: 1;
  background-color: rgba(252, 247, 247, 0.58); /* 灰 + 50% 透明 */
  border-radius: 20px;
  box-shadow: 0 4px 24px 0 rgba(0,0,0,0.04);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 40px 26px 32px 26px;
  box-sizing: border-box;
  &:hover{
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }

  .icon {
    //margin-bottom: 16px;
  }
  .title {
    font-size: 22px;
    font-weight: 600;
    color: #222;
    margin-bottom: 8px;
  }
  .desc {
    font-size: 15px;
    color: #999;
    line-height: 1.6;
  }
}
.resource-editor-card.big {
  flex: 1;
  align-items: flex-start;
  //.icon {
  //  margin-bottom: 24px;
  //}
  .title {
    font-size: 24px;
  }
  .desc {
    font-size: 15px;
    margin-top: 8px;
  }
}

.resource-editor-right {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  min-width: 420px;
  flex: 1;
  height: 100%; // 新增
}

.resource-editor-card.small {
  min-width: 180px;
  min-height: 140px;
  //.icon {
  //  margin-bottom: 12px;
  //}
  .title {
    font-size: 20px;
  }
  .desc {
    font-size: 14px;
  }
}

/* 响应式适配 */
@media (max-width: 900px) {
  .resource-editor-main {
    flex-direction: column;
    align-items: center;
    gap: 24px;
  }
  .resource-editor-right {
    min-width: 0;
    width: 100%;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }
  .resource-editor-card.big {
    width: 100%;
    min-width: 0;
    min-height: 220px;
  }
}

@media (max-width: 600px) {
  .resource-editor-bg {
    padding: 16px 0;
  }
  .resource-editor-title {
    margin-left: 16px;
    font-size: 16px;
    margin-bottom: 16px;
    cursor: pointer;
  }
  .resource-editor-main {
    gap: 12px;
  }
  .resource-editor-right {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  .resource-editor-card {
    padding: 24px 16px 16px 16px;
    border-radius: 14px;
  }
  .resource-editor-card.big {
    min-height: 140px;
    .title {
      font-size: 18px;
    }
  }
  .resource-editor-card.small {
    min-height: 80px;
    .title {
      font-size: 16px;
    }
    .desc {
      font-size: 12px;
    }
  }
}
