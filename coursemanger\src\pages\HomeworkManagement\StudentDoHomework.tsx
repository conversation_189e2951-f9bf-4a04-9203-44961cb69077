import {
  getLearnStatus,
  getStuHomeworkDetail,
  getStuSubRecord,
  helpSubmitRecord,
  queryHomeworkFallback,
  submitMicroMajorHomework,
  submitRecord,
  submitRecordAndUpdateStatus,
  updateLearnStatus,
  updateReadFallback,
} from '@/api/homework';
import FilePreviewModal from '@/components/FilePreviewModal';
import useLocale from '@/hooks/useLocale';
import { getSensitiveWord } from '@/utils';
import { LeftOutlined, WarningOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { Button, Checkbox, Modal, Space, Spin, message } from 'antd';
import $ from 'jquery';
import moment from 'moment';
import React, { FC, useEffect, useRef, useState } from 'react';
import { useDispatch, useLocation, useSelector } from 'umi';
import './StudentDoHomework.less';
import AnswerCard from './components/AnswerCard';
import CommonHeader from './components/CommonHeader';
import HomeworkSubItem from './components/HomeworkSubItem';
import ImageModal from './components/ImageModal';
import { getPreviewType, handleDownload } from './utils/tools';

interface IStudentDoHomework {
  handleBack: (isRefresh?: boolean) => void;
  stuCode?: string;
  homeworkItem: any;
}

const StudentDoHomework: FC<IStudentDoHomework> = ({
  handleBack,
  stuCode,
  homeworkItem,
}) => {
  const [formType, setFromType] = useState<string>('')
  const [ckeckAll, setCheckAll] = useState<boolean>(false)
  const { t } = useLocale();
  const dispatch = useDispatch();
  const location: any = useLocation();
  const [loading, setLoading] = useState<boolean>(false);
  const [topicData, setTopicData] = useState<any>([]);
  const [openParse, setOpenParse] = useState<boolean>(false);
  const [timeOut, setTimeOut] = useState<boolean>(false);
  const [subRecord, setSubRecord] = useState<any>({});
  const [resubmit, setResubmit] = useState<boolean>(false);
  const [isEnd, setIsEnd] = useState<boolean>(false);
  const [scores, setScores] = useState<any>({});
  const [comment, setComment] = useState<string>('');
  const [comments, setComments] = useState<any>({}); // 教师多个评语
  const [fallbackData, setFallbackData] = useState<any>({});
  const [fallbackVisible, setFallbackVisible] = useState<boolean>(false);
  const [id, setId] = useState<string>(''); // 作业的唯一id
  const [curImage, setCurImage] = useState<string>('');
  const [imageVisible, setImageVisible] = useState<boolean>(false);
  const timeoutRef = useRef<any>(null);
  const [classEnding, setClassEnding] = useState<boolean>(false);
  const [answerCorrect, setAnswerCorrect] = useState<any>({});
  const [teacherFiles, setTeacherFiles] = useState<any>({});
  const files = useSelector<any, any>(state => state.homework.files);

  const { userInfo } = useSelector<any, any>(state => state.global);

  // const submitMicroHomework = (data: any) => {
  //   submitRecordAndUpdateStatus(data).then((res: any) => {
  //     message.success('作业提交成功');
  //     handleBack(true);
  //     cancel();
  //   })
  // }

  const handleTempSave = (state: 1 | 2, showMessage: boolean = false) => {
    let answers: any = {};
    Object.keys(subRecord).forEach((key: any) => {
      answers[key] = subRecord[key]?.filter(
        (item: any) => item !== '' && item != null,
      );
    });
    let submitExamResource: any = {
      answers: answers,
      homeworkId: id,
      submitState: state,
      hasAttachments: files,
    };
    stuCode ? (submitExamResource.stuCode = stuCode) : '';
    const params = {
      courseId: location.query.id,
      courseType: handleQueryType(),
      chapterId: homeworkItem.chapterId,
      sectionId: homeworkItem.sectionId,
      subsectionId: homeworkItem.guid,
      status: 2,
      resourceType: 'homework',
      userCode: userInfo.userCode,
      courseSemester: location.query.semester,
      homeWorkType: location.query.type.includes('microMajor')? 2: stuCode? 1: 0,//作业提交类型 0学生提交 1老师代交 2微专业提交
      submitExamResource
    }
    // if (location.query.type.includes('microMajor') && state == 2) {
    //   return submitMicroHomework(params)
    // }
    let func
    if(params.chapterId && state == 2){
      //有章节信息使用新接口提交
      func = submitRecordAndUpdateStatus(params)
    }else{
      //无章节信息保留原来的逻辑
      func = stuCode ? helpSubmitRecord(submitExamResource) : submitRecord(submitExamResource);
    }
    return func.then((res: any) => {
        if (res.status === 200) {
          // 只在手动暂存时显示提示
          if(showMessage) {
            message.success(`${state == 1 ? t('已保存') : t('提交成功')}`);
          }
          // 只有在点击提交(state=2)时才退出编辑模式
          if (state == 2) {
            dispatch({
              type: 'homework/handlePublicChange',
              payload: {
                files: {},
                oldFiles: {},
                stuCode: null,
              },
            });
            cancel();
            // HandleCompleteHomework();
            handleBack(true);
          }
        } else {
          message.error(res.message);
        }
      });
  };
  const { run, cancel } = useRequest(handleTempSave as any, {
    pollingInterval: 30000,
    manual: true,
    pollingWhenHidden: false,
    pollingErrorRetryCount: 3,
  });
  useEffect(() => {
    $(document).on('click', '.homework-sub-item img', (e: any) => {
      const src = e.target.getAttribute('data-annotate-src');
      if (src) {
        setCurImage(src);
      } else {
        setCurImage(e.target.src);
      }

      setImageVisible(true);
    });
    return () => {
      clearTimeout(timeoutRef.current);
    };
  }, []);
  useEffect(() => {
    if (homeworkItem.id) {
      handleGetHomeworkDetail();
      const now = Date.now();
      setIsEnd(now >= homeworkItem.closeTime);
    }
  }, [homeworkItem]);

  useEffect(() => {
    if (id) {
      getFallback();
      getSubmission();
    }
  }, [id]);

  const getFallback = () => {
    console.info(homeworkItem);
    queryHomeworkFallback({
      courseId: location.query.id,
      homeworkId: id,
      parentId: homeworkItem.parentId,
      userCode: userInfo.userCode,
    }).then((res: any) => {
      if (res.status === 200) {
        setFallbackData(res.data ?? {});
        if (res.data.status === 0) {
          setFallbackVisible(true);
        }
      }
    });
  };

  const handleGetHomeworkDetail = () => {
    setLoading(true);
    getStuHomeworkDetail(
      homeworkItem.id,
      homeworkItem.parentId,
      location.query.id,
    ).then((res: any) => {
      if (res.status === 200) {
        setId(res.data.id); // homeworkId 唯一
        setClassEnding(res.data?.classEnding ?? false);
        dispatch({
          type: 'homework/handlePublicChange',
          payload: {
            homeworkData: { ...homeworkItem, realId: res.data.id },
          },
        });
        setOpenParse(res.data.openParse);
        setTimeOut(res.data.timeOut);
        setResubmit(res.data.resubmit);
        const topicQuestionList = res.data.questions.map((item: any, index: number) => {
          item.question?.questions_options?.map(
            (item_: any, index_: number) => {
              item_.seq = index_ + 1;
              item_.content = item_.content;
            },
          );
          return {
            serialNo: index + 1,
            ...item.question,
            topicId: item.question?.id,
            ...item,
          };
        })
        console.info('topicQuestionList', topicQuestionList);
        setTopicData(topicQuestionList);
      }
      setLoading(false);
    });
  };

  const getSubmission = () => {
    setLoading(true);
    getStuSubRecord(id, location.query.id, stuCode)
      .then((res: any) => {
        if (res.status === 200) {
          if (homeworkItem.submitState !== 2 && !isEnd && !classEnding) {
            // 自动暂存时不显示提示,传入showMessage=false
            timeoutRef.current = setTimeout(() => {
              run(1, false);
            }, 30000);
          }
          setSubRecord(res.data.answers ?? {});
          const temp: any = {};
          Reflect.ownKeys(res.data.answersExtends ?? {}).forEach((key: any) => {
            temp[key] = res.data.answersExtends[key]?.map(
              (item: any) => item.correct,
            );
          });
          setAnswerCorrect(temp);
          setScores(res.data.scores ?? {});
          setComment(res.data.comment ?? '');
          setComments(res.data.question_comments ?? {});
          setTeacherFiles(res.data?.teacherAttachment ?? {});
          dispatch({
            type: 'homework/handlePublicChange',
            payload: {
              files: res.data.hasAttachments ?? {},
              oldFiles: res.data.hasAttachments ?? {},
              recordData: res.data,
            },
          });
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };
  const handleAnswerChange = (value: string | string[], id: string) => {
    setSubRecord((pre: any) => {
      return { ...pre, [id]: value };
    });
  };

  const handleSubmit = (state: 1 | 2) => {
    getSensitiveWord(
      Object.values(subRecord)
        ?.join()
        ?.replace(RegExp('<.+?>', 'g'), '') ?? '',
      '答案',
      () => {
        // 手动点击暂存/提交按钮时,传入showMessage=true显示提示
        handleTempSave(state, true);
      },
      () => setLoading(false),
    );
  };
  function handleQueryType() {
    const isArray = Array.isArray(location.query.type);
    return isArray ? location.query.type[0] : location.query.type;
  }
  // const HandleCompleteHomework = () => {
  //   getLearnStatus({
  //     userCode: stuCode ?? null,
  //     courseId: location.query.id,
  //     subsectionId: homeworkItem.guid,
  //   }).then(res => {
  //     if (res.status === 200) {
  //       const data: any = {
  //         courseId: location.query.id,
  //         courseType: handleQueryType(),
  //         subsectionId: homeworkItem.guid,
  //         status: 2,
  //         userCode: stuCode ?? null,
  //       };
  //       if (res.data?.id) data.id = res.data.id;
  //       updateLearnStatus(data).then(res => {
  //         if (res.status !== 200) {
  //           message.error(res.message);
  //         }
  //       });
  //     }
  //   });
  // };

  const fallbackConfirm = () => {
    updateReadFallback({ id: fallbackData.id }).then((res: any) => {
      if (res.status === 200) {
        setFallbackVisible(false);
      }
    });
  };
  const [previewVisible, setPreviewVisible] = useState<boolean>(false);
  const [currentFile, setCurrentFile] = useState<any>({});
  const { fileMap } = useSelector<Models.Store, any>(state => state.global);

  const handlePreview = (file: any) => {
    const { attachmentSource, attachmentName } = file;
    setCurrentFile({
      filePath: attachmentSource,
      type: getPreviewType(attachmentSource, fileMap),
      extraData: attachmentName,
    });
    setPreviewVisible(true);
  };

  return (
    <Spin spinning={loading}>
      <div className="student-do-homework">
        <AnswerCard
          answers={subRecord}
          questions={topicData}
          scores={scores}
          comment={comment}
          correctObj={answerCorrect}
        />
        <div className="box">
          <div className="header-container">
            <div
              className="back-btn"
              onClick={() => {
                if (homeworkItem.submitState !== 2 && !isEnd && !classEnding) {
                  // if ((resubmit || homeworkItem.submitState !== 2) && !isEnd || stuCode) {
                  handleTempSave(1);
                  cancel();
                }
                handleBack();
                dispatch({
                  type: 'homework/handlePublicChange',
                  payload: {
                    files: {},
                    oldFiles: {},
                  },
                });
              }}
            >
              <LeftOutlined />
              {t('返回')}
            </div>
          </div>
          <CommonHeader showFrom homeworkItem={homeworkItem} />
          {
            formType == 'mapHomework' ? <div>已答题：
              <span className='done_ques'>{Object.keys(subRecord).length}</span>/
              <span>{topicData.length}</span></div> :
              <div className="tips">
                {t('* 此次作业在截止日期前')}
                {resubmit ? t('可重复提交') : t('只允许提交一次')}
              </div>
          }
          {
            formType == 'mapHomework' ? <Space style={{ margin: '10px 0' }}>
              <Checkbox onChange={(val) => setCheckAll(val.target.checked)}>全选</Checkbox>
              <Button>提交</Button>
            </Space> : null
          }
        </div>
        <div className="content-container">
          {topicData.map((item: any, index: number) => (
            <HomeworkSubItem
              comment={comments[item.id]}
              score={scores[item.id]}
              key={index}
              // 可以超时，只提交一次，已经提交 多次提交，
              // 时间到了 只提交一次且已提交
              isEnd={(isEnd && ((!timeOut || homeworkItem.submitState === 2) || (homeworkItem.submitState === 2 && !resubmit))) || classEnding}
              files={files[item.id]}
              canEdit={resubmit || homeworkItem.submitState !== 2}
              isCorrect={answerCorrect[item.id]}
              openParse={openParse}
              index={index + 1}
              data={item}
              answer={subRecord?.[item.id]}
              teacherFiles={teacherFiles?.[item.id]}
              onChange={handleAnswerChange}
              onDownload={handleDownload}
              onPreview={handlePreview}
              onImagePreview={(src: string) => {
                setCurImage(src);
                setImageVisible(true);
              }}
              fromType={formType}
            />
          ))}
        </div>
        <div className="btn-container">
          {(((resubmit || homeworkItem.submitState !== 2) &&
            (!isEnd || (timeOut && homeworkItem.submitState !== 2)) &&
            !classEnding) ||
            stuCode) && (
              <Space>
                <Button type="primary" onClick={() => handleSubmit(2)}>
                  {t('提交')}
                </Button>
                {homeworkItem.submitState !== 2 && (
                  <Button type="primary" ghost onClick={() => handleSubmit(1)}>
                    {t('暂存')}
                  </Button>
                )}
              </Space>
            )}
        </div>
        <Modal
          title={t('退回作业')}
          wrapClassName="stu-fallback-modal"
          open={fallbackVisible}
          footer={null}
          closable={false}
        >
          <p>
            <WarningOutlined />
            {t('您的作业被老师退回了')}
          </p>
          <p>
            {t('请在')}
            <span>
              {moment(fallbackData.closeTime).format('YYYY-MM-DD HH:mm')}
            </span>
            {t('前重新完成提交')}
          </p>
          {fallbackData.reason && (
            <p>
              {t('退回原因：')}
              {fallbackData.reason}
            </p>
          )}
          <div className="btn-wrp">
            <Button onClick={fallbackConfirm} type="primary">
              {t('确定')}
            </Button>
          </div>
        </Modal>
        <FilePreviewModal
          visible={previewVisible}
          onClose={() => setPreviewVisible(false)}
          file={currentFile}
          fileType={currentFile.type}
        />
        <ImageModal
          image={curImage}
          visible={imageVisible}
          onClose={() => setImageVisible(false)}
        />
      </div>
    </Spin>
  );
};

export default StudentDoHomework;
