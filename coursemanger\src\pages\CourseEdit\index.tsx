import React from "react";
import "./index.less";
import AI from '/src/assets/imgs/HomeNav/AIAgent.png'
import Edit from '/src/assets/imgs/HomeNav/gongjuxiang.png'
import Map from "/src/assets/imgs/HomeNav/zhishiditu.png"
import KeCheng from "/src/assets/imgs/HomeNav/kecheng.png"
import KeCheng2 from "/src/assets/imgs/HomeNav/kecheng2.png"
import {LeftOutlined} from "@ant-design/icons";
import Header from "@/components/Header";
const cards = [
  {
    title: "智能体",
    desc: "模块文字介绍模块文字介绍模块文字",
    icon: (
      <svg width="40" height="40" viewBox="0 0 40 40">
        <circle cx="20" cy="20" r="20" fill="#A48DFF" />
        <text x="20" y="26" textAnchor="middle" fontSize="18" fill="#fff">AI</text>
      </svg>
    ),
    img:AI,
    href: '/learn/workbench/#/agent'
  },
  {
    title: "我的图谱",
    desc: "模块文字介绍模块文字介绍模块文字",
    icon: (
      <svg width="40" height="40" viewBox="0 0 40 40">
        <circle cx="20" cy="20" r="20" fill="#FF8B3D" />
        <circle cx="20" cy="16" r="6" fill="#fff"/>
        <rect x="17" y="22" width="6" height="8" rx="3" fill="#fff"/>
      </svg>
    ),
    img: Map,
    href: '/learn/workbench/#/coursemap/minemap'
  },
  {
    title: "编辑工具",
    desc: "模块文字介绍模块文字介绍模块文字",
    icon: (
      <svg width="40" height="40" viewBox="0 0 40 40">
        <circle cx="20" cy="20" r="20" fill="#3DD598" />
        <rect x="12" y="18" width="16" height="10" rx="2" fill="#fff"/>
        <rect x="18" y="12" width="4" height="8" rx="2" fill="#fff"/>
      </svg>
    ),
    img: Edit,
    href: '/aitools/#/overview'
  },
  {
    title: "课程出版",
    desc: "模块文字介绍模块文字介绍模块文字",
    icon: (
      <svg width="40" height="40" viewBox="0 0 40 40">
        <circle cx="20" cy="20" r="20" fill="#2D8CFF" />
        <rect x="12" y="12" width="16" height="16" rx="2" fill="#fff"/>
        <rect x="16" y="16" width="8" height="8" rx="1" fill="#2D8CFF"/>
      </svg>
    ),
    img: KeCheng,
    href: '/learn/workbench/#/coursetemplate/mytemplate'
  },
];

export default function CourseEdit() {
  function goToReturn() {
    console.log(111)
    location.href = '/learn/workbench/#/newHome'
  }
  return (
    <>
    <Header />
    <div className="resource-editor-bg">
      <div className="resource-editor-main" style={{ position: "relative" }}>
        <div className="resource-editor-title" onClick={goToReturn}><LeftOutlined />资源编辑</div>
        <a className="resource-editor-card big" href='/learn/workbench/#/coursetemplate/mytemplate' target='_blank'>
          <div>
            <div className="icon">
              <img width="56" height="56" src={KeCheng2} />
            </div>
            <div className="title">我的课程</div>
            <div className="desc">模块文字介绍模块文字介绍模块文字介绍模块文字介绍模块文字介绍</div>
          </div>
        </a>
        <div className="resource-editor-right">
          {cards.map((item, idx) => (
            <a href={item.href} target="_blank" key={idx}>
              <div className="resource-editor-card small" >
                <div className="icon">
                  <img src={item.img} />
                </div>
                <div className="title">{item.title}</div>
                <div className="desc">{item.desc}</div>
              </div>
            </a>
          ))}
        </div>
      </div>
    </div>
    </>
  );
}
