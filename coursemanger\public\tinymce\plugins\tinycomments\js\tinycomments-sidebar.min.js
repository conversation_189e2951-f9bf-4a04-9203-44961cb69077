/* Tiny Comments plugin
*
* Copyright 2010-2020 Tiny Technologies Inc. All rights reserved.
*
* Version: 2.3.0-117
*/
!function(){"use strict";function h(){}function i(t,r){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t(r.apply(null,n))}}function y(n){return function(){return n}}function f(n){return n}function d(r){for(var o=[],n=1;n<arguments.length;n++)o[n-1]=arguments[n];return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var t=o.concat(n);return r.apply(null,t)}}function u(n){return function(){throw new Error(n)}}var s=y(!1),v=y(!0),_=y("dismiss.popups"),l=y("reposition.popups"),n=function(){return c},c={fold:function(n,e){return n()},is:s,isSome:s,isNone:v,getOr:r,getOrThunk:t,getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:y(null),getOrUndefined:y(void 0),or:r,orThunk:t,map:n,each:h,bind:n,exists:s,forall:v,filter:n,equals:e,equals_:e,toArray:function(){return[]},toString:y("none()")};function e(n){return n.isNone()}function t(n){return n()}function r(n){return n}function o(n){if(null===n)return"null";if(void 0===n)return"undefined";var e=typeof n;return"object"==e&&(Array.prototype.isPrototypeOf(n)||n.constructor&&"Array"===n.constructor.name)?"array":"object"==e&&(String.prototype.isPrototypeOf(n)||n.constructor&&"String"===n.constructor.name)?"string":e}function a(o){return j(function(n,e){if(n.length!==e.length)return!1;for(var t=n.length,r=0;r<t;r++)if(!o.eq(n[r],e[r]))return!1;return!0})}function m(n,t){return r=a(n),o=function(n){return e=n,n=t,Array.prototype.slice.call(e).sort(n);var e},j(function(n,e){return r.eq(o(n),o(e))});var r,o}function g(n){return!(null==n)}function w(n,e){return n=n,e=e,-1<Q.call(n,e)}function p(n,e){for(var t=0,r=n.length;t<r;t++){if(e(n[t],t))return!0}return!1}function x(n,e){for(var t=n.length,r=new Array(t),o=0;o<t;o++){var i=n[o];r[o]=e(i,o)}return r}function O(n,e){for(var t=0,r=n.length;t<r;t++){e(n[t],t)}}function S(n,e){for(var t=[],r=0,o=n.length;r<o;r++){var i=n[r];e(i,r)&&t.push(i)}return t}function b(n,e,t){return function(n,e){for(var t=n.length-1;0<=t;t--){e(n[t],t)}}(n,function(n){t=e(t,n)}),t}function E(n,e,t){return O(n,function(n){t=e(t,n)}),t}function k(n,e){return function(n,e,t){for(var r=0,o=n.length;r<o;r++){var i=n[r];if(e(i,r))return H.some(i);if(t(i,r))break}return H.none()}(n,e,s)}function C(n,e){for(var t=0,r=n.length;t<r;t++){if(e(n[t],t))return H.some(t)}return H.none()}function T(n){for(var e=[],t=0,r=n.length;t<r;++t){if(!G(n[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+n);$.apply(e,n[t])}return e}function M(n,e){return T(x(n,e))}function D(n,e){for(var t=0,r=n.length;t<r;++t){if(!0!==e(n[t],t))return!1}return!0}function A(n){return(n=J.call(n,0)).reverse(),n}function F(n,e){return S(n,function(n){return!w(e,n)})}function R(n){return 0===n.length?H.none():H.some(n[0])}function N(n,e){for(var t=0;t<n.length;t++){var r=e(n[t],t);if(r.isSome())return r}return H.none()}var B=function(t){function n(){return o}function e(n){return n(t)}var r=y(t),o={fold:function(n,e){return e(t)},is:function(n){return t===n},isSome:v,isNone:s,getOr:r,getOrThunk:r,getOrDie:r,getOrNull:r,getOrUndefined:r,or:n,orThunk:n,map:function(n){return B(n(t))},each:function(n){n(t)},bind:e,exists:e,forall:e,filter:function(n){return n(t)?o:c},toArray:function(){return[t]},toString:function(){return"some("+t+")"},equals:function(n){return n.is(t)},equals_:function(n,e){return n.fold(s,function(n){return e(t,n)})}};return o},H={some:B,none:n,from:function(n){return null==n?c:B(n)}},P=function(n){if(null==n)throw new Error("Node cannot be null or undefined");return{dom:n}},I={fromHtml:function(n,e){e=(e||document).createElement("div");if(e.innerHTML=n,!e.hasChildNodes()||1<e.childNodes.length)throw console.error("HTML does not have a single root node",n),new Error("HTML must have a single root node");return P(e.childNodes[0])},fromTag:function(n,e){n=(e||document).createElement(n);return P(n)},fromText:function(n,e){n=(e||document).createTextNode(n);return P(n)},fromDom:P,fromPoint:function(n,e,t){return H.from(n.dom.elementFromPoint(e,t)).map(P)}},j=function(n){return{eq:n}},L=j(function(n,e){return n===e}),V=j(function(n,e){if(n===e)return!0;var c,t=o(n);return t===o(e)&&(-1!==["undefined","boolean","number","string","function","xml","null"].indexOf(t)?n===e:"array"===t?a(V).eq(n,e):"object"===t&&(c=V,j(function(n,e){var t=Object.keys(n),r=Object.keys(e);if(!m(L).eq(t,r))return!1;for(var o=t.length,i=0;i<o;i++){var u=t[i];if(!c.eq(n[u],e[u]))return!1}return!0}).eq(n,e)))}),W=function(t){return function(n){return n=typeof(e=n),(null===e?"null":"object"==n&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"==n&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":n)===t;var e}},U=function(e){return function(n){return typeof n===e}},z=W("string"),q=W("object"),G=W("array"),Y=U("boolean"),K=U("function"),X=U("number"),J=Array.prototype.slice,Q=Array.prototype.indexOf,$=Array.prototype.push,Z=function(){return(Z=Object.assign||function(n){for(var e,t=1,r=arguments.length;t<r;t++)for(var o in e=arguments[t])Object.prototype.hasOwnProperty.call(e,o)&&(n[o]=e[o]);return n}).apply(this,arguments)};function nn(n,e){var t={};for(o in n)Object.prototype.hasOwnProperty.call(n,o)&&e.indexOf(o)<0&&(t[o]=n[o]);if(null!=n&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(n);r<o.length;r++)e.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(n,o[r])&&(t[o[r]]=n[o[r]]);return t}function en(){for(var n=0,e=0,t=arguments.length;e<t;e++)n+=arguments[e].length;for(var r=Array(n),o=0,e=0;e<t;e++)for(var i=arguments[e],u=0,c=i.length;u<c;u++,o++)r[o]=i[u];return r}function tn(t){var r,o=!1;return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return o||(o=!0,r=t.apply(null,n)),r}}function rn(n,e){var t=String(e).toLowerCase();return k(n,function(n){return n.search(t)})}function on(n,e){return-1!==n.indexOf(e)}function un(n){return window.matchMedia(n).matches}function cn(n,e){if(1!==(n=n.dom).nodeType)return!1;if(void 0!==n.matches)return n.matches(e);if(void 0!==n.msMatchesSelector)return n.msMatchesSelector(e);if(void 0!==n.webkitMatchesSelector)return n.webkitMatchesSelector(e);if(void 0!==n.mozMatchesSelector)return n.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")}function an(n){return 1!==n.nodeType&&9!==n.nodeType&&11!==n.nodeType||0===n.childElementCount}function sn(n,e){return n.dom===e.dom}function fn(n){return n.dom.nodeName.toLowerCase()}function ln(n){return I.fromDom(n.dom.ownerDocument)}function dn(n){return ne(n)?n:ln(n)}function mn(n){return I.fromDom(dn(n).dom.defaultView)}function gn(n){return H.from(n.dom.parentNode).map(I.fromDom)}function pn(n){return x(n.dom.childNodes,I.fromDom)}function hn(n,e){return n=n.dom.childNodes,H.from(n[e]).map(I.fromDom)}function vn(n){return hn(n,0)}function bn(n){return n=ie(n),ee(n)?H.some(n):H.none()}function yn(n){return I.fromDom(n.dom.host)}function _n(n){function e(){return n.stopPropagation()}function t(){return n.preventDefault()}var r=I.fromDom(function(n){if(oe()&&g(n.target)){var e=I.fromDom(n.target);if($n(e)&&ue(e)&&n.composed&&n.composedPath){e=n.composedPath();if(e)return R(e)}}return H.from(n.target)}(n).getOr(n.target)),o=i(t,e);return{target:r,x:n.clientX,y:n.clientY,stop:e,prevent:t,kill:o,raw:n}}function wn(n,e,t,r,o){var i,u,r=(i=t,u=r,function(n){i(n)&&u(_n(n))});return n.dom.addEventListener(e,r,o),{unbind:d(ce,n,e,r,o)}}function xn(n,e,t){return wn(n,e,ae,t,!1)}function On(n,e,t){return wn(n,e,ae,t,!0)}function Sn(e,t){gn(e).each(function(n){n.dom.insertBefore(t.dom,e.dom)})}function En(e,t){vn(e).fold(function(){se(e,t)},function(n){e.dom.insertBefore(t.dom,n.dom)})}var kn,Cn,Tn=function(){return Mn(0,0)},Mn=function(n,e){return{major:n,minor:e}},Dn={nu:Mn,detect:function(n,e){e=String(e).toLowerCase();return 0===n.length?Tn():function(n,e){var t=function(n,e){for(var t=0;t<n.length;t++){var r=n[t];if(r.test(e))return r}}(n,e);if(!t)return{major:0,minor:0};n=function(n){return Number(e.replace(t,"$"+n))};return Mn(n(1),n(2))}(n,e)},unknown:Tn},An=function(n,t){return rn(n,t).map(function(n){var e=Dn.detect(n.versionRegexes,t);return{current:n.name,version:e}})},Fn=function(n,t){return rn(n,t).map(function(n){var e=Dn.detect(n.versionRegexes,t);return{current:n.name,version:e}})},Rn=(kn=/^\s+|\s+$/g,function(n){return n.replace(kn,"")}),Nn=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Bn=function(e){return function(n){return on(n,e)}},Hn=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(n){return on(n,"edge/")&&on(n,"chrome")&&on(n,"safari")&&on(n,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Nn],search:function(n){return on(n,"chrome")&&!on(n,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(n){return on(n,"msie")||on(n,"trident")}},{name:"Opera",versionRegexes:[Nn,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Bn("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Bn("firefox")},{name:"Safari",versionRegexes:[Nn,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(n){return(on(n,"safari")||on(n,"mobile/"))&&on(n,"applewebkit")}}],Pn=[{name:"Windows",search:Bn("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(n){return on(n,"iphone")||on(n,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Bn("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:Bn("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Bn("linux"),versionRegexes:[]},{name:"Solaris",search:Bn("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Bn("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Bn("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],In={browsers:y(Hn),oses:y(Pn)},jn="Firefox",Ln=function(n){var e=n.current,t=n.version,n=function(n){return function(){return e===n}};return{current:e,version:t,isEdge:n("Edge"),isChrome:n("Chrome"),isIE:n("IE"),isOpera:n("Opera"),isFirefox:n(jn),isSafari:n("Safari")}},Vn={unknown:function(){return Ln({current:void 0,version:Dn.unknown()})},nu:Ln,edge:y("Edge"),chrome:y("Chrome"),ie:y("IE"),opera:y("Opera"),firefox:y(jn),safari:y("Safari")},Wn="Windows",Un="Android",zn="Solaris",qn="FreeBSD",Gn="ChromeOS",Yn=function(n){var e=n.current,t=n.version,n=function(n){return function(){return e===n}};return{current:e,version:t,isWindows:n(Wn),isiOS:n("iOS"),isAndroid:n(Un),isOSX:n("OSX"),isLinux:n("Linux"),isSolaris:n(zn),isFreeBSD:n(qn),isChromeOS:n(Gn)}},Kn={unknown:function(){return Yn({current:void 0,version:Dn.unknown()})},nu:Yn,windows:y(Wn),ios:y("iOS"),android:y(Un),linux:y("Linux"),osx:y("OSX"),solaris:y(zn),freebsd:y(qn),chromeos:y(Gn)},Xn=function(n,e){var t,r,o=In.browsers(),i=In.oses(),u=An(o,n).fold(Vn.unknown,Vn.nu),c=Fn(i,n).fold(Kn.unknown,Kn.nu);return{browser:u,os:c,deviceType:(t=u,r=n,o=e,u=(i=c).isiOS()&&!0===/ipad/i.test(r),n=i.isiOS()&&!u,e=i.isiOS()||i.isAndroid(),c=e||o("(pointer:coarse)"),o=u||!n&&e&&o("(min-device-width:768px)"),e=n||e&&!o,t=t.isSafari()&&i.isiOS()&&!1===/safari/i.test(r),r=!e&&!o&&!t,{isiPad:y(u),isiPhone:y(n),isTablet:y(o),isPhone:y(e),isTouch:y(c),isAndroid:i.isAndroid,isiOS:i.isiOS,isWebView:y(t),isDesktop:y(r)})}},Jn=tn(function(){return Xn(navigator.userAgent,un)}),Qn=("undefined"!=typeof window||Function("return this;")(),function(e){return function(n){return n.dom.nodeType===e}}),$n=Qn(1),Zn=Qn(3),ne=Qn(9),ee=Qn(11),te=gn,re=K(Element.prototype.attachShadow)&&K(Node.prototype.getRootNode),oe=y(re),ie=re?function(n){return I.fromDom(n.dom.getRootNode())}:dn,ue=function(n){return g(n.dom.shadowRoot)},ce=function(n,e,t,r){n.dom.removeEventListener(e,t,r)},ae=v,se=function(n,e){n.dom.appendChild(e.dom)};(rc=Cn=Cn||{})[rc.Modern=0]="Modern",rc[rc.Silver=1]="Silver";function fe(n){var e=n;return{get:function(){return e},set:function(n){e=n}}}var le,de,me,ge,pe={},he={exports:pe};de=pe,me=he,ge=le=void 0,function(n){"object"==typeof de&&void 0!==me?me.exports=n():"function"==typeof le&&le.amd?le([],n):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).EphoxContactWrapper=n()}(function(){return function r(o,i,u){function c(e,n){if(!i[e]){if(!o[e]){var t="function"==typeof ge&&ge;if(!n&&t)return t(e,!0);if(a)return a(e,!0);throw(t=new Error("Cannot find module '"+e+"'")).code="MODULE_NOT_FOUND",t}t=i[e]={exports:{}},o[e][0].call(t.exports,function(n){return c(o[e][1][n]||n)},t,t.exports,r,o,i,u)}return i[e].exports}for(var a="function"==typeof ge&&ge,n=0;n<u.length;n++)c(u[n]);return c}({1:[function(n,e,t){var r,o,e=e.exports={};function i(){throw new Error("setTimeout has not been defined")}function u(){throw new Error("clearTimeout has not been defined")}function c(e){if(r===setTimeout)return setTimeout(e,0);if((r===i||!r)&&setTimeout)return r=setTimeout,setTimeout(e,0);try{return r(e,0)}catch(n){try{return r.call(null,e,0)}catch(n){return r.call(this,e,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:i}catch(n){r=i}try{o="function"==typeof clearTimeout?clearTimeout:u}catch(n){o=u}}();var a,s=[],f=!1,l=-1;function d(){f&&a&&(f=!1,a.length?s=a.concat(s):l=-1,s.length&&m())}function m(){if(!f){var n=c(d);f=!0;for(var e=s.length;e;){for(a=s,s=[];++l<e;)a&&a[l].run();l=-1,e=s.length}a=null,f=!1,function(e){if(o===clearTimeout)return clearTimeout(e);if((o===u||!o)&&clearTimeout)return o=clearTimeout,clearTimeout(e);try{o(e)}catch(n){try{return o.call(null,e)}catch(n){return o.call(this,e)}}}(n)}}function g(n,e){this.fun=n,this.array=e}function p(){}e.nextTick=function(n){var e=new Array(arguments.length-1);if(1<arguments.length)for(var t=1;t<arguments.length;t++)e[t-1]=arguments[t];s.push(new g(n,e)),1!==s.length||f||c(m)},g.prototype.run=function(){this.fun.apply(null,this.array)},e.title="browser",e.browser=!0,e.env={},e.argv=[],e.version="",e.versions={},e.on=p,e.addListener=p,e.once=p,e.off=p,e.removeListener=p,e.removeAllListeners=p,e.emit=p,e.prependListener=p,e.prependOnceListener=p,e.listeners=function(n){return[]},e.binding=function(n){throw new Error("process.binding is not supported")},e.cwd=function(){return"/"},e.chdir=function(n){throw new Error("process.chdir is not supported")},e.umask=function(){return 0}},{}],2:[function(n,l,e){(function(e){function r(){}function i(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],f(n,this)}function o(t,r){for(;3===t._state;)t=t._value;0!==t._state?(t._handled=!0,i._immediateFn(function(){var n,e=1===t._state?r.onFulfilled:r.onRejected;if(null!==e){try{n=e(t._value)}catch(n){return void c(r.promise,n)}u(r.promise,n)}else(1===t._state?u:c)(r.promise,t._value)})):t._deferreds.push(r)}function u(e,n){try{if(n===e)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"==typeof n||"function"==typeof n)){var t=n.then;if(n instanceof i)return e._state=3,e._value=n,void a(e);if("function"==typeof t)return void f((r=t,o=n,function(){r.apply(o,arguments)}),e)}e._state=1,e._value=n,a(e)}catch(n){c(e,n)}var r,o}function c(n,e){n._state=2,n._value=e,a(n)}function a(n){2===n._state&&0===n._deferreds.length&&i._immediateFn(function(){n._handled||i._unhandledRejectionFn(n._value)});for(var e=0,t=n._deferreds.length;e<t;e++)o(n,n._deferreds[e]);n._deferreds=null}function s(n,e,t){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof e?e:null,this.promise=t}function f(n,e){var t=!1;try{n(function(n){t||(t=!0,u(e,n))},function(n){t||(t=!0,c(e,n))})}catch(n){if(t)return;t=!0,c(e,n)}}var n,t;n=this,t=setTimeout,i.prototype.catch=function(n){return this.then(null,n)},i.prototype.then=function(n,e){var t=new this.constructor(r);return o(this,new s(n,e,t)),t},i.all=function(n){var c=Array.prototype.slice.call(n);return new i(function(o,i){if(0===c.length)return o([]);var u=c.length;for(var n=0;n<c.length;n++)!function e(t,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var r=n.then;if("function"==typeof r)return void r.call(n,function(n){e(t,n)},i)}c[t]=n,0==--u&&o(c)}catch(n){i(n)}}(n,c[n])})},i.resolve=function(e){return e&&"object"==typeof e&&e.constructor===i?e:new i(function(n){n(e)})},i.reject=function(t){return new i(function(n,e){e(t)})},i.race=function(o){return new i(function(n,e){for(var t=0,r=o.length;t<r;t++)o[t].then(n,e)})},i._immediateFn="function"==typeof e?function(n){e(n)}:function(n){t(n,0)},i._unhandledRejectionFn=function(n){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",n)},i._setImmediateFn=function(n){i._immediateFn=n},i._setUnhandledRejectionFn=function(n){i._unhandledRejectionFn=n},void 0!==l&&l.exports?l.exports=i:n.Promise||(n.Promise=i)}).call(this,n("timers").setImmediate)},{timers:3}],3:[function(a,n,s){(function(n,e){var r=a("process/browser.js").nextTick,t=Function.prototype.apply,o=Array.prototype.slice,i={},u=0;function c(n,e){this._id=n,this._clearFn=e}s.setTimeout=function(){return new c(t.call(setTimeout,window,arguments),clearTimeout)},s.setInterval=function(){return new c(t.call(setInterval,window,arguments),clearInterval)},s.clearTimeout=s.clearInterval=function(n){n.close()},c.prototype.unref=c.prototype.ref=function(){},c.prototype.close=function(){this._clearFn.call(window,this._id)},s.enroll=function(n,e){clearTimeout(n._idleTimeoutId),n._idleTimeout=e},s.unenroll=function(n){clearTimeout(n._idleTimeoutId),n._idleTimeout=-1},s._unrefActive=s.active=function(n){clearTimeout(n._idleTimeoutId);var e=n._idleTimeout;0<=e&&(n._idleTimeoutId=setTimeout(function(){n._onTimeout&&n._onTimeout()},e))},s.setImmediate="function"==typeof n?n:function(n){var e=u++,t=!(arguments.length<2)&&o.call(arguments,1);return i[e]=!0,r(function(){i[e]&&(t?n.apply(null,t):n.call(null),s.clearImmediate(e))}),e},s.clearImmediate="function"==typeof e?e:function(n){delete i[n]}}).call(this,a("timers").setImmediate,a("timers").clearImmediate)},{"process/browser.js":1,timers:3}],4:[function(n,e,t){var r=n("promise-polyfill"),n="undefined"!=typeof window?window:Function("return this;")();e.exports={boltExport:n.Promise||r}},{"promise-polyfill":2}]},{},[4])(4)});function ve(n){setTimeout(function(){throw n},0)}function be(n){return Ke(Ue(n))}function ye(n,t){return nt(n,function(n,e){return{k:e,v:t(n,e)}})}function _e(n,e){var t,r,o,i,u={};return t=e,i=u,r=function(n,e){i[e]=n},o=h,Ze(n,function(n,e){(t(n,e)?r:o)(n,e)}),u}function we(n,e){return rt(n,e)&&void 0!==n[e]&&null!==n[e]}function xe(n,e,t){if(!(z(t)||Y(t)||X(t)))throw console.error("Invalid call to Attribute.set. Key ",e,":: Value ",t,":: Element ",n),new Error("Attribute value was not simple");n.setAttribute(e,t+"")}function Oe(n,e,t){xe(n.dom,e,t)}function Se(n,e){return null===(e=n.dom.getAttribute(e))?void 0:e}function Ee(n,e){return H.from(Se(n,e))}function ke(n,e){return!(!(n=n.dom)||!n.hasAttribute)&&n.hasAttribute(e)}function Ce(n,e){n.dom.removeAttribute(e)}function Te(n,e){return void 0===(e=Se(n,e))||""===e?[]:e.split(" ")}function Me(n){return void 0!==n.dom.classList}function De(n,e){return function(n,e,t){t=Te(n,e).concat([t]);return Oe(n,e,t.join(" ")),!0}(n,"class",e)}function Ae(n,e){return r=e,0<(n=S(Te(t=n,e="class"),function(n){return n!==r})).length?Oe(t,e,n.join(" ")):Ce(t,e),0;var t,r}function Fe(n,e){Me(n)?n.dom.classList.add(e):De(n,e)}function Re(n){0===(Me(n)?n.dom.classList:Te(n,"class")).length&&Ce(n,"class")}function Ne(n,e){Me(n)?n.dom.classList.remove(e):Ae(n,e),Re(n)}function Be(n,e){return Me(n)&&n.dom.classList.contains(e)}function He(n,e){return function(n,e){e=void 0===e?document:e.dom;return an(e)?[]:x(e.querySelectorAll(n),I.fromDom)}(e,n)}var Pe,Ie,je=he.exports.boltExport,Le=function(n){var t=H.none(),e=[],r=function(n){o()?u(n):e.push(n)},o=function(){return t.isSome()},i=function(n){O(n,u)},u=function(e){t.each(function(n){setTimeout(function(){e(n)},0)})};return n(function(n){o()||(t=H.some(n),i(e),e=[])}),{get:r,map:function(t){return Le(function(e){r(function(n){e(t(n))})})},isReady:o}},Ve={nu:Le,pure:function(e){return Le(function(n){n(e)})}},We=function(t){function n(n){t().then(n,ve)}return{map:function(n){return We(function(){return t().then(n)})},bind:function(e){return We(function(){return t().then(function(n){return e(n).toPromise()})})},anonBind:function(n){return We(function(){return t().then(function(){return n.toPromise()})})},toLazy:function(){return Ve.nu(n)},toCached:function(){var n=null;return We(function(){return null===n&&(n=t()),n})},toPromise:t,get:n}},Ue=function(n){return We(function(){return new je(n)})},ze=function(n){return We(function(){return je.resolve(n)})},qe=function(t){return{is:function(n){return t===n},isValue:v,isError:s,getOr:y(t),getOrThunk:y(t),getOrDie:y(t),or:function(n){return qe(t)},orThunk:function(n){return qe(t)},fold:function(n,e){return e(t)},map:function(n){return qe(n(t))},mapError:function(n){return qe(t)},each:function(n){n(t)},bind:function(n){return n(t)},exists:function(n){return n(t)},forall:function(n){return n(t)},toOptional:function(){return H.some(t)}}},Ge=function(t){return{is:s,isValue:s,isError:v,getOr:f,getOrThunk:function(n){return n()},getOrDie:function(){return u(String(t))()},or:function(n){return n},orThunk:function(n){return n()},fold:function(n,e){return n(t)},map:function(n){return Ge(t)},mapError:function(n){return Ge(n(t))},each:h,bind:function(n){return Ge(t)},exists:s,forall:v,toOptional:H.none}},Ye={value:qe,error:Ge,fromOption:function(n,e){return n.fold(function(){return Ge(e)},qe)}},Ke=function(i){return Z(Z({},i),{toCached:function(){return Ke(i.toCached())},bindFuture:function(e){return Ke(i.bind(function(n){return n.fold(function(n){return ze(Ye.error(n))},function(n){return e(n)})}))},bindResult:function(e){return Ke(i.map(function(n){return n.bind(e)}))},mapResult:function(e){return Ke(i.map(function(n){return n.map(e)}))},mapError:function(e){return Ke(i.map(function(n){return n.mapError(e)}))},foldResult:function(e,t){return i.map(function(n){return n.fold(e,t)})},withTimeout:function(n,o){return Ke(Ue(function(e){var t=!1,r=setTimeout(function(){t=!0,e(Ye.error(o()))},n);i.get(function(n){t||(clearTimeout(r),e(n))})}))}})},Xe=function(n){return Ke(ze(Ye.value(n)))},Je={nu:be,wrap:Ke,pure:Xe,value:Xe,error:function(n){return Ke(ze(Ye.error(n)))},fromResult:function(n){return Ke(ze(n))},fromFuture:function(n){return Ke(n.map(Ye.value))},fromPromise:function(n){return be(function(e){n.then(function(n){e(Ye.value(n))},function(n){e(Ye.error(n))})})}},Qe=Object.keys,$e=Object.hasOwnProperty,Ze=function(n,e){for(var t=Qe(n),r=0,o=t.length;r<o;r++){var i=t[r];e(n[i],i)}},nt=function(n,t){var r={};return Ze(n,function(n,e){e=t(n,e);r[e.k]=e.v}),r},et=function(n,t){var r=[];return Ze(n,function(n,e){r.push(t(n,e))}),r},tt=function(n,e){return rt(n,e)?H.from(n[e]):H.none()},rt=function(n,e){return $e.call(n,e)},ot=function(n){var e=Zn(n)?n.dom.parentNode:n.dom;if(null==e||null===e.ownerDocument)return!1;var t,r,o=e.ownerDocument;return bn(I.fromDom(e)).fold(function(){return o.body.contains(e)},(t=ot,r=yn,function(n){return t(r(n))}))},it=y("tox-comment--active"),ut=y("data-mce-annotation"),ct=y("tinycomments");function at(e,i,r,u){function t(n,e){r.notificationManager.open({text:n,type:"error"}),e&&console.error(n,e)}function c(n,e){return{view:{type:"splash"},grabFocus:function(){return 1===n||e}}}function o(n,o){i.set(n),i.get().fold(function(){return Je.pure({response:Pe.Change,update:c(o,u.hasFocus())})},function(n){return e.lookup({conversationUid:n}).foldResult(function(n){return Ye.value({response:Pe.Change,update:c(o,u.hasFocus())})},function(e){var n,t,r=i.get().forall(function(n){return n!==e.conversation.uid});return Ye.value(r?{response:Pe.NoChange}:{response:Pe.Change,update:(n=o,t=u.hasFocus(),{view:{type:"comments",conversation:e.conversation},grabFocus:function(){return t||w([1,3,4],n)}})})})}).get(function(n){return n.fold(function(n){t("An error occurred reading the conversation. See the Console for details.",n)},function(n){n.response===Pe.Change&&(u.update(n.update),l())})})}function a(n){var e="."+it(),n=I.fromDom(n.getBody()),e=He(n,e);O(e,function(n){Ne(n,it())})}function s(n){f.set(n),u.setReadonly(n)}var f=fe(!1),l=function(){var n,e,t;return s(i.get().isNone()&&(e=(n=r).selection.getRng(),t=He(I.fromDom(e.commonAncestorContainer),"["+ut()+'="'+ct()+'"]'),p(t,d(function(n,e,t){e=e.createRange();return e.selectNode(t.dom),e.compareBoundaryPoints(Range.END_TO_START,n)<0&&0<e.compareBoundaryPoints(Range.START_TO_END,n)},e,n.getDoc())))),f.get()};return{refreshSidebar:o,refreshView:function(n){n.fold(function(){a(r),o(H.none(),0)},function(n){var e=n.uid,n=n.nodes;n=n,a(r),O(n,function(n){Fe(n,it())}),o(H.some(e),0)})},setReadonly:s,showError:t,refreshReadonly:l}}(oc=Pe=Pe||{})[oc.Change=0]="Change",oc[oc.NoChange=1]="NoChange",(ic=Ie=Ie||{})[ic.Error=0]="Error",ic[ic.Value=1]="Value";function st(n,e,t){return n.stype===Ie.Error?e(n.serror):t(n.svalue)}function ft(n){return{stype:Ie.Value,svalue:n}}function lt(n){return{stype:Ie.Error,serror:n}}function dt(n){return Ar.defaultedThunk(y(n))}function mt(n,e){var t={};return t[n]=e,t}function gt(n){return e={},O(n,function(n){e[n.key]=n.value}),e;var e}function pt(n,e){var t,r,o;return 0<(o=(t=[],r=[],O(n,function(n){n.fold(function(n){t.push(n)},function(n){r.push(n)})}),{errors:t,values:r})).errors.length?(n=o.errors,Ye.error(T(n))):(e=e,0===(o=o.values).length?Ye.value(e):Ye.value(Mr(e,Dr.apply(void 0,o))))}function ht(n){return i(wr,T)(n)}function vt(n){return q(n)&&100<Qe(n).length?" removed due to size":JSON.stringify(n,null,2)}function bt(n,e){return wr([{path:n,getErrorInfo:e}])}function yt(t,r,o){return tt(r,o).fold(function(){return n=o,e=r,bt(t,function(){return'Could not find valid *strict* value for "'+n+'" in '+vt(e)});var n,e},yr)}function _t(n,e,t){return e=tt(n,e).fold(function(){return t(n)},f),yr(e)}function wt(u,c,n,a){return n.fold(function(t,e,n,r){function o(n){return n=r.extract(u.concat([t]),a,n),Sr(n,function(n){return mt(e,a(n))})}function i(n){return n.fold(function(){var n=mt(e,a(H.none()));return yr(n)},function(n){n=r.extract(u.concat([t]),a,n);return Sr(n,function(n){return mt(e,a(H.some(n)))})})}return n.fold(function(){return xr(yt(u,c,t),o)},function(n){return xr(_t(c,t,n),o)},function(){return xr(yr(tt(c,t)),i)},function(n){return xr(function(e,n,t){n=tt(e,n).map(function(n){return!0===n?t(e):n});return yr(n)}(c,t,n),i)},function(n){var e=n(c),n=Sr(_t(c,t,y({})),function(n){return Mr(e,n)});return xr(n,o)})},function(n,e){e=e(c);return yr(mt(n,a(e)))})}function xt(r){return{extract:function(t,n,e){return Or(r(e,n),function(n){return e=n,bt(t,function(){return e});var e})},toString:function(){return"val"}}}function Ot(n){var i=Lr(n),u=b(n,function(e,n){return n.fold(function(n){return Mr(e,Hr(n,!0))},y(e))},{});return{extract:function(n,e,t){var r,o=Y(t)?[]:Qe(_e(t,function(n){return null!=n})),o=S(o,function(n){return!we(u,n)});return 0===o.length?i.extract(n,e,t):(r=o,bt(n,function(){return"There are unsupported fields: ["+r.join(", ")+"] specified"}))},toString:i.toString}}function St(t,o){function i(n,e){return o=xt(t),function(t,r,n){n=x(n,function(n,e){return o.extract(t.concat(["["+e+"]"]),r,n)});return Ir(n)}(n,f,e);var o}return{extract:function(e,t,r){var n=Qe(r),n=i(e,n);return xr(n,function(n){n=x(n,function(n){return jr.field(n,n,Fr(),o)});return Lr(n).extract(e,t,r)})},toString:function(){return"setOf("+o.toString()+")"}}}function Et(t,e,r,o,i){return tt(o,i).fold(function(){return n=o,e=i,bt(t,function(){return'The chosen schema: "'+e+'" did not exist in branches: '+vt(n)});var n,e},function(n){return n.extract(t.concat(["branch: "+i]),e,r)})}function kt(o,i){return{extract:function(e,t,r){return tt(r,o).fold(function(){return n=o,bt(e,function(){return'Choice schema did not contain choice key: "'+n+'"'});var n},function(n){return Et(e,t,r,i,n)})},toString:function(){return"chooseOn("+o+"). Possible values: "+Qe(i)}}}function Ct(e){return xt(function(n){return e(n).fold(wr,yr)})}function Tt(n,e,t){return br(function(n,e,t,r){t=e.extract([n],t,r);return Er(t,function(n){return{input:r,errors:n}})}(n,e,f,t))}function Mt(n){return n.fold(function(n){throw new Error(Gr(n))},f)}function Dt(n,e,t){return Mt(Tt(n,e,t))}function At(n,e){return kt(n,ye(e,Lr))}function Ft(n){return Ur(n,n,Fr(),Vr())}function Rt(n,e){return Ur(n,n,Fr(),e)}function Nt(n,e){return Ur(n,n,Fr(),Lr(e))}function Bt(n){return Ur(n,n,Rr(),Vr())}function Ht(n,e){return Ur(n,n,Rr(),e)}function Pt(n,e){return Ht(n,Lr(e))}function It(n,e){return Ht(n,Ot(e))}function jt(n,e){return Ur(n,n,dt(e),Vr())}function Lt(n,e,t){return Ur(n,n,dt(e),t)}function Vt(n,e){return Wr(n,e)}function Wt(n){return K(n)?n:s}function Ut(n,e){return sn(n.element,e.event.target)}function zt(n){if(!we(n,"can")&&!we(n,"abort")&&!we(n,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(n,null,2)+" does not have can, abort, or run!");return Dt("Extracting event.handler",Ot([jt("can",v),jt("abort",s),jt("run",h)]),n)}function qt(t){var e,r,o,i,n=(r=function(n){return n.can},function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return E(e,function(n,e){return n&&r(e).apply(void 0,t)},!0)}),u=(o=e=t,i=function(n){return n.abort},function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return E(o,function(n,e){return n||i(e).apply(void 0,t)},!1)});return zt({can:n,abort:u,run:function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];O(t,function(n){n.run.apply(void 0,e)})}})}function Gt(n,e){Fo(n,n.element,e,{})}function Yt(n,e,t){Fo(n,n.element,e,t)}function Kt(n){Gt(n,yo())}function Xt(n,e,t){Fo(n,e,t,{})}function Jt(n,e){return{key:n,value:zt({run:e})}}function Qt(n){return function(t){return{key:n,value:zt({run:function(n,e){Ut(n,e)&&t(n,e)}})}}}function $t(n){return Jt(n,function(n,e){e.cut()})}function Zt(n,e){var t=n.toString(),r=t.indexOf(")")+1,o=t.indexOf("("),i=t.substring(o+1,r-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:e,parameters:Io(i)}},n}function nr(n){return{classes:void 0!==n.classes?n.classes:[],attributes:void 0!==n.attributes?n.attributes:{},styles:void 0!==n.styles?n.styles:{}}}function er(t,r,o){return Ho(function(n,e){o(n,t,r)})}function tr(o,i,u){var n,e,t,r,c,a;return n=function(t){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];var r=[t].concat(n);return t.config({name:y(o)}).fold(function(){throw new Error("We could not find any behaviour configuration for: "+o+". Using API: "+u)},function(n){var e=Array.prototype.slice.call(r,1);return i.apply(void 0,[t,n.config,n.state].concat(e))})},e=u,r=(t=i).toString(),c=r.indexOf(")")+1,t=r.indexOf("("),a=r.substring(t+1,c-1).split(/,\s*/),n.toFunctionAnnotation=function(){return{name:e,parameters:Io(a.slice(0,1).concat(a.slice(3)))}},n}function rr(n){return{key:n,value:void 0}}function or(n){var e,t,r,o,i,u=Dt("Creating behaviour: "+n.name,Vo,n);return e=u.fields,t=u.name,r=u.active,o=u.apis,i=u.extra,n=u.state,u=Ot(e),e=Pt(t,[It("config",e)]),jo(u,e,t,r,o,i,n)}function ir(n,e){return{key:n,value:{config:{},me:function(n,e){e=Ro(e);return or({fields:[Ft("enabled")],name:n,active:{events:y(e)}})}(n,e),configAsRaw:y({}),initialConfig:{},state:$r}}}function ur(n){return n.dom.focus()}function cr(n){return void 0===n&&(n=I.fromDom(document)),H.from(n.dom.activeElement).map(I.fromDom)}function ar(e){return cr(ie(e)).filter(function(n){return e.dom.contains(n.dom)})}function sr(e,n){O(n,function(n){se(e,n)})}function fr(n){n.dom.textContent="",O(pn(n),function(n){zo(n)})}function lr(n){return n.dom.innerHTML}function dr(n,e){var t=ln(n).dom,r=I.fromDom(t.createDocumentFragment()),t=function(n,e){e=(e||document).createElement("div");return e.innerHTML=n,pn(I.fromDom(e))}(e,t);sr(r,t),fr(n),se(n,r)}function mr(n){return e=n,n=!1,I.fromDom(e.dom.cloneNode(n));var e}function gr(n){var e=mr(n);return n=e,e=I.fromTag("div"),n=I.fromDom(n.dom.cloneNode(!0)),se(e,n),lr(e)}var pr,hr,vr=function(n){return n.fold(lt,ft)},br=function(n){return st(n,Ye.error,Ye.value)},yr=ft,_r=function(n){var e=[],t=[];return O(n,function(n){st(n,function(n){return t.push(n)},function(n){return e.push(n)})}),{values:e,errors:t}},wr=lt,xr=function(n,e){return n.stype===Ie.Value?e(n.svalue):n},Or=function(n,e){return n.stype===Ie.Error?e(n.serror):n},Sr=function(n,e){return n.stype===Ie.Value?{stype:Ie.Value,svalue:e(n.svalue)}:n},Er=function(n,e){return n.stype===Ie.Error?{stype:Ie.Error,serror:e(n.serror)}:n},kr=function(u){if(!G(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var c=[],t={};return O(u,function(n,r){var e=Qe(n);if(1!==e.length)throw new Error("one and only one name per case");var o=e[0],i=n[o];if(void 0!==t[o])throw new Error("duplicate key detected:"+o);if("cata"===o)throw new Error("cannot have a case named cata (sorry)");if(!G(i))throw new Error("case arguments must be an array");c.push(o),t[o]=function(){var n=arguments.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+o+". Expected "+i.length+" ("+i+"), got "+n);for(var t=new Array(n),e=0;e<t.length;e++)t[e]=arguments[e];return{fold:function(){if(arguments.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+arguments.length);return arguments[r].apply(null,t)},match:function(n){var e=Qe(n);if(c.length!==e.length)throw new Error("Wrong number of arguments to match. Expected: "+c.join(",")+"\nActual: "+e.join(","));if(!D(c,function(n){return w(e,n)}))throw new Error("Not all branches were specified when using match. Specified: "+e.join(", ")+"\nRequired: "+c.join(", "));return n[o].apply(null,t)},log:function(n){console.log(n,{constructors:c,constructor:o,params:t})}}}}),t},Cr=Object.prototype.hasOwnProperty,Tr=function(u){return function(){for(var n=new Array(arguments.length),e=0;e<n.length;e++)n[e]=arguments[e];if(0===n.length)throw new Error("Can't merge zero objects");for(var t={},r=0;r<n.length;r++){var o,i=n[r];for(o in i)Cr.call(i,o)&&(t[o]=u(t[o],i[o]))}return t}},Mr=Tr(function(n,e){return q(n)&&q(e)?Mr(n,e):e}),Dr=Tr(function(n,e){return e}),Ar=kr([{strict:[]},{defaultedThunk:["fallbackThunk"]},{asOption:[]},{asDefaultedOptionThunk:["fallbackThunk"]},{mergeWithThunk:["baseThunk"]}]),Fr=Ar.strict,Rr=Ar.asOption,Nr=Ar.defaultedThunk,Br=(Ar.asDefaultedOptionThunk,Ar.mergeWithThunk),Hr=(kr([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]),mt),Pr=function(n,e){n=_r(n);return 0<n.errors.length?ht(n.errors):(n=n.values,e=e,0<n.length?yr(Mr(e,Dr.apply(void 0,n))):yr(e))},Ir=function(n){n=_r(n);return 0<n.errors.length?ht(n.errors):yr(n.values)},jr=kr([{field:["key","okey","presence","prop"]},{state:["okey","instantiator"]}]),Lr=function(r){return{extract:function(n,e,t){return function(e,t,n,r){n=x(n,function(n){return wt(e,t,n,r)});return Pr(n,{})}(n,t,r,e)},toString:function(){return"obj{\n"+x(r,function(n){return n.fold(function(n,e,t,r){return n+" -> "+r.toString()},function(n,e){return"state("+n+")"})}).join("\n")+"}"}}},Vr=y(xt(yr)),Wr=jr.state,Ur=jr.field,zr=xt(yr),qr=function(e,n){return St(function(n){return vr(e(n))},n)},Gr=function(n){return"Errors: \n"+function(n){n=10<n.length?n.slice(0,10).concat([{path:[],getErrorInfo:function(){return"... (only showing first ten failures)"}}]):n;return x(n,function(n){return"Failed path: ("+n.path.join(" > ")+")\n"+n.getErrorInfo()})}(n.errors).join("\n")+"\n\nInput object: "+vt(n.input)},Yr=y(zr),Kr=function(t,r){return xt(function(n){var e=typeof n;return t(n)?yr(n):wr("Expected type: "+r+" but got: "+e)})},Xr=Kr(Y,"boolean"),Jr=Kr(K,"function"),Qr=function(n){return Rt(n,Jr)},$r={init:function(){return Zr({readState:function(){return"No State required"}})}},Zr=function(n){return n},no=function(n,e,t){var r=e(n),o=Wt(t);return r.orThunk(function(){return o(n)?H.none():function(n,e,t){for(var r=n.dom,o=Wt(t);r.parentNode;){r=r.parentNode;var i=I.fromDom(r),u=e(i);if(u.isSome())return u;if(o(i))break}return H.none()}(n,e,o)})},eo=y,to=eo("touchstart"),ro=eo("touchmove"),oo=eo("touchend"),io=eo("mousedown"),uo=eo("mouseover"),co=eo("keydown"),ao=eo("keyup"),so=eo("input"),fo=eo("click"),lo=eo("transitionend"),mo=function(n){return y("alloy."+n)},go={tap:mo("tap")},po=mo("focus"),ho=mo("blur.post"),vo=mo("paste.post"),bo=mo("receive"),yo=mo("execute"),_o=mo("focus.item"),wo=go.tap,xo=mo("longpress"),Oo=mo("sandbox.close"),So=mo("system.init"),Eo=mo("system.attached"),ko=mo("system.detached"),Co=mo("system.dismissRequested"),To=mo("system.repositionRequested"),Mo=mo("focusmanager.shifted"),Do=mo("highlight"),Ao=mo("dehighlight"),Fo=function(n,e,t,r){r=Z({target:e},r);n.getSystem().triggerEvent(t,e,r)},Ro=gt,No=Qt(Eo()),Bo=Qt(ko()),Ho=Qt(So()),Po=(pr=yo(),function(n){return Jt(pr,n)}),Io=function(n){return x(n,function(n){return r=t="/*",t=(e=e=n).length-t.length,""===r||e.length>=r.length&&e.substr(t,t+r.length)===r?n.substring(0,n.length-"/*".length):n;var e,t,r})},jo=function(t,n,r,o,e,i,u){function c(n){return we(n,r)?n[r]():H.none()}var e=ye(e,function(n,e){return tr(r,n,e)}),i=ye(i,Zt),a=Z(Z(Z({},i),e),{revoke:d(rr,r),config:function(n){var e=Dt(r+"-config",t,n);return{key:r,value:{config:e,me:a,configAsRaw:tn(function(){return Dt(r+"-config",t,n)}),initialConfig:n,state:u}}},schema:function(){return n},exhibit:function(n,t){return c(n).bind(function(e){return tt(o,"exhibit").map(function(n){return n(t,e.config,e.state)})}).getOr(nr({}))},name:function(){return r},handlers:function(n){return c(n).map(function(n){return tt(o,"events").getOr(function(){return{}})(n.config,n.state)}).getOr({})}});return a},Lo=gt,Vo=Ot([Ft("fields"),Ft("name"),jt("active",{}),jt("apis",{}),jt("state",$r),jt("extra",{})]),Wo=Ot([Ft("branchKey"),Ft("branches"),Ft("name"),jt("active",{}),jt("apis",{}),jt("state",$r),jt("extra",{})]),Uo=y(void 0),zo=function(n){n=n.dom;null!==n.parentNode&&n.parentNode.removeChild(n)},qo="unknown";(uc=hr=hr||{})[uc.STOP=0]="STOP",uc[uc.NORMAL=1]="NORMAL",uc[uc.LOGGING=2]="LOGGING";function Go(e,n,t){var r,o,i,u;switch(tt(Ko.get(),e).orThunk(function(){var n=Qe(Ko.get());return N(n,function(n){return-1<e.indexOf(n)?H.some(Ko.get()[n]):H.none()})}).getOr(hr.NORMAL)){case hr.NORMAL:return t(Jo());case hr.LOGGING:n=(r=e,o=n,i=[],u=(new Date).getTime(),{logEventCut:function(n,e,t){i.push({outcome:"cut",target:e,purpose:t})},logEventStopped:function(n,e,t){i.push({outcome:"stopped",target:e,purpose:t})},logNoParent:function(n,e,t){i.push({outcome:"no-parent",target:e,purpose:t})},logEventNoHandlers:function(n,e){i.push({outcome:"no-handlers-left",target:e})},logEventResponse:function(n,e,t){i.push({outcome:"response",purpose:t,target:e})},write:function(){var n=(new Date).getTime();w(["mousemove","mouseover","mouseout",So()],r)||console.log(r,{event:r,time:n-u,target:o.dom,sequence:x(i,function(n){return w(["cut","stopped","response"],n.outcome)?"{"+n.purpose+"} "+n.outcome+" at ("+gr(n.target)+")":n.outcome})})}}),t=t(n);return n.write(),t;case hr.STOP:return!0}}function Yo(n){return n.cHandler}var Ko=fe({}),Xo=["alloy/data/Fields","alloy/debugging/Debugging"],Jo=y({logEventCut:h,logEventStopped:h,logNoParent:h,logEventNoHandlers:h,logEventResponse:h,write:h}),Qo=function(n,e){return{cHandler:d.apply(void 0,[n.handler].concat(e)),purpose:n.purpose}};function $o(n,e,t,r,o){return n(t,r)?H.some(t):K(o)&&o(t)?H.none():e(t,r,o)}function Zo(n,e,t){for(var r=n.dom,o=K(t)?t:s;r.parentNode;){r=r.parentNode;var i=I.fromDom(r);if(e(i))return H.some(i);if(o(i))break}return H.none()}function ni(n,e,t){return $o(function(n,e){return e(n)},Zo,n,e,t)}function ei(n,e,t){return Zo(n,function(n){return cn(n,e)},t)}function ti(n,e){return function(n,e){e=void 0===e?document:e.dom;return an(e)?H.none():H.from(e.querySelector(n)).map(I.fromDom)}(e,n)}function ri(n,e,t){return $o(cn,ei,n,e,t)}var oi=[8],ii=[9],ui=[13],ci=[27],ai=[32],si=[37],fi=[38],li=[39],di=[40];function mi(n){return void 0===(n=n.raw).touches||1!==n.touches.length?H.none():H.some(n.touches[0])}function gi(t){var r,o,i,u=fe(H.none()),c=fe(!1),a=(r=function(n){t.triggerEvent(xo(),n),c.set(!0)},o=400,i=null,{cancel:function(){null!==i&&(clearTimeout(i),i=null)},schedule:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];i=setTimeout(function(){r.apply(null,n),i=null},o)}}),s=gt([{key:to(),value:function(e){return mi(e).each(function(n){a.cancel();n={x:n.clientX,y:n.clientY,target:e.target};a.schedule(e),c.set(!1),u.set(H.some(n))}),H.none()}},{key:ro(),value:function(n){return a.cancel(),mi(n).each(function(r){u.get().each(function(n){var e,t;e=r,t=n,n=Math.abs(e.clientX-t.x),t=Math.abs(e.clientY-t.y),(5<n||5<t)&&u.set(H.none())})}),H.none()}},{key:oo(),value:function(e){a.cancel();return u.get().filter(function(n){return sn(n.target,e.target)}).map(function(n){return c.get()?(e.prevent(),!1):t.triggerEvent(wo(),e)})}}]);return{fireIfReady:function(e,n){return tt(s,n).bind(function(n){return n(e)})}}}function pi(n){var e;return n.raw.which===oi[0]&&!w(["input","textarea"],fn(n.target))&&(n=n.target,!ri(n,'[contenteditable="true"]',e).isSome())}function hi(){return Jn().browser.isFirefox()}function vi(e,n){var t,r=Dt("Getting GUI events settings",xi,n),o=gi(r),i=x(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),function(n){return xn(e,n,function(e){o.fireIfReady(e,n).each(function(n){n&&e.kill()}),r.triggerEvent(n,e)&&e.kill()})}),u=fe(H.none()),c=xn(e,"paste",function(e){o.fireIfReady(e,"paste").each(function(n){n&&e.kill()}),r.triggerEvent("paste",e)&&e.kill(),u.set(H.some(setTimeout(function(){r.triggerEvent(vo(),e)},0)))}),a=xn(e,"keydown",function(n){r.triggerEvent("keydown",n)?n.kill():!0===r.stopBackspace&&pi(n)&&n.prevent()}),s=(t=e,n=function(n){r.triggerEvent("focusin",n)&&n.kill()},hi()?On(t,"focus",n):xn(t,"focusin",n)),f=fe(H.none()),l=(t=e,n=function(n){r.triggerEvent("focusout",n)&&n.kill(),f.set(H.some(setTimeout(function(){r.triggerEvent(ho(),n)},0)))},hi()?On(t,"blur",n):xn(t,"focusout",n));return{unbind:function(){O(i,function(n){n.unbind()}),a.unbind(),s.unbind(),l.unbind(),c.unbind(),u.get().each(clearTimeout),f.get().each(clearTimeout)}}}function bi(n,e){return e=tt(n,"target").getOr(e),fe(e)}function yi(n,r,e,t,o,i){var u,c,n=n(r,t),a=(e=e,o=o,u=fe(!1),c=fe(!1),{stop:function(){u.set(!0)},cut:function(){c.set(!0)},isStopped:u.get,isCut:c.get,event:e,setSource:o.set,getSource:o.get});return n.fold(function(){return i.logEventNoHandlers(r,t),Oi.complete()},function(e){var t=e.descHandler;return Yo(t)(a),a.isStopped()?(i.logEventStopped(r,e.element,t.purpose),Oi.stopped()):a.isCut()?(i.logEventCut(r,e.element,t.purpose),Oi.complete()):gn(e.element).fold(function(){return i.logNoParent(r,e.element,t.purpose),Oi.complete()},function(n){return i.logEventResponse(r,e.element,t.purpose),Oi.resume(n)})})}function _i(n,e,t){var r,o=(e=e,r=fe(!1),{stop:function(){r.set(!0)},cut:h,isStopped:r.get,isCut:s,event:e,setSource:u("Cannot set source of a broadcasted event"),getSource:u("Cannot get source of a broadcasted event")});return O(n,function(n){n=n.descHandler;Yo(n)(o)}),o.isStopped()}function wi(n){var e=(new Date).getTime();return n+"_"+Math.floor(1e9*Math.random())+ ++ki+String(e)}var xi=Ot([Qr("triggerEvent"),jt("stopBackspace",!0)]),Oi=kr([{stopped:[]},{resume:["element"]},{complete:[]}]),Si=function(e,t,r,n,o,i){return yi(e,t,r,n,o,i).fold(function(){return!0},function(n){return Si(e,t,r,n,o,i)},function(){return!1})},Ei=function(n,e,t,r,o){var i=bi(t,r);return Si(n,e,t,r,i,o)},ki=0,Ci=y("alloy-id-"),Ti=y("data-alloy-id"),Mi=Ci(),Di=Ti(),Ai=function(n,e){Object.defineProperty(n.dom,Di,{value:e,writable:!0})},Fi=function(n){n=$n(n)?n.dom[Di]:null;return H.from(n)},Ri=wi,Ni=function(n,e){return{element:n,descHandler:e}},Bi=function(n,e){return{id:n,descHandler:e}};function Hi(){var i={};return{registerId:function(r,o,n){Ze(n,function(n,e){var t=void 0!==i[e]?i[e]:{};t[o]=Qo(n,r),i[e]=t})},unregisterId:function(t){Ze(i,function(n,e){n.hasOwnProperty(t)&&delete n[t]})},filterByType:function(n){return tt(i,n).map(function(n){return et(n,function(n,e){return Bi(e,n)})}).getOr([])},find:function(n,e,t){var o=tt(i,e);return no(t,function(n){return t=o,Fi(r=n).fold(function(){return H.none()},function(e){return t.bind(function(n){return tt(n,e)}).map(function(n){return Ni(r,n)})});var t,r},n)}}}function Pi(){function r(n){var e=n.element;return Fi(e).fold(function(){return function(n,e){n=wi(Mi+n);return Ai(e,n),n}("uid-",n.element)},function(n){return n})}var o=Hi(),i={},u=function(n){Fi(n.element).each(function(n){delete i[n],o.unregisterId(n)})};return{find:function(n,e,t){return o.find(n,e,t)},filter:function(n){return o.filterByType(n)},register:function(n){var e=r(n);we(i,e)&&function(n,e){var t=i[e];if(t!==n)throw new Error('The tagId "'+e+'" is already used by: '+gr(t.element)+"\nCannot use it for: "+gr(n.element)+"\nThe conflicting element is"+(ot(t.element)?" ":" not ")+"already in the DOM");u(n)}(n,e);var t=[n];o.registerId(t,e,n.events),i[e]=n},unregister:u,getById:function(n){return tt(i,n)}}}function Ii(e){function n(n){return function(){throw new Error("The component must be in a context to send: "+n+(e?"\n"+gr(e().element)+" is not in context.":""))}}return{debugInfo:y("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),build:n("build"),addToWorld:n("addToWorld"),removeFromWorld:n("removeFromWorld"),addToGui:n("addToGui"),removeFromGui:n("removeFromGui"),getByUid:n("getByUid"),getByDom:n("getByDom"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),isConnected:s}}function ji(n){return Hr(Hu,n)}function Li(r){return n=function(n){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];return r.apply(void 0,en([n.getApis(),n],e))},t=(e=r).toString(),o=t.indexOf(")")+1,e=t.indexOf("("),i=t.substring(e+1,o-1).split(/,\s*/),n.toFunctionAnnotation=function(){return{name:"OVERRIDE",parameters:Io(i.slice(1))}},n;var n,e,t,o,i}function Vi(n,o){var i={};return Ze(n,function(n,r){Ze(n,function(n,e){var t=tt(i,e).getOr([]);i[e]=t.concat([o(r,n)])})}),i}function Wi(e,n,t,r){var o=Z({},n);function i(n){return b(n,function(n,e){return Z(Z({},e.modification),n)},{})}O(t,function(n){o[n.name()]=n.exhibit(e,r)});var u=Vi(o,function(n,e){return{name:n,modification:e}}),n=b(u.classes,function(n,e){return e.modification.concat(n)},[]),t=i(u.attributes),u=i(u.styles);return nr({classes:n,attributes:t,styles:u})}function Ui(o,i,n,u){try{var e=function(n,e){n=J.call(n,0);return n.sort(e),n}(n,function(n,e){var t=n[i],r=e[i],n=u.indexOf(t),e=u.indexOf(r);if(-1===n)throw new Error("The ordering for "+o+" does not have an entry for "+t+".\nOrder specified: "+JSON.stringify(u,null,2));if(-1===e)throw new Error("The ordering for "+o+" does not have an entry for "+r+".\nOrder specified: "+JSON.stringify(u,null,2));return n<e?-1:e<n?1:0});return Ye.value(e)}catch(n){return Ye.error([n])}}function zi(n,e){return{name:n,handler:e}}function qi(n,e,t){var r,o,e=Z(Z({},t),(r=n,o={},O(e,function(n){o[n.name()]=n.handlers(r)}),o));return Vi(e,zi)}function Gi(n){var o=K(n=n)?{can:y(!0),abort:y(!1),run:n}:n;return function(n,e){for(var t=[],r=2;r<arguments.length;r++)t[r-2]=arguments[r];n=[n,e].concat(t);o.abort.apply(void 0,n)?e.stop():o.can.apply(void 0,n)&&o.run.apply(void 0,n)}}function Yi(n,e,t){return(e=e[t])?Ui("Event: "+t,"name",n,e).map(function(n){n=x(n,function(n){return n.handler});return qt(n)}):(t=t,n=n,Ye.error(["The event ("+t+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(x(n,function(n){return n.name}),null,2)]))}function Ki(n){var e;return Tt("custom.definition",Lr([Ur("dom","dom",Fr(),Lr([Ft("tag"),jt("styles",{}),jt("classes",[]),jt("attributes",{}),Bt("value"),Bt("innerHtml")])),Ft("components"),Ft("uid"),jt("events",{}),jt("apis",{}),Ur("eventOrder","eventOrder",((e={})[yo()]=["disabling",Iu,"toggling","typeaheadevents"],e[po()]=[Iu,"focusing","keying"],e[So()]=[Iu,"disabling","toggling","representing"],e[so()]=[Iu,"representing","streaming","invalidating"],e[ko()]=[Iu,"representing","item-events","tooltipping"],e[io()]=["focusing",Iu,"item-type-events"],e[to()]=["focusing",Iu,"item-type-events"],e[uo()]=["item-type-events","tooltipping"],e[bo()]=["receiving","reflecting","tooltipping"],e=e,Ar.mergeWithThunk(y(e))),Yr()),Bt("domModification")]),n)}function Xi(e,n){O(n,function(n){Fe(e,n)})}function Ji(e,n){O(n,function(n){Ne(e,n)})}function Qi(n){return void 0!==n.style&&K(n.style.getPropertyValue)}function $i(n,e,t){if(!z(t))throw console.error("Invalid call to CSS.set. Property ",e,":: Value ",t,":: Element ",n),new Error("CSS value must be a string: "+t);Qi(n)&&n.style.setProperty(e,t)}function Zi(n,e){Qi(n)&&n.style.removeProperty(e)}function nu(n,e,t){n=n.dom,$i(n,e,t)}function eu(n,e){var t=n.dom;Ze(e,function(n,e){$i(t,e,n)})}function tu(n,e){var t=n.dom;Ze(e,function(n,e){n.fold(function(){Zi(t,e)},function(n){$i(t,e,n)})})}function ru(n,e){var t=n.dom,r=window.getComputedStyle(t).getPropertyValue(e);return""!==r||ot(n)?r:ju(t,e)}function ou(n,e){return n=n.dom,e=ju(n,e),H.from(e).filter(function(n){return 0<n.length})}function iu(n,e){var t=n.dom;Zi(t,e),Ee(n,"style").map(Rn).is("")&&Ce(n,"style")}function uu(n){return n.dom.value}function cu(n,e){if(void 0===e)throw new Error("Value.set was undefined");n.dom.value=e}function au(n){var e,t,r=I.fromTag(n.tag);e=r,o=n.attributes,t=e.dom,Ze(o,function(n,e){xe(t,e,n)}),Xi(r,n.classes),eu(r,n.styles),n.innerHtml.each(function(n){return dr(r,n)});var o=n.domChildren;return sr(r,o),n.value.each(function(n){cu(r,n)}),n.uid,Ai(r,n.uid),r}function su(n,e){return t=n,e=x(n=e,function(n){return Pt(n.name(),[Ft("config"),jt("state",$r)])}),e=Tt("component.behaviours",Lr(e),t.behaviours).fold(function(n){throw new Error(Gr(n)+"\nComplete spec:\n"+JSON.stringify(t,null,2))},function(n){return n}),{list:n,data:ye(e,function(n){var e=n.map(function(n){return{config:n.config,state:n.state.init(n.config)}});return function(){return e}})};var t}function fu(n){var e,t=(e=tt(t=n,"behaviours").getOr({}),t=S(Qe(e),function(n){return void 0!==e[n]}),x(t,function(n){return e[n].me}));return su(n,t)}function lu(n,e,t){var r,o=Z(Z({},(r=n).dom),{uid:r.uid,domChildren:x(r.components,function(n){return n.element})}),n={"alloy.base.modification":r=n.domModification.fold(function(){return nr({})},nr)};return r=r=0<e.length?Wi(t,n,e,o):r,Z(Z({},o=o),{attributes:Z(Z({},o.attributes),r.attributes),styles:Z(Z({},o.styles),r.styles),classes:o.classes.concat(r.classes)})}function du(n,e,t){var r={"alloy.base.behaviour":n.events};return function(n,e,t,r){r=qi(n,t,r);return Pu(r,e)}(t,n.eventOrder,e,r).getOrDie()}function mu(n){var t,e,r,o,i,u,c,a=(s=Nu(n)).events,s=function(n){n=tt(n,"components").getOr([]);return x(n,Wu)}(n=nn(s,["events"])),a=Z(Z({},n),{events:Z(Z({},Ru),a),components:s});return Ye.value((e=fe(Bu),r=Mt(Ki(t=a)),s=fu(t),a=s.list,o=s.data,s=lu(r,a,o),i=au(s),a=du(r,a,o),u=fe(r.components),c={getSystem:e.get,config:function(n){var e=o;return(K(e[n.name()])?e[n.name()]:function(){throw new Error("Could not find "+n.name()+" in "+JSON.stringify(t,null,2))})()},hasConfigured:function(n){return K(o[n.name()])},spec:t,readState:function(n){return o[n]().map(function(n){return n.state.readState()}).getOr("not enabled")},getApis:function(){return r.apis},connect:function(n){e.set(n)},disconnect:function(){e.set(Ii(f))},element:i,syncComponents:function(){var n=pn(i),n=M(n,function(n){return e.get().getByDom(n).fold(function(){return[]},function(n){return[n]})});u.set(n)},components:u.get,events:a}));function f(){return c}}function gu(n){return n.dump}function pu(n,e){return Z(Z({},n.dump),Lo(e))}function hu(){return Nt("markers",[Ft("backgroundMenu")].concat(Yu()).concat(Ku()))}function vu(n,e,t){return function(){var n=new Error;if(void 0===n.stack)return;n=n.stack.split("\n");k(n,function(e){return 0<e.indexOf("alloy")&&!p(Xo,function(n){return-1<e.indexOf(n)})}).getOr(qo)}(),Ur(e,e,t,Ct(function(t){return Ye.value(function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t.apply(void 0,n)})}))}function bu(n,e){return Vt(n,y(e))}function yu(n){return Vt(n,f)}function _u(n){return rt(n,"uiType")}function wu(n,e,t,r){return _u(t)&&t.uiType===Qu?(i=t,u=r,(o=n).exists(function(n){return n!==i.owner})?$u.single(!0,y(i)):tt(u,i.name).fold(function(){throw new Error("Unknown placeholder component: "+i.name+"\nKnown: ["+Qe(u)+"]\nNamespace: "+o.getOr("none")+"\nSpec: "+JSON.stringify(i,null,2))},function(n){return n.replace()})):$u.single(!1,y(t));var o,i,u}function xu(e,t,n,r){var o,i,u,r=ye(r,function(n,e){return r=n,o=!1,{name:y(t=e),required:function(){return r.fold(function(n,e){return n},function(n,e){return n})},used:function(){return o},replace:function(){if(o)throw new Error("Trying to use the same placeholder more than once: "+t);return o=!0,r}};var t,r,o}),n=(o=e,i=t,u=r,M(n,function(n){return Zu(o,i,n,u)}));return Ze(r,function(n){if(!1===n.used()&&n.required())throw new Error("Placeholder: "+n.name()+" was not found in components list\nNamespace: "+e.getOr("none")+"\nComponents: "+JSON.stringify(t.components,null,2))}),n}function Ou(n){function e(n){return n.name}return n.fold(e,e,e,e)}function Su(n,e,t,r){return Mr(e.defaults(n,t,r),t,{uid:n.partUids[e.name]},e.overrides(n,t,r))}function Eu(o,n){var e={};return O(n,function(n){n.fold(H.some,H.none,H.some,H.some).each(function(t){var r=ac(o,t.pname);e[t.name]=function(n){var e=Dt("Part: "+t.name+" in "+o,Lr(t.schema),n);return Z(Z({},r),{config:n,validated:e})}})}),e}function ku(n,e,t){return r=e,i={},o={},O(t,function(n){n.fold(function(r){i[r.pname]=nc(!0,function(n,e,t){return r.factory.sketch(Su(n,r,e,t))})},function(n){var e=r.parts[n.name];o[n.name]=y(n.factory.sketch(Su(r,n,e[cc()]),e))},function(r){i[r.pname]=nc(!1,function(n,e,t){return r.factory.sketch(Su(n,r,e,t))})},function(o){i[o.pname]=ec(!0,function(e,n,t){var r=e[o.name];return x(r,function(n){return o.factory.sketch(Mr(o.defaults(e,n,t),n,o.overrides(e,n)))})})})}),{internals:y(i),externals:y(o)};var r,i,o}function Cu(n,e,t){return xu(H.some(n),e,e.components,t)}function Tu(e,n){return n=x(n,Ou),gt(x(n,function(n){return{key:n,value:e+"-"+n}}))}function Mu(e){return Ur("partUids","partUids",Br(function(n){return Tu(n.uid,e)}),Yr())}function Du(n,e,t,r,o){return o=o,o=(0<(r=r).length?[Nt("parts",r)]:[]).concat([Ft("uid"),jt("dom",{}),jt("components",[]),yu("originalSpec"),jt("debug.sketcher",{})]).concat(o),Dt(n+" [SpecSchema]",Ot(o.concat(e)),t)}function Au(n,e,t,r,o){var i=sc(o),u=M(t,function(n){return n.fold(H.none,H.some,H.none,H.none).map(function(n){return Nt(n.name,n.schema.concat([yu(cc())]))}).toArray()}),o=Mu(t),o=Du(n,e,i,u,[o]),t=ku(0,o,t);return r(o,Cu(n,o,t.internals()),i,t.externals())}var Fu=Ro([{key:po(),value:zt({can:function(n,e){var t=e.event,r=t.originator,o=t.target;return e=o,!(sn(t=r,n.element)&&!sn(t,e))||(console.warn(po()+" did not get interpreted by the desired target. \nOriginator: "+gr(r)+"\nTarget: "+gr(o)+"\nCheck the "+po()+" event handlers"),!1)}})}]),Ru=Object.freeze({__proto__:null,events:Fu}),Nu=f,Bu=Ii(),Hu=wi("alloy-premade"),Pu=function(n,o){n=et(n,function(t,r){return(1===t.length?Ye.value(t[0].handler):Yi(t,o,r)).map(function(n){var e=Gi(n),n=1<t.length?S(o[r],function(e){return p(t,function(n){return n.name===e})}).join(" > "):t[0].name;return Hr(r,{handler:e,purpose:n})})});return pt(n,{})},Iu="alloy.base.behaviour",ju=function(n,e){return Qi(n)?n.style.getPropertyValue(e):""},Lu=function(n){var e=Dt("external.component",Ot([Ft("element"),Bt("uid")]),n),t=fe(Ii());e.uid.each(function(n){Ai(e.element,n)});var r={getSystem:t.get,config:H.none,hasConfigured:s,connect:function(n){t.set(n)},disconnect:function(){t.set(Ii(function(){return r}))},getApis:function(){return{}},element:e.element,spec:n,readState:y("No state"),syncComponents:h,components:y([]),events:{}};return ji(r)},Vu=Ri,Wu=function(e){return tt(e,Hu).fold(function(){var n=e.hasOwnProperty("uid")?e:Z({uid:Vu("")},e);return mu(n).getOrDie()},function(n){return n})},Uu=ji,zu=function(r,n){return e=r,t={},n=x(n,function(n){return e=n.name(),t="Cannot configure "+n.name()+" for "+r,Ur(e,e,Rr(),xt(function(n){return wr("The field: "+e+" is forbidden. "+t)}));var e,t}).concat([Vt("dump",f)]),Lt(e,t,Lr(n));var e,t},qu=zu,Gu=pu,Yu=y([Ft("menu"),Ft("selectedMenu")]),Ku=y([Ft("item"),Ft("selectedItem")]),Xu=(y(Lr(Ku().concat(Yu()))),y(Lr(Ku()))),Ju=Nt("initSize",[Ft("numColumns"),Ft("numRows")]),n=function(n){return vu(0,n,dt(h))},W=function(n){return vu(0,n,dt(H.none))},U=function(n){return vu(0,n,Fr())},Nn=function(n){return vu(0,n,Fr())},Bn=y(Ju),Qu="placeholder",$u=kr([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),Zu=function(r,o,i,u){return wu(r,0,i,u).fold(function(n,e){var t=_u(i)?e(o,i.config,i.validated):e(o),e=tt(t,"components").getOr([]),e=M(e,function(n){return Zu(r,o,n,u)});return[Z(Z({},t),{components:e})]},function(n,e){if(_u(i)){var t=e(o,i.config,i.validated);return i.validated.preprocess.getOr(f)(t)}return e(o)})},nc=$u.single,ec=$u.multiple,tc=y(Qu),Hn=kr([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),Pn=jt("factory",{sketch:f}),Qn=jt("schema",[]),re=Ft("name"),rc=Ur("pname","pname",Nr(function(n){return"<alloy."+wi(n.name)+">"}),Yr()),pe=Wr("schema",function(){return[Bt("preprocess")]}),he=jt("defaults",y({})),Xe=jt("overrides",y({})),oc=Lr([Pn,Qn,re,rc,he,Xe]),ic=Lr([Pn,Qn,re,he,Xe]),Tr=Lr([Pn,Qn,re,rc,he,Xe]),zr=Lr([Pn,pe,re,Ft("unit"),rc,he,Xe]),Kr=function(e,t){return function(n){n=Dt("Converting part type",t,n);return e(n)}},eo=Kr(Hn.required,oc),go=Kr(Hn.external,ic),mo=Kr(Hn.optional,Tr),uc=Kr(Hn.group,zr),cc=y("entirety"),ac=function(n,e){return{uiType:tc(),owner:n,name:e}},sc=function(n){return rt(n,"uid")?n:Z(Z({},n),{uid:Ri("uid")})};function fc(n,e){se(n.element,e.element)}function lc(e,n){var t,r=e.components();O((t=e).components(),function(n){return zo(n.element)}),fr(t.element),t.syncComponents(),r=F(r,n),O(r,function(n){xc(n),e.getSystem().removeFromWorld(n)}),O(n,function(n){n.getSystem().isConnected()?fc(e,n):(e.getSystem().addToWorld(n),fc(e,n),ot(e.element)&&Oc(n)),e.syncComponents()})}function dc(n,e){Sc(n,e,se)}function mc(n){xc(n),zo(n.element),n.getSystem().removeFromWorld(n)}function gc(e){var n=gn(e.element).bind(function(n){return e.getSystem().getByDom(n).toOptional()});mc(e),n.each(function(n){n.syncComponents()})}function pc(n){var e=n.components();O(e,mc),fr(n.element),n.syncComponents()}function hc(t){function r(e){return gn(t.element).fold(function(){return!0},function(n){return sn(e,n)})}function a(n,e){return i.find(r,n,e)}function o(e){var n=i.filter(bo());O(n,function(n){n=n.descHandler;Yo(n)(e)})}var i=Pi(),n=vi(t.element,{triggerEvent:function(t,r){return Go(t,r.target,function(n){return e=n,Ei(a,t,n=r,n.target,e);var e})}}),u={debugInfo:y("real"),triggerEvent:function(e,t,r){Go(e,t,function(n){return Ei(a,e,r,t,n)})},triggerFocus:function(u,c){Fi(u).fold(function(){ur(u)},function(n){Go(po(),u,function(n){var e,t,r,o,i;return e=a,t=po(),i=n,n=bi(r={originator:c,kill:h,prevent:h,target:u},o=u),yi(e,t,r,o,n,i),!1})})},triggerEscape:function(n,e){u.triggerEvent("keydown",n.element,e.event)},getByUid:function(n){return g(n)},getByDom:function(n){return p(n)},build:Wu,addToGui:function(n){s(n)},removeFromGui:function(n){f(n)},addToWorld:function(n){e(n)},removeFromWorld:function(n){c(n)},broadcast:function(n){l(n)},broadcastOn:function(n,e){d(n,e)},broadcastEvent:function(n,e){m(n,e)},isConnected:v},e=function(n){n.connect(u),Zn(n.element)||(i.register(n),O(n.components(),e),u.triggerEvent(So(),n.element,{target:n.element}))},c=function(n){Zn(n.element)||(O(n.components(),c),i.unregister(n)),n.disconnect()},s=function(n){dc(t,n)},f=function(n){gc(n)},l=function(n){o({universal:!0,data:n})},d=function(n,e){o({universal:!1,channels:n,data:e})},m=function(n,e){n=i.filter(n);return _i(n,e)},g=function(n){return i.getById(n).fold(function(){return Ye.error(new Error('Could not find component with uid: "'+n+'" in system.'))},Ye.value)},p=function(n){n=Fi(n).getOr("not found");return g(n)};return e(t),{root:t,element:t.element,destroy:function(){n.unbind(),zo(t.element)},add:s,remove:f,getByUid:g,getByDom:p,addToWorld:e,removeFromWorld:c,broadcast:l,broadcastOn:d,broadcastEvent:m}}function vc(n){var e=void 0!==n.uid&&we(n,"uid")?n.uid:Ri("memento");return{get:function(n){return n.getSystem().getByUid(e).getOrDie()},getOpt:function(n){return n.getSystem().getByUid(e).toOptional()},asSpec:function(){return Z(Z({},n),{uid:e})}}}function bc(n,e){return void 0!==n?n:void 0!==e?e:0}function yc(n){var e=(i=n.dom.ownerDocument).body,t=i.defaultView,r=i.documentElement;if(e===n.dom)return kc(e.offsetLeft,e.offsetTop);var o=bc(null==t?void 0:t.pageYOffset,r.scrollTop),i=bc(null==t?void 0:t.pageXOffset,r.scrollLeft),t=bc(r.clientTop,e.clientTop),e=bc(r.clientLeft,e.clientLeft);return Cc(n).translate(i-e,o-t)}var _c=Ot([Ft("name"),Ft("factory"),Ft("configFields"),jt("apis",{}),jt("extraApis",{})]),wc=Ot([Ft("name"),Ft("factory"),Ft("configFields"),Ft("partFields"),jt("apis",{}),jt("extraApis",{})]),Ci=function(n){var e=Dt("Sketcher for "+n.name,_c,n),t=ye(e.apis,Li),n=ye(e.extraApis,Zt);return Z(Z({name:e.name,configFields:e.configFields,sketch:function(n){return function(n,e,t,r){r=sc(r);return t(Du(n,e,r,[],[]),r)}(e.name,e.configFields,e.factory,n)}},t),n)},Ti=function(n){var e=Dt("Sketcher for "+n.name,wc,n),t=Eu(e.name,e.partFields),r=ye(e.apis,Li),n=ye(e.extraApis,Zt);return Z(Z({name:e.name,partFields:e.partFields,configFields:e.configFields,sketch:function(n){return Au(e.name,e.configFields,e.partFields,e.factory,n)},parts:t},r),n)},xc=(Ci({name:"Container",factory:function(n){var e=n.dom,t=e.attributes,e=nn(e,["attributes"]);return{uid:n.uid,dom:Z({tag:"div",attributes:Z({role:"presentation"},t)},e),components:n.components,behaviours:gu(n.containerBehaviours),events:n.events,domModification:n.domModification,eventOrder:n.eventOrder}},configFields:[jt("components",[]),zu("containerBehaviours",[]),jt("events",{}),jt("domModification",{}),jt("eventOrder",{})]}),function(n){Gt(n,ko());n=n.components();O(n,xc)}),Oc=function(n){var e=n.components();O(e,Oc),Gt(n,Eo())},Sc=function(n,e,t){n.getSystem().addToWorld(e),t(n.element,e.element),ot(n.element)&&Oc(e),n.syncComponents()},Fu=Object.freeze({__proto__:null,exhibit:function(n,e){return nr({classes:[],styles:e.useFixed()?{}:{position:"relative"}})}}),Ec=function(t,r){return{left:t,top:r,translate:function(n,e){return Ec(t+n,r+e)}}},kc=Ec,Cc=function(n){var e=n.dom,t=e.ownerDocument.body;return t===e?kc(t.offsetLeft,t.offsetTop):ot(n)?function(n){n=n.getBoundingClientRect();return kc(n.left,n.top)}(e):kc(0,0)};function Tc(t,r){function n(n){var e=r(n);if(e<=0||null===e){n=ru(n,t);return parseFloat(n)||0}return e}function o(t,n){return E(n,function(n,e){e=ru(t,e),e=void 0===e?0:parseInt(e,10);return isNaN(e)?n:n+e},0)}return{set:function(n,e){if(!X(e)&&!e.match(/^[0-9]+$/))throw new Error(t+".set accepts only positive integer values. Value was "+e);n=n.dom;Qi(n)&&(n.style[t]=e+"px")},get:n,getOuter:n,aggregate:o,max:function(n,e,t){t=o(n,t);return t<e?e-t:0}}}function Mc(n){return Ca.get(n)}function Dc(n){return Ca.getOuter(n)}function Ac(n){return Ta.get(n)}function Fc(n){return Ta.getOuter(n)}function Rc(n){var n=(e=void 0!==n?n.dom:document).body.scrollLeft||e.documentElement.scrollLeft,e=e.body.scrollTop||e.documentElement.scrollTop;return kc(n,e)}function Nc(n,e,t,r){return{x:n,y:e,width:t,height:r,right:n+t,bottom:e+r}}function Bc(n){var t=void 0===n?window:n,n=t.document,r=Rc(I.fromDom(n));return function(n){n=void 0===n?window:n;return H.from(n.visualViewport)}(t).fold(function(){var n=t.document.documentElement,e=n.clientWidth,n=n.clientHeight;return Nc(r.left,r.top,e,n)},function(n){return Nc(Math.max(n.pageLeft,r.left),Math.max(n.pageTop,r.top),n.width,n.height)})}function Hc(t){var n=I.fromDom(document),r=Rc(n);return function(n,e){n=e.owner(n),n=Ma(e,n);return H.some(n)}(t,Da).fold(d(yc,t),function(n){var e=Cc(t),n=b(n,function(n,e){e=Cc(e);return{left:n.left+e.left,top:n.top+e.top}},{left:0,top:0});return kc(n.left+e.left+r.left,n.top+e.top+r.top)})}function Pc(n,e,t,r){return{x:n,y:e,width:t,height:r,right:n+t,bottom:e+r}}function Ic(n){var e=yc(n),t=Fc(n),n=Dc(n);return Pc(e.left,e.top,t,n)}function jc(){return Bc(window)}function Lc(n,t){var r=ie(t),e=cr(r).bind(function(e){function n(n){return sn(e,n)}var r,o;return n(t)?H.some(t):(r=n,(o=function(n){for(var e=0;e<n.childNodes.length;e++){var t=I.fromDom(n.childNodes[e]);if(r(t))return H.some(t);t=o(n.childNodes[e]);if(t.isSome())return t}return H.none()})(t.dom))}),n=n(t);return e.each(function(e){cr(r).filter(function(n){return sn(n,e)}).fold(function(){ur(e)},h)}),n}function Vc(n,e,t,r,o){return{position:n,left:e,top:t,right:r,bottom:o}}function Wc(n,e){function t(n){return n+"px"}tu(n,{position:H.some(e.position),left:e.left.map(t),top:e.top.map(t),right:e.right.map(t),bottom:e.bottom.map(t)})}function Uc(n,e,t,r,o,i){var u=e.x-t,c=e.y-r,a=o-(u+e.width),s=i-(c+e.height),f=H.some(u),l=H.some(c),d=H.some(a),m=H.some(s),g=H.none();return t=e.direction,r=function(){return Vc(n,f,l,g,g)},o=function(){return Vc(n,g,l,d,g)},i=function(){return Vc(n,f,g,g,m)},u=function(){return Vc(n,g,g,d,m)},c=function(){return Vc(n,f,l,g,g)},a=function(){return Vc(n,f,g,g,m)},s=function(){return Vc(n,f,l,g,g)},e=function(){return Vc(n,g,l,d,g)},t.fold(r,o,i,u,c,a,s,e)}function zc(n,e){var t=d(Hc,e),r=n.fold(t,t,function(){var n=Rc();return Hc(e).translate(-n.left,-n.top)}),n=Fc(e),t=Dc(e);return Pc(r.left,r.top,n,t)}function qc(n,e,t,r){return r<(e=n+e)?t:e<t?r:e}function Gc(n,e,t){return Math.min(Math.max(n,e),t)}function Yc(e,t){return function(n,e){for(var t={},r=0,o=n.length;r<o;r++){var i=n[r];t[String(i)]=e(i,r)}return t}(["left","right","top","bottom"],function(n){return tt(t,n).map(function(n){return function(n,e){switch(e){case 1:return n.x;case 0:return n.x+n.width;case 2:return n.y;case 3:return n.y+n.height}}(e,n)})})}function Kc(n,o,i){function e(e,t){var r="top"===e||"bottom"===e?i.top:i.left;return tt(o,e).bind(f).bind(function(n){return"left"===e||"top"===e?t<=n?H.some(n):H.none():n<=t?H.some(n):H.none()}).map(function(n){return n+r}).getOr(t)}var t=e("left",n.x),r=e("top",n.y),u=e("right",n.right),n=e("bottom",n.bottom);return Pc(t,r,u-t,n-r)}function Xc(n,e,t,r){var o=n.x,i=n.y,u=n.bubble.offset,c=u.left,a=u.top,s=(v=Kc(r,n.boundsRestriction,u)).y,f=v.bottom,l=v.x,d=v.right,m=(v=(m=o+c,p=b=i+a,g=e,h=t,u=(r=v).x,i=r.y,a=r.width,o=r.height,c=i<=p,t=(e=u<=m)&&c,v=m+g<=u+a&&p+h<=i+o,u=Math.abs(Math.min(g,e?u+a-m:u-(m+g))),i=Math.abs(Math.min(h,c?i+o-p:i-(p+h))),g=Math.max(r.x,r.right-g),h=Math.max(r.y,r.bottom-h),{originInBounds:t,sizeInBounds:v,limitX:Gc(m,r.x,g),limitY:Gc(p,r.y,h),deltaW:u,deltaH:i})).originInBounds,g=v.sizeInBounds,p=v.limitX,h=v.deltaW,i=y((r=v.limitY)+(u=v.deltaH)-s),v=y(f-r),v=(s=n.direction,v=f=v,i=i,s.fold(f,f,i,i,f,i,v,v)),l=y(p+h-l),d=y(d-p),b={x:p,y:r,width:h,height:u,maxHeight:v,maxWidth:(r=n.direction,d=v=d,l=l,r.fold(v,l,v,l,d,d,v,l)),direction:n.direction,classes:{on:n.bubble.classesOn,off:n.bubble.classesOff},label:n.label,candidateYforTest:b};return m&&g?Wa.fit(b):Wa.nofit(b,h,u)}function Jc(n,e,t,u,c){function r(n,r,o,i){return n=n(e,t,u),Xc(n,a,s,c).fold(Wa.fit,function(n,e,t){return i<t||o<e?Wa.nofit(n,e,t):Wa.nofit(r,o,i)})}var a=t.width,s=t.height;return E(n,function(n,e){e=d(r,e);return n.fold(Wa.fit,e)},Wa.nofit({x:e.x,y:e.y,width:t.width,height:t.height,maxHeight:t.height,maxWidth:t.width,direction:Aa(),classes:{on:[],off:[]},label:"none",candidateYforTest:e.y},-1,-1)).fold(f,f)}function Qc(n,e,t,r){return iu(e,"max-height"),iu(e,"max-width"),e={width:Fc(e=e),height:Dc(e)},Jc(r.preference,n,e,t,r.bounds)}function $c(n,e,t){var o;Wc(n,(t=t.origin,o=e,t.fold(function(){return Vc("absolute",H.some(o.x),H.some(o.y),H.none(),H.none())},function(n,e,t,r){return Uc("absolute",o,n,e,t,r)},function(n,e,t,r){return Uc("fixed",o,n,e,t,r)})))}function Zc(n,e){!function(n,e){e=Ca.max(n,e,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"]);nu(n,"max-height",e+"px")}(n,Math.floor(e))}function na(n,e,t){return void 0===n[e]?t:n[e]}function ea(n,e,t,r,o,i){var u,c=na(i,"maxHeightFunction",Ua()),a=na(i,"maxWidthFunction",h),i=n.anchorBox,n=n.origin,a={bounds:(u=n,o.fold(function(){return u.fold(jc,jc,Pc)},function(t){return u.fold(t,t,function(){var n=t(),e=ja(u,n.x,n.y);return Pc(e.left,e.top,n.width,n.height)})})),origin:n,preference:r,maxHeightFunction:c,maxWidthFunction:a};za(i,e,t,a)}function ta(n,e,t){function o(n){return tt(t,n).getOr([])}function r(n,e,t){var r=F(qa,t);return{offset:kc(n,e),classesOn:M(t,o),classesOff:M(r,o)}}return{southeast:function(){return r(-n,e,["top","alignLeft"])},southwest:function(){return r(n,e,["top","alignRight"])},south:function(){return r(-n/2,e,["top","alignCentre"])},northeast:function(){return r(-n,-e,["bottom","alignLeft"])},northwest:function(){return r(n,-e,["bottom","alignRight"])},north:function(){return r(-n/2,-e,["bottom","alignCentre"])},east:function(){return r(n,-e/2,["valignCentre","left"])},west:function(){return r(-n,-e/2,["valignCentre","right"])},innerNorthwest:function(){return r(-n,e,["top","alignRight"])},innerNortheast:function(){return r(n,e,["top","alignLeft"])},innerNorth:function(){return r(-n/2,e,["top","alignCentre"])},innerSouthwest:function(){return r(-n,-e,["bottom","alignRight"])},innerSoutheast:function(){return r(n,-e,["bottom","alignLeft"])},innerSouth:function(){return r(-n/2,-e,["bottom","alignCentre"])},innerWest:function(){return r(n,-e/2,["valignCentre","right"])},innerEast:function(){return r(-n,-e/2,["valignCentre","left"])}}}function ra(){return ta(0,0,{})}function oa(n,e,t,r,o,i){return{x:n,y:e,bubble:t,direction:r,boundsRestriction:o,label:i}}function ia(n,e){return n.x+n.width/2-e.width/2}function ua(n,e){return n.x+n.width-e.width}function ca(n,e){return n.y-e.height}function aa(n){return n.y+n.height}function sa(n,e){return n.y+n.height/2-e.height/2}function fa(n,e,t){return oa(n.x,aa(n),t.southeast(),Aa(),Yc(n,{left:1,top:3}),"layout-se")}function la(n,e,t){return oa(ua(n,e),aa(n),t.southwest(),Fa(),Yc(n,{right:0,top:3}),"layout-sw")}function da(n,e,t){return oa(n.x,ca(n,e),t.northeast(),Ra(),Yc(n,{left:1,bottom:2}),"layout-ne")}function ma(n,e,t){return oa(ua(n,e),ca(n,e),t.northwest(),Na(),Yc(n,{right:0,bottom:2}),"layout-nw")}function ga(n,e,t){return oa(ia(n,e),ca(n,e),t.north(),Ha(),Yc(n,{bottom:2}),"layout-n")}function pa(n,e,t){return oa(ia(n,e),aa(n),t.south(),Ba(),Yc(n,{top:3}),"layout-s")}function ha(n,e,t){return oa((r=n).x+r.width,sa(n,e),t.east(),Pa(),Yc(n,{left:0}),"layout-e");var r}function va(n,e,t){return oa((r=e,n.x-r.width),sa(n,e),t.west(),Ia(),Yc(n,{right:1}),"layout-w");var r}function ba(){return[fa,la,da,ma,pa,ga,ha,va]}function ya(){return[la,fa,ma,da,pa,ga,ha,va]}function _a(){return[da,ma,fa,la,ga,pa]}function wa(){return[ma,da,la,fa,ga,pa]}function xa(){return[fa,la,da,ma,pa,ga]}function Oa(){return[la,fa,ma,da,pa,ga]}function Sa(e,t){return function(n){return"rtl"===Ga(n)?t:e}}function Ea(n,e,t){return ni(n,e,t).isSome()}var ka,Ca=Tc("height",function(n){var e=n.dom;return ot(n)?e.getBoundingClientRect().height:e.offsetHeight}),Ta=Tc("width",function(n){return n.dom.offsetWidth}),Ma=function(t,n){return t.view(n).fold(y([]),function(n){var e=t.owner(n),e=Ma(t,e);return[n].concat(e)})},Da=Object.freeze({__proto__:null,view:function(n){return(n.dom===document?H.none():H.from(null===(n=n.dom.defaultView)||void 0===n?void 0:n.frameElement)).map(I.fromDom)},owner:ln}),Ju=kr([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),Aa=Ju.southeast,Fa=Ju.southwest,Ra=Ju.northeast,Na=Ju.northwest,Ba=Ju.south,Ha=Ju.north,Pa=Ju.east,Ia=Ju.west,Nr=kr([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),ja=function(n,e,t){var r=kc(e,t);return n.fold(y(r),y(r),function(){var n=Rc();return r.translate(-n.left,-n.top)})},La=(Nr.none,Nr.relative),Va=Nr.fixed,Wa=kr([{fit:["reposition"]},{nofit:["reposition","deltaW","deltaH"]}]),Ua=y(function(n,e){Zc(n,e),eu(n,{"overflow-x":"hidden","overflow-y":"auto"})}),za=function(n,e,t,r){var o=Qc(n,e,t,r);$c(e,o,r),function(n,e){e=e.classes;Ji(n,e.off),Xi(n,e.on)}(e,o),n=e,t=o,(0,r.maxHeightFunction)(n,t.maxHeight),e=e,o=o,(0,r.maxWidthFunction)(e,o.maxWidth)},qa=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right"],Ga=function(n){return"rtl"===ru(n,"direction")?"rtl":"ltr"};(Qn=ka=ka||{}).TopToBottom="toptobottom",Qn.BottomToTop="bottomtotop";function Ya(n){return Ea(n,function(n){return $n(n)&&Se(n,"data-alloy-vertical-dir")===ka.BottomToTop})}function Ka(e,n,t,r,o,i,u){var c=u.map(Ya).getOr(!1),a=n.layouts.map(function(n){return n.onLtr(e)}),u=n.layouts.map(function(n){return n.onRtl(e)}),t=c?n.layouts.bind(function(n){return n.onBottomLtr.map(function(n){return n(e)})}).or(a).getOr(o):a.getOr(t),r=c?n.layouts.bind(function(n){return n.onBottomRtl.map(function(n){return n(e)})}).or(u).getOr(i):u.getOr(r);return Sa(t,r)(e)}function Xa(n,e,t){var r,o,n=n.document.createRange();return r=n,e.fold(function(n){r.setStartBefore(n.dom)},function(n,e){r.setStart(n.dom,e)},function(n){r.setStartAfter(n.dom)}),o=n,t.fold(function(n){o.setEndBefore(n.dom)},function(n,e){o.setEnd(n.dom,e)},function(n){o.setEndAfter(n.dom)}),n}function Ja(n,e,t,r,o){return(n=n.document.createRange()).setStart(e.dom,t),n.setEnd(r.dom,o),n}function Qa(n){return{left:n.left,top:n.top,right:n.right,bottom:n.bottom,width:n.width,height:n.height}}function $a(n,e,t){return e(I.fromDom(t.startContainer),t.startOffset,I.fromDom(t.endContainer),t.endOffset)}function Za(n,e){var o,t,e=(o=n,e.match({domRange:function(n){return{ltr:y(n),rtl:H.none}},relative:function(n,e){return{ltr:tn(function(){return Xa(o,n,e)}),rtl:tn(function(){return H.some(Xa(o,e,n))})}},exact:function(n,e,t,r){return{ltr:tn(function(){return Ja(o,n,e,t,r)}),rtl:tn(function(){return H.some(Ja(o,t,r,n,e))})}}}));return(t=(e=e).ltr()).collapsed?e.rtl().filter(function(n){return!1===n.collapsed}).map(function(n){return os.rtl(I.fromDom(n.endContainer),n.endOffset,I.fromDom(n.startContainer),n.startOffset)}).getOrThunk(function(){return $a(0,os.ltr,t)}):$a(0,os.ltr,t)}var Pn=function(){return Pt("layouts",[Ft("onLtr"),Ft("onRtl"),Bt("onBottomLtr"),Bt("onBottomRtl")])},pe=[Ft("hotspot"),Bt("bubble"),jt("overrides",{}),Pn(),bu("placement",function(n,e,t){var r=e.hotspot,r=zc(t,r.element),n=Ka(n.element,e,xa(),Oa(),_a(),wa(),H.some(e.hotspot.element));return H.some({anchorBox:r,bubble:e.bubble.getOr(ra()),overrides:e.overrides,layouts:n,placer:H.none()})})],re=[Ft("x"),Ft("y"),jt("height",0),jt("width",0),jt("bubble",ra()),jt("overrides",{}),Pn(),bu("placement",function(n,e,t){t=ja(t,e.x,e.y),t=Pc(t.left,t.top,e.width,e.height),n=Ka(n.element,e,ba(),ya(),ba(),ya(),H.none());return H.some({anchorBox:t,bubble:e.bubble,overrides:e.overrides,layouts:n,placer:H.none()})})],ns=function(n,e,t,r){return{start:n,soffset:e,finish:t,foffset:r}},rc=kr([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),es=(rc.before,rc.on,rc.after,function(n){return n.fold(f,f,f)}),ts=kr([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),rs={domRange:ts.domRange,relative:ts.relative,exact:ts.exact,exactFromRange:function(n){return ts.exact(n.start,n.soffset,n.finish,n.foffset)},getWin:function(n){n=n.match({domRange:function(n){return I.fromDom(n.startContainer)},relative:function(n,e){return es(n)},exact:function(n,e,t,r){return n}});return mn(n)},range:ns},os=kr([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]);os.ltr,os.rtl;function is(n,e,t,r){var o,i,u,c,a=(i=e,u=t,c=r,(a=ln(o=n).dom.createRange()).setStart(o.dom,i),a.setEnd(u.dom,c),a),r=sn(n,t)&&e===r;return a.collapsed&&!r}function us(n){if(0<n.rangeCount){var e=n.getRangeAt(0),n=n.getRangeAt(n.rangeCount-1);return H.some(ns(I.fromDom(e.startContainer),e.startOffset,I.fromDom(n.endContainer),n.endOffset))}return H.none()}function cs(n){if(null===n.anchorNode||null===n.focusNode)return us(n);var e=I.fromDom(n.anchorNode),t=I.fromDom(n.focusNode);return is(e,n.anchorOffset,t,n.focusOffset)?H.some(ns(e,n.anchorOffset,t,n.focusOffset)):us(n)}function as(n){return n=n,H.from(n.getSelection()).filter(function(n){return 0<n.rangeCount}).bind(cs)}function ss(n,e){var i,n=Za(i=n,e).match({ltr:function(n,e,t,r){var o=i.document.createRange();return o.setStart(n.dom,e),o.setEnd(t.dom,r),o},rtl:function(n,e,t,r){var o=i.document.createRange();return o.setStart(t.dom,r),o.setEnd(n.dom,e),o}});return 0<(e=0<(n=(e=n).getClientRects()).length?n[0]:e.getBoundingClientRect()).width||0<e.height?H.some(e).map(Qa):H.none()}function fs(n,e){return{element:n,offset:e}}function ls(n,e){if(0===(t=pn(n)).length)return fs(n,e);if(e<t.length)return fs(t[e],0);var t=t[t.length-1],r=Zn(t)?(r=t,yf.get(r).length):pn(t).length;return fs(t,r)}function ds(n){return n.fold(f,function(n,e,t){return n.translate(-e,-t)})}function ms(n){return n.fold(f,f)}function gs(n){return E(n,function(n,e){return n.translate(e.left,e.top)},kc(0,0))}function ps(n){return n=x(n,ms),gs(n)}function hs(n,e,t){var r=ln(n.element),r=Rc(r),t=function(t,n){n=mn(n.root).dom;return H.from(n.frameElement).map(I.fromDom).filter(function(n){var e=ln(n),n=ln(t.element);return sn(e,n)}).map(yc)}(n,t).getOr(r);return wf(t,r.left,r.top)}function vs(n,e,t,r){var o=n,i=e,u=t,c=r;return n<0&&(o=0,u=t+n),e<0&&(i=0,c=r+e),i=_f(kc(o,i)),H.some({point:i,width:u,height:c})}function bs(n,i,u,c,a){return n.map(function(n){var e,t=[i,n.point],r=(e=function(){return ps(t)},r=function(){return ps(t)},o=function(){return function(n){n=x(n,ds);return gs(n)}(t)},c.fold(e,r,o)),o={x:r.left,y:r.top,width:n.width,height:n.height},r=(u.showAbove?_a:xa)(),n=(u.showAbove?wa:Oa)(),n=Ka(a,u,r,n,r,n,H.none());return{anchorBox:o,bubble:u.bubble.getOr(ra()),overrides:u.overrides,layouts:n,placer:H.none()}})}function ys(n,e){return Zn(n)?{element:n,offset:e}:ls(n,e)}function _s(n,e){return e.getSelection.getOrThunk(function(){return function(){return as(n)}})().map(function(n){var e=ys(n.start,n.soffset),n=ys(n.finish,n.foffset);return rs.range(e.element,e.offset,n.element,n.offset)})}function ws(n){return n.x+n.width}function xs(n,e){return n.x-e.width}function Os(n,e){return n.y-e.height+n.height}function Ss(n,e,t){return oa(ws(n),n.y,t.southeast(),Aa(),Yc(n,{left:0,top:2}),"link-layout-se")}function Es(n,e,t){return oa(xs(n,e),n.y,t.southwest(),Fa(),Yc(n,{right:1,top:2}),"link-layout-sw")}function ks(n,e,t){return oa(ws(n),Os(n,e),t.northeast(),Ra(),Yc(n,{left:0,bottom:3}),"link-layout-ne")}function Cs(n,e,t){return oa(xs(n,e),Os(n,e),t.northwest(),Na(),Yc(n,{right:1,bottom:3}),"link-layout-nw")}function Ts(){return[Ss,Es,ks,Cs]}function Ms(){return[Es,Ss,Cs,ks]}function Ds(n,e,t,r,o){e={anchorBox:t.anchorBox,origin:e},ea(e,o.element,t.bubble,t.layouts,r,t.overrides)}function As(n,e,t,r){r=n.getSystem().build(r),Sc(n,r,t)}function Fs(n,e,t,r){n=kf(n),k(n,function(n){return sn(r.element,n.element)}).each(gc)}function Rs(e,n,t,o,r){var i=kf(e);return H.from(i[o]).map(function(n){return Fs(e,0,0,n),r.each(function(n){As(e,0,function(n,e){var t,r;r=e,hn(t=n,o).fold(function(){se(t,r)},function(n){Sn(n,r)})},n)}),n})}function Ns(n){return!((n=n.dom).offsetWidth<=0&&n.offsetHeight<=0)}function Bs(n,e){e.ignore||(ur(n.element),e.onFocus(n))}function Hs(n,e,t){e.store.manager.onLoad(n,e,t)}function Ps(n,e,t){e.store.manager.onUnload(n,e,t)}function Is(n,e,t,r){var o=e.store;t.update([r]),o.setValue(n,r),e.onSetValue(n,r)}function js(n,e,t,r){return tt(e.routes,r.start).bind(function(n){return tt(n,r.destination)})}function Ls(e,t,n){var r,o,i;i=n,Bf(r=e,o=t).bind(function(n){return Nf(r,o,i,n)}).each(function(n){n=n.transition;Ne(e.element,n.transitionClass),Ce(e.element,t.destinationAttr)})}function Vs(n,e,t,r){Ls(n,e,t),ke(n.element,e.stateAttr)&&Se(n.element,e.stateAttr)!==r&&e.onFinish(n,r),Oe(n.element,e.stateAttr,r)}function Ws(n,e,t){(e=e.aria).update(n,e,t.get())}function Us(e,n,t){n.toggleClass.each(function(n){(t.get()?Fe:Ne)(e.element,n)})}function zs(n,e,t){If(n,e,t,!t.get())}function qs(n,e,t){t.set(!0),Us(n,e,t),Ws(n,e,t)}function Gs(n,e,t){t.set(!1),Us(n,e,t),Ws(n,e,t)}function Ys(n,e,t){If(n,e,t,e.selected)}function Ks(){function n(n,e){e.stop(),Kt(n)}return[Jt(fo(),n),Jt(wo(),n),$t(to()),$t(io())]}function Xs(n){return Ro(T([n.map(function(t){return Po(function(n,e){t(n),e.stop()})}).toArray(),Ks()]))}function Js(n,e,t){var r=A(n.slice(0,e)),e=A(n.slice(e+1));return k(r.concat(e),t)}function Qs(n,e,t){return e=A(n.slice(0,e)),k(e,t)}function $s(n,e,t){var r=n.slice(0,e),e=n.slice(e+1);return k(e.concat(r),t)}function Zs(n,e,t){return e=n.slice(e+1),k(e,t)}function nf(e){return function(n){n=n.raw;return w(e,n.which)}}function ef(n){return function(e){return D(n,function(n){return n(e)})}}function tf(n){return!0===n.raw.shiftKey}function rf(n){return!0===n.raw.ctrlKey}function of(n,e){return{matches:n,classification:e}}function uf(n){for(var e=[],t=function(n){e.push(n)},r=0;r<n.length;r++)n[r].each(t);return e}function cf(t,r,n,o){var e=He(t.element,"."+r.highlightClass);O(e,function(e){p(o,function(n){return n.element===e})||(Ne(e,r.highlightClass),t.getSystem().getByDom(e).each(function(n){r.onDehighlight(t,n),Gt(n,Ao())}))})}function af(n,e,t,r){cf(n,e,0,[r]),Wf(n,e,t,r)||(Fe(r.element,e.highlightClass),e.onHighlight(n,r),Gt(r,Do()))}function sf(e,t,n,r){var o=He(e.element,"."+t.itemClass);return C(o,function(n){return Be(n,t.highlightClass)}).bind(function(n){n=qc(n,r,0,o.length-1);return e.getSystem().getByDom(o[n]).toOptional()})}function ff(n,e,t){e.exists(function(e){return t.exists(function(n){return sn(n,e)})})||Yt(n,Mo(),{prevFocus:e,newFocus:t})}function lf(){function r(n){return ar(n.element)}return{get:r,set:function(n,e){var t=r(n);n.getSystem().triggerFocus(e,n.element);e=r(n);ff(n,t,e)}}}function df(){function r(n){return Yf.getHighlighted(n).map(function(n){return n.element})}return{get:r,set:function(e,n){var t=r(e);e.getSystem().getByDom(n).fold(h,function(n){Yf.highlight(e,n)});n=r(e);ff(e,t,n)}}}var mf,gf,pf,hf,vf,bf,yf=(mf=Zn,gf="text",{get:function(n){if(!mf(n))throw new Error("Can only get "+gf+" value of a "+gf+" node");return pf(n).getOr("")},getOption:pf=function(n){return mf(n)?H.from(n.dom.nodeValue):H.none()},set:function(n,e){if(!mf(n))throw new Error("Can only set raw "+gf+" value of a "+gf+" node");n.dom.nodeValue=e}}),he=kr([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),_f=he.screen,wf=he.absolute,Xe=[Bt("getSelection"),Ft("root"),Bt("bubble"),Pn(),jt("overrides",{}),jt("showAbove",!1),bu("placement",function(n,e,t){var r=mn(e.root).dom,o=hs(n,0,e),i=_s(r,e).bind(function(n){return ss(r,rs.exactFromRange(n)).orThunk(function(){var e=I.fromText("\ufeff");return Sn(n.start,e),ss(r,rs.exact(e,0,e,1)).map(function(n){return zo(e),n})}).bind(function(n){return vs(n.left,n.top,n.width,n.height)})}),n=_s(r,e).bind(function(n){return $n(n.start)?H.some(n.start):te(n.start)}).getOr(n.element);return bs(i,o,e,t,n)})],oc=[Ft("node"),Ft("root"),Bt("bubble"),Pn(),jt("overrides",{}),jt("showAbove",!1),bu("placement",function(t,r,o){var i=hs(t,0,r);return r.node.bind(function(n){var e=n.dom.getBoundingClientRect(),n=vs(e.left,e.top,e.width,e.height),e=r.node.getOr(t.element);return bs(n,i,r,o,e)})})],ic=[Ft("item"),Pn(),jt("overrides",{}),bu("placement",function(n,e,t){t=zc(t,e.item.element),n=Ka(n.element,e,Ts(),Ms(),Ts(),Ms(),H.none());return H.some({anchorBox:t,bubble:ra(),overrides:e.overrides,layouts:n,placer:H.none()})})],xf=At("anchor",{selection:Xe,node:oc,hotspot:pe,submenu:ic,makeshift:re}),Of=function(n,e,t,r,o,i){i=i.map(Ic);return Sf(n,e,t,r,o,i)},Sf=function(i,u,n,e,c,a){var s=Dt("positioning anchor.info",xf,e);Lc(function(){nu(c.element,"position","fixed");var n=ou(c.element,"visibility");nu(c.element,"visibility","hidden");var e,t=u.useFixed()?(e=document.documentElement,Va(0,0,e.clientWidth,e.clientHeight)):(e=yc((r=i).element),r=r.element.dom.getBoundingClientRect(),La(e.left,e.top,r.width,r.height)),r=s.placement,o=a.map(y).or(u.getBounds);r(i,s,t).each(function(n){n.placer.getOr(Ds)(i,t,n,o,c)}),n.fold(function(){iu(c.element,"visibility")},function(n){nu(c.element,"visibility",n)}),ou(c.element,"left").isNone()&&ou(c.element,"top").isNone()&&ou(c.element,"right").isNone()&&ou(c.element,"bottom").isNone()&&ou(c.element,"position").is("fixed")&&iu(c.element,"position")},c.element)},Tr=Object.freeze({__proto__:null,position:function(n,e,t,r,o){Of(n,e,t,r,o,H.none())},positionWithin:Of,positionWithinBounds:Sf,getMode:function(n,e,t){return e.useFixed()?"fixed":"absolute"}}),Kr=[jt("useFixed",s),Bt("getBounds")],Ef=or({fields:Kr,name:"positioning",active:Fu,apis:Tr}),kf=function(n,e){return n.components()},Cf=or({fields:[],name:"replacing",apis:Object.freeze({__proto__:null,append:function(n,e,t,r){As(n,0,se,r)},prepend:function(n,e,t,r){As(n,0,En,r)},remove:Fs,replaceAt:Rs,replaceBy:function(e,n,t,r,o){var i=kf(e);return C(i,r).bind(function(n){return Rs(e,0,0,n,o)})},set:function(e,n,t,r){Lc(function(){var n=x(r,e.getSystem().build);lc(e,n)},e.element)},contents:kf})}),Tf="comments.readonly",Mf="comments.resize",Hn=Object.freeze({__proto__:null,getCurrent:function(n,e,t){return e.find(n)}}),zr=[Ft("find")],Df=or({fields:zr,name:"composing",apis:Hn}),Ju=Object.freeze({__proto__:null,focus:Bs,blur:function(n,e){e.ignore||n.element.dom.blur()},isFocused:function(n){return e=n.element,n=ie(e).dom,e.dom===n.activeElement;var e}}),Nr=Object.freeze({__proto__:null,exhibit:function(n,e){e=e.ignore?{}:{attributes:{tabindex:"-1"}};return nr(e)},events:function(t){return Ro([Jt(po(),function(n,e){Bs(n,t),e.stop()})].concat(t.stopMousedown?[Jt(io(),function(n,e){e.event.prevent()})]:[]))}}),Qn=[n("onFocus"),jt("stopMousedown",!1),jt("ignore",!1)],Af=or({fields:Qn,name:"focusing",active:Nr,apis:Ju}),rc=Object.freeze({__proto__:null,onLoad:Hs,onUnload:Ps,setValue:function(n,e,t,r){e.store.manager.setValue(n,e,t,r)},getValue:function(n,e,t){return e.store.manager.getValue(n,e,t)},getState:function(n,e,t){return t}}),kr=Object.freeze({__proto__:null,events:function(t,r){var n=t.resetOnDom?[No(function(n,e){Hs(n,t,r)}),Bo(function(n,e){Ps(n,t,r)})]:[er(t,r,Hs)];return Ro(n)}}),he=function(){var n=fe(null);return Zr({set:n.set,get:n.get,isNotSet:function(){return null===n.get()},clear:function(){n.set(null)},readState:function(){return{mode:"memory",value:n.get()}}})},Xe=function(){var i=fe({}),u=fe({});return Zr({readState:function(){return{mode:"dataset",dataByValue:i.get(),dataByText:u.get()}},lookup:function(n){return tt(i.get(),n).orThunk(function(){return tt(u.get(),n)})},update:function(n){var e=i.get(),t=u.get(),r={},o={};O(n,function(e){r[e.value]=e,tt(e,"meta").each(function(n){tt(n,"text").each(function(n){o[n]=e})})}),i.set(Z(Z({},e),r)),u.set(Z(Z({},t),o))},clear:function(){i.set({}),u.set({})}})},oc=Object.freeze({__proto__:null,memory:he,dataset:Xe,manual:function(){return Zr({readState:function(){}})},init:function(n){return n.store.manager.state(n)}}),pe=[Bt("initialValue"),Ft("getFallbackEntry"),Ft("getDataKey"),Ft("setValue"),bu("manager",{setValue:Is,getValue:function(n,e,t){var r=e.store,o=r.getDataKey(n);return t.lookup(o).fold(function(){return r.getFallbackEntry(o)},function(n){return n})},onLoad:function(e,t,r){t.store.initialValue.each(function(n){Is(e,t,r,n)})},onUnload:function(n,e,t){t.clear()},state:Xe})],ic=[Ft("getValue"),jt("setValue",h),Bt("initialValue"),bu("manager",{setValue:function(n,e,t,r){e.store.setValue(n,r),e.onSetValue(n,r)},getValue:function(n,e,t){return e.store.getValue(n)},onLoad:function(e,t,n){t.store.initialValue.each(function(n){t.store.setValue(e,n)})},onUnload:h,state:$r.init})],re=[Bt("initialValue"),bu("manager",{setValue:function(n,e,t,r){t.set(r),e.onSetValue(n,r)},getValue:function(n,e,t){return t.get()},onLoad:function(n,e,t){e.store.initialValue.each(function(n){t.isNotSet()&&t.set(n)})},onUnload:function(n,e,t){t.clear()},state:he})],Kr=[Lt("store",{mode:"memory"},At("mode",{memory:re,manual:ic,dataset:pe})),n("onSetValue"),jt("resetOnDom",!1)],Ff=or({fields:Kr,name:"representing",active:kr,apis:rc,extra:{setValueFrom:function(n,e){e=Ff.getValue(e);Ff.setValue(n,e)}},state:oc}),Fu=Object.freeze({__proto__:null,exhibit:function(n,e){return nr({attributes:gt([{key:e.tabAttr,value:"true"}])})}}),Tr=[jt("tabAttr","data-alloy-tabstop")],Rf=or({fields:Tr,name:"tabstopping",active:Fu}),Nf=function(n,e,t,r){return js(0,e,0,r).bind(function(e){return e.transition.map(function(n){return{transition:n,route:e}})})},Bf=function(e,t,n){var r=e.element;return Ee(r,t.destinationAttr).map(function(n){return{start:Se(e.element,t.stateAttr),destination:n}})},zr=Object.freeze({__proto__:null,findRoute:js,disableTransition:Ls,getCurrentRoute:Bf,jumpTo:Vs,progressTo:function(e,t,r,o){var i,u;u=t,ke((i=e).element,u.destinationAttr)&&(Ee(i.element,u.destinationAttr).each(function(n){Oe(i.element,u.stateAttr,n)}),Ce(i.element,u.destinationAttr));var n,c,c=(n=t,c=o,{start:Se(e.element,n.stateAttr),destination:c});Nf(e,t,r,c).fold(function(){Vs(e,t,r,o)},function(n){Ls(e,t,r);n=n.transition;Fe(e.element,n.transitionClass),Oe(e.element,t.destinationAttr,o)})},getState:function(n,e,t){return Ee(n.element,e.stateAttr)}}),Hn=Object.freeze({__proto__:null,events:function(o,i){return Ro([Jt(lo(),function(t,n){var r=n.event.raw;Bf(t,o).each(function(e){js(0,o,0,e).each(function(n){n.transition.each(function(n){r.propertyName===n.property&&(Vs(t,o,i,e.destination),o.onTransition(t,e))})})})}),No(function(n,e){Vs(n,o,i,o.initialState)})])}}),Qn=[jt("destinationAttr","data-transitioning-destination"),jt("stateAttr","data-transitioning-state"),Ft("initialState"),n("onTransition"),n("onFinish"),Rt("routes",qr(Ye.value,qr(Ye.value,Ot([It("transition",[Ft("property"),Ft("transitionClass")])]))))],Hf=or({fields:Qn,name:"transitioning",active:Hn,apis:zr,extra:{createRoutes:function(n){var t={};return Ze(n,function(n,e){e=e.split("<->");t[e[0]]=Hr(e[1],n),t[e[1]]=Hr(e[0],n)}),t},createBistate:function(n,e,t){return gt([{key:n,value:Hr(e,t)},{key:e,value:Hr(n,t)}])},createTristate:function(n,e,t,r){return gt([{key:n,value:gt([{key:e,value:r},{key:t,value:r}])},{key:e,value:gt([{key:n,value:r},{key:t,value:r}])},{key:t,value:gt([{key:n,value:r},{key:e,value:r}])}])}}}),Nr=Jn().os.isOSX()?"⌘":"Ctrl",Pf=ye({tc_menu_name:"TinyComments",tc_announce_sidebar_available:"Comment. Sidebar available. Press "+Nr+" + Alt + M to switch to sidebar",tc_items_addcomment:"Add comment",tc_items_showcomments:"Show comments",tc_items_deleteall:"Delete all conversations",tc_edit_buttons_save:"Save",tc_edit_buttons_cancel:"Cancel",tc_reply_buttons_save:"Save",tc_reply_buttons_clear:"Clear",tc_reply_placeholders:"Say something ...",tc_kebab_deleteconversation:"Delete conversation",tc_kebab_delete:"Delete",tc_kebab_edit:"Edit",tc_edit_problem_comment:"An error occurred editing this comment. See the Console for details.",tc_edit_unauthorised_comment:"You are not allowed to edit this comment",tc_delete_buttons_cancel:"Cancel",tc_delete_buttons_proceed:"Delete",tc_create_problem:"An error occurred while creating a comment. See the Console for details",tc_reply_problem:"An error occurred while replying to a comment. See the Console for details",tc_delete_prompts_conversation:"Delete this conversation?",tc_delete_prompts_conversation_detail_sing:"1 comment will be deleted. You can't undo this action.",tc_delete_prompts_conversation_detail_pl:"{0} comments will be deleted. You can't undo this action.",tc_delete_prompts_all:"Delete all conversations in the content? This cannot be undone",tc_delete_prompts_comment:"Are you sure you want to delete this comment?",tc_delete_problem_all:"An error occurred deleting all the conversations. See the Console for details.",tc_delete_problem_conversation:"An error occurred deleting the conversation. See the Console for details.",tc_delete_problem_comment:"An error occurred deleting the comment. See the Console for details.",tc_delete_unauthorised_all:"You are not allowed to delete all the conversations",tc_delete_unauthorised_conversation:"You are not allowed to delete this conversation",tc_delete_unauthorised_comment:"You are not allowed to delete this comment",tc_date_less_than_a_minute_ago:"a moment ago",tc_date_1_minute_ago:"1 minute ago",tc_date_x_minutes_ago:"{0} minutes ago",tc_date_1_hour_ago:"1 hour ago",tc_date_x_hours_ago:"{0} hours ago",tc_date_1_day_ago:"1 day ago",tc_date_x_days_ago:"{0} days ago",tc_date_1_week_ago:"1 week ago",tc_date_x_weeks_ago:"{0} weeks ago",tc_date_1_month_ago:"1 month ago",tc_date_x_months_ago:"{0} months ago",tc_date_1_year_ago:"1 year ago",tc_date_x_years_ago:"{0} years ago",tc_date_comment_edited:" (edited)",tc_comment_buttons_showmore:"SHOW MORE",tc_comment_buttons_showless:"SHOW LESS"},f),If=function(n,e,t,r){(r?qs:Gs)(n,e,t)},Ju=Object.freeze({__proto__:null,onLoad:Ys,toggle:zs,isOn:function(n,e,t){return t.get()},on:qs,off:Gs,set:If}),Xe=Object.freeze({__proto__:null,exhibit:function(){return nr({})},events:function(n,e){var t,r,o,i=(t=n,r=e,o=zs,Po(function(n){o(n,t,r)})),e=er(n,e,Ys);return Ro(T([n.toggleOnExecute?[i]:[],[e]]))}}),jf=function(n,e,t){Oe(n.element,"aria-expanded",t)},he=[jt("selected",!1),Bt("toggleClass"),jt("toggleOnExecute",!0),Lt("aria",{mode:"none"},At("mode",{pressed:[jt("syncWithExpanded",!1),bu("update",function(n,e,t){Oe(n.element,"aria-pressed",t),e.syncWithExpanded&&jf(n,e,t)})],checked:[bu("update",function(n,e,t){Oe(n.element,"aria-checked",t)})],expanded:[bu("update",jf)],selected:[bu("update",function(n,e,t){Oe(n.element,"aria-selected",t)})],none:[bu("update",h)]}))],Lf=or({fields:he,name:"toggling",active:Xe,apis:Ju,state:(hf=!1,{init:function(){var e=fe(hf);return{get:function(){return e.get()},set:function(n){return e.set(n)},clear:function(){return e.set(hf)},readState:function(){return e.get()}}}})}),Vf=(vf=tf,function(n){return!vf(n)}),Wf=function(n,e,t,r){return Be(r.element,e.highlightClass)},Uf=function(n,e,t,r){e=He(n.element,"."+e.itemClass);return H.from(e[r]).fold(function(){return Ye.error("No element found with index "+r)},n.getSystem().getByDom)},zf=function(e,n,t){return ti(e.element,"."+n.itemClass).bind(function(n){return e.getSystem().getByDom(n).toOptional()})},qf=function(e,n,t){n=He(e.element,"."+n.itemClass);return(0<n.length?H.some(n[n.length-1]):H.none()).bind(function(n){return e.getSystem().getByDom(n).toOptional()})},Gf=function(e,n,t){n=He(e.element,"."+n.itemClass);return uf(x(n,function(n){return e.getSystem().getByDom(n).toOptional()}))},re=Object.freeze({__proto__:null,dehighlightAll:function(n,e,t){return cf(n,e,0,[])},dehighlight:function(n,e,t,r){Wf(n,e,t,r)&&(Ne(r.element,e.highlightClass),e.onDehighlight(n,r),Gt(r,Ao()))},highlight:af,highlightFirst:function(e,t,r){zf(e,t).each(function(n){af(e,t,r,n)})},highlightLast:function(e,t,r){qf(e,t).each(function(n){af(e,t,r,n)})},highlightAt:function(e,t,r,n){Uf(e,t,r,n).fold(function(n){throw new Error(n)},function(n){af(e,t,r,n)})},highlightBy:function(e,t,r,n){var o=Gf(e,t);k(o,n).each(function(n){af(e,t,r,n)})},isHighlighted:Wf,getHighlighted:function(e,n,t){return ti(e.element,"."+n.highlightClass).bind(function(n){return e.getSystem().getByDom(n).toOptional()})},getFirst:zf,getLast:qf,getPrevious:function(n,e,t){return sf(n,e,0,-1)},getNext:function(n,e,t){return sf(n,e,0,1)},getCandidates:Gf}),ic=[Ft("highlightClass"),Ft("itemClass"),n("onHighlight"),n("onDehighlight")],Yf=or({fields:ic,name:"highlighting",apis:re});(pe=bf=bf||{}).OnFocusMode="onFocus",pe.OnEnterOrSpaceMode="onEnterOrSpace",pe.OnApiMode="onApi";function Kf(n,e,t,r,c){function a(e,t,n,r,o){var i;return n=n=n(e,t,r,o),i=t.event,k(n,function(n){return n.matches(i)}).map(function(n){return n.classification}).bind(function(n){return n(e,t,r,o)})}var o={schema:function(){return n.concat([jt("focusManager",lf()),Lt("focusInside","onFocus",Ct(function(n){return w(["onFocus","onEnterOrSpace","onApi"],n)?Ye.value(n):Ye.error("Invalid value for focusInside")})),bu("handler",o),bu("state",e),bu("sendFocusIn",c)])},processKey:a,toEvents:function(i,u){var n=i.focusInside!==bf.OnFocusMode?H.none():c(i).map(function(t){return Jt(po(),function(n,e){t(n,i,u),e.stop()})}),e=[Jt(co(),function(r,o){a(r,o,t,i,u).fold(function(){var e,t,n;e=r,t=o,n=nf(ai.concat(ui))(t.event),i.focusInside===bf.OnEnterOrSpaceMode&&n&&Ut(e,t)&&c(i).each(function(n){n(e,i,u),t.stop()})},function(n){o.stop()})}),Jt(ao(),function(n,e){a(n,e,r,i,u).each(function(n){e.stop()})})];return Ro(n.toArray().concat(e))}};return o}function Xf(n){return"input"===fn(n)&&"radio"!==Se(n,"type")||"textarea"===fn(n)}function Jf(n,e){return H.some(!0)}function Qf(n,e,t){return t.execute(n,e,n.element)}function $f(i){return function(n,e,t,r){var o=i(n.element);return Ul(o,n,e,t,r)}}function Zf(n,e){return e=Sa(n,e),$f(e)}function nl(n,e){return n=Sa(e,n),$f(n)}function el(n,e,t){var r,t=He(n,t),t=S(t,Ns);return C(r=t,function(n){return sn(n,e)}).map(function(n){return{index:n,candidates:r}})}function tl(n,e){return C(n,function(n){return sn(e,n)})}function rl(e,n,t,r){return r(Math.floor(n/t),n%t).bind(function(n){n=n.row*t+n.column;return 0<=n&&n<e.length?H.some(e[n]):H.none()})}function ol(r,n,o,i,u){return rl(r,n,i,function(n,e){var t=n===o-1?r.length-n*i:i,t=qc(e,u,0,t-1);return H.some({row:n,column:t})})}function il(r,n,o,i,u){return rl(r,n,i,function(n,e){var t=qc(n,u,0,o-1),n=t===o-1?r.length-t*i:i,n=Gc(e,0,n-1);return H.some({row:t,column:n})})}function ul(e,t,n){ti(e.element,t.selector).each(function(n){t.focusManager.set(e,n)})}function cl(n,e,t,o){var i=function(n,e,t){var r=qc(e,o,0,t.length-1);return r===n?H.none():(e=t[r],"button"===fn(e)&&"disabled"===Se(e,"disabled")?i(n,r,t):H.from(t[r]))};return el(n,t,e).bind(function(n){var e=n.index,n=n.candidates;return i(e,e,n)})}function al(e,t,r){return n=e,(o=r).focusManager.get(n).bind(function(n){return ri(n,o.selector)}).bind(function(n){return r.execute(e,t,n)});var n,o}function sl(e,t,n){t.getInitial(e).orThunk(function(){return ti(e.element,t.selector)}).each(function(n){t.focusManager.set(e,n)})}function fl(n,e,t){return cl(n,t.selector,e,-1)}function ll(n,e,t){return cl(n,t.selector,e,1)}function dl(o){return function(n,e,t,r){return o(n,e,t,r).bind(function(){return t.executeOnMove?al(n,e,t):H.some(!0)})}}function ml(n,e,t){return t.onEscape(n,e)}function gl(n,e,t){return H.from(n[e]).bind(function(n){return H.from(n[t]).map(function(n){return{rowIndex:e,columnIndex:t,cell:n}})})}function pl(n,e,t,r){var o=n[e].length,o=qc(t,r,0,o-1);return gl(n,e,o)}function hl(n,e,t,r){return t=qc(t,r,0,n.length-1),r=n[t].length,r=Gc(e,0,r-1),gl(n,t,r)}function vl(n,e,t,r){var o=n[e].length,o=Gc(t+r,0,o-1);return gl(n,e,o)}function bl(n,e,t,r){return t=Gc(t+r,0,n.length-1),r=n[t].length,r=Gc(e,0,r-1),gl(n,t,r)}function yl(e,t,n){t.previousSelector(e).orThunk(function(){var n=t.selectors;return ti(e.element,n.cell)}).each(function(n){t.focusManager.set(e,n)})}function _l(e,t,n){ti(e.element,t.selector).each(function(n){t.focusManager.set(e,n)})}function wl(n,e,t){return cl(n,t.selector,e,-1)}function xl(n,e,t){return cl(n,t.selector,e,1)}function Ol(n,e,t){(e.disabled()?td:rd)(n,e)}function Sl(n,e){return!0===e.useNative&&w(ed,fn(n.element))}function El(n){Oe(n.element,"disabled","disabled")}function kl(n){Ce(n.element,"disabled")}function Cl(n){Oe(n.element,"aria-disabled","true")}function Tl(n){Oe(n.element,"aria-disabled","false")}function Ml(n,e){return Sl(n,e)?ke(n.element,"disabled"):"true"===Se(n.element,"aria-disabled")}function Dl(n){return od.config(n)}function Al(){var n={};return n[Tf]={schema:Ot([Rt("readonly",Xr)]),onReceive:function(n,e){od.set(n,e.readonly)}},n}function Fl(n){return[Dl(n),id.config({channels:Al()})]}var Rl,Nl,Bl,Hl,Pl,Il,jl,Ll,Vl,Kr=function(n){function i(n,e){return n=n.visibilitySelector.bind(function(n){return ri(e,n)}).getOr(e),0<Mc(n)}function e(e,t,n){(function(n,e){n=He(n.element,e.selector),n=S(n,function(n){return i(e,n)});return H.from(n[e.firstTabstop])})(e,t).each(function(n){t.focusManager.set(e,n)})}function c(e,n,t,r,o){return o(n,t,function(n){return i(e=r,n=n)&&e.useTabstopAt(n);var e}).fold(function(){return r.cyclic?H.some(!0):H.none()},function(n){return r.focusManager.set(e,n),H.some(!0)})}function o(e,n,t,r){var o,i,u=He(e.element,t.selector);return o=e,(i=t).focusManager.get(o).bind(function(n){return ri(n,i.selector)}).bind(function(n){return C(u,d(sn,n)).bind(function(n){return c(e,u,n,t,r)})})}var t=[Bt("onEscape"),Bt("onEnter"),jt("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),jt("firstTabstop",0),jt("useTabstopAt",v),Bt("visibilitySelector")].concat([n]),r=y([of(ef([tf,nf(ii)]),function(n,e,t){var r=t.cyclic?Js:Qs;return o(n,0,t,r)}),of(nf(ii),function(n,e,t){var r=t.cyclic?$s:Zs;return o(n,0,t,r)}),of(nf(ci),function(e,t,n){return n.onEscape.bind(function(n){return n(e,t)})}),of(ef([Vf,nf(ui)]),function(e,t,n){return n.onEnter.bind(function(n){return n(e,t)})})]),n=y([]);return Kf(t,$r.init,r,n,function(){return H.some(e)})},kr=Kr(Vt("cyclic",s)),rc=Kr(Vt("cyclic",v)),oc=function(n,e,t){return Xf(t)&&nf(ai)(e.event)?H.none():(Xt(n,t,yo()),H.some(!0))},Tr=[jt("execute",oc),jt("useSpace",!1),jt("useEnter",!0),jt("useControlEnter",!1),jt("useDown",!1)],Fu=Kf(Tr,$r.init,function(n,e,t,r){var o=t.useSpace&&!Xf(n.element)?ai:[],i=t.useEnter?ui:[],n=t.useDown?di:[],n=o.concat(i).concat(n);return[of(nf(n),Qf)].concat(t.useControlEnter?[of(ef([rf,nf(ui)]),Qf)]:[])},function(n,e,t,r){return t.useSpace&&!Xf(n.element)?[of(nf(ai),Jf)]:[]},function(){return H.none()}),Qn=function(){var t=fe(H.none());return Zr({readState:function(){return t.get().map(function(n){return{numRows:String(n.numRows),numColumns:String(n.numColumns)}}).getOr({numRows:"?",numColumns:"?"})},setGridSize:function(n,e){t.set(H.some({numRows:n,numColumns:e}))},getNumRows:function(){return t.get().map(function(n){return n.numRows})},getNumColumns:function(){return t.get().map(function(n){return n.numColumns})}})},Wl=Object.freeze({__proto__:null,flatgrid:Qn,init:function(n){return n.state(n)}}),Hn=function(o){return function(n,e,t,r){return Ul(o,n,e,t,r)}},Ul=function(e,t,n,r,o){return r.focusManager.get(t).bind(function(n){return e(t.element,n,r,o)}).map(function(n){return r.focusManager.set(t,n),!0})},zr=Hn,Nr=Hn,zl=Hn,he=[Ft("selector"),jt("execute",oc),W("onEscape"),jt("captureTab",!1),Bn()],Xe=function(o){return function(n,e,t,r){return el(n,e,t.selector).bind(function(n){return o(n.candidates,n.index,r.getNumRows().getOr(t.initSize.numRows),r.getNumColumns().getOr(t.initSize.numColumns))})}},Ju=function(n,e,t){return t.captureTab?H.some(!0):H.none()},ic=Xe(function(n,e,t,r){return ol(n,e,t,r,-1)}),re=Xe(function(n,e,t,r){return ol(n,e,t,r,1)}),pe=Xe(function(n,e,t,r){return il(n,e,t,r,-1)}),Kr=Xe(function(n,e,t,r){return il(n,e,t,r,1)}),Tr=y([of(nf(si),Zf(ic,re)),of(nf(li),nl(ic,re)),of(nf(fi),zr(pe)),of(nf(di),Nr(Kr)),of(ef([tf,nf(ii)]),Ju),of(ef([Vf,nf(ii)]),Ju),of(nf(ci),function(n,e,t){return t.onEscape(n,e)}),of(nf(ai.concat(ui)),function(e,t,r,n){return o=e,(i=r).focusManager.get(o).bind(function(n){return ri(n,i.selector)}).bind(function(n){return r.execute(e,t,n)});var o,i})]),Hn=y([of(nf(ai),Jf)]),Xe=Kf(he,Qn,Tr,Hn,function(){return H.some(ul)}),ic=[Ft("selector"),jt("getInitial",H.none),jt("execute",oc),W("onEscape"),jt("executeOnMove",!1),jt("allowVertical",!0)],re=y([of(nf(ai),Jf)]),pe=Kf(ic,$r.init,function(n,e,t,r){var o=si.concat(t.allowVertical?fi:[]),t=li.concat(t.allowVertical?di:[]);return[of(nf(o),dl(Zf(fl,ll))),of(nf(t),dl(nl(fl,ll))),of(nf(ui),al),of(nf(ai),al),of(nf(ci),ml)]},re,function(){return H.some(sl)}),Kr=[Nt("selectors",[Ft("row"),Ft("cell")]),jt("cycles",!0),jt("previousSelector",H.none),jt("execute",oc)],Ju=function(n,e){return function(t,r,i){var u=i.cycles?n:e;return ri(r,i.selectors.row).bind(function(n){var e=He(n,i.selectors.cell);return tl(e,r).bind(function(r){var o=He(t,i.selectors.row);return tl(o,n).bind(function(n){var e,t=(e=i,x(o,function(n){return He(n,e.selectors.cell)}));return u(t,n,r).map(function(n){return n.cell})})})})}},he=Ju(function(n,e,t){return pl(n,e,t,-1)},function(n,e,t){return vl(n,e,t,-1)}),Qn=Ju(function(n,e,t){return pl(n,e,t,1)},function(n,e,t){return vl(n,e,t,1)}),Tr=Ju(function(n,e,t){return hl(n,t,e,-1)},function(n,e,t){return bl(n,t,e,-1)}),Hn=Ju(function(n,e,t){return hl(n,t,e,1)},function(n,e,t){return bl(n,t,e,1)}),ic=y([of(nf(si),Zf(he,Qn)),of(nf(li),nl(he,Qn)),of(nf(fi),zr(Tr)),of(nf(di),Nr(Hn)),of(nf(ai.concat(ui)),function(e,t,r){return ar(e.element).bind(function(n){return r.execute(e,t,n)})})]),re=y([of(nf(ai),Jf)]),Ju=Kf(Kr,$r.init,ic,re,function(){return H.some(yl)}),he=[Ft("selector"),jt("execute",oc),jt("moveOnTab",!1)],Qn=function(e,t,r){return r.focusManager.get(e).bind(function(n){return r.execute(e,t,n)})},zr=y([of(nf(fi),zl(wl)),of(nf(di),zl(xl)),of(ef([tf,nf(ii)]),function(n,e,t,r){return t.moveOnTab?zl(wl)(n,e,t,r):H.none()}),of(ef([Vf,nf(ii)]),function(n,e,t,r){return t.moveOnTab?zl(xl)(n,e,t,r):H.none()}),of(nf(ui),Qn),of(nf(ai),Qn)]),Tr=y([of(nf(ai),Jf)]),Nr=Kf(he,$r.init,zr,Tr,function(){return H.some(_l)}),Hn=[W("onSpace"),W("onEnter"),W("onShiftEnter"),W("onLeft"),W("onRight"),W("onTab"),W("onShiftTab"),W("onUp"),W("onDown"),W("onEscape"),jt("stopSpaceKeyup",!1),Bt("focusIn")],Kr=Kf(Hn,$r.init,function(n,e,t){return[of(nf(ai),t.onSpace),of(ef([Vf,nf(ui)]),t.onEnter),of(ef([tf,nf(ui)]),t.onShiftEnter),of(ef([tf,nf(ii)]),t.onShiftTab),of(ef([Vf,nf(ii)]),t.onTab),of(nf(fi),t.onUp),of(nf(di),t.onDown),of(nf(si),t.onLeft),of(nf(li),t.onRight),of(nf(ai),t.onSpace),of(nf(ci),t.onEscape)]},function(n,e,t){return t.stopSpaceKeyup?[of(nf(ai),Jf)]:[]},function(n){return n.focusIn}),ql=kr.schema(),Gl=rc.schema(),Yl=pe.schema(),Kl=Xe.schema(),Xl=Ju.schema(),Jl=Fu.schema(),Ql=Nr.schema(),$l=Kr.schema(),Zl=(jl=Dt("Creating behaviour: "+(Rl={branchKey:"mode",branches:Object.freeze({__proto__:null,acyclic:ql,cyclic:Gl,flow:Yl,flatgrid:Kl,matrix:Xl,execution:Jl,menu:Ql,special:$l}),name:"keying",active:{events:function(n,e){return n.handler.toEvents(n,e)}},apis:{focusIn:function(e,t,r){t.sendFocusIn(t).fold(function(){e.getSystem().triggerFocus(e.element,e.element)},function(n){n(e,t,r)})},setGridSize:function(n,e,t,r,o){we(t,"setGridSize")?t.setGridSize(r,o):console.error("Layout does not support setGridSize")}},state:Wl}).name,Wo,Rl),Nl=At(jl.branchKey,jl.branches),Bl=jl.name,Hl=jl.active,Pl=jl.apis,Il=jl.extra,Rl=jl.state,Nl=Pt(Bl,[Ht("config",jl=Nl)]),jo(jl,Nl,Bl,Hl,Pl,Il,Rl)),nd=Ci({name:"Button",factory:function(n){function t(e){return tt(n.dom,"attributes").bind(function(n){return tt(n,e)})}var e=Xs(n.action),r=n.dom.tag;return{uid:n.uid,dom:n.dom,components:n.components,events:e,behaviours:Gu(n.buttonBehaviours,[Af.config({}),Zl.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:function(){if("button"!==r)return{role:t("role").getOr("button")};var n=t("type").getOr("button"),e=t("role").map(function(n){return{role:n}}).getOr({});return Z({type:n},e)}()},eventOrder:n.eventOrder}},configFields:[jt("uid",void 0),Ft("dom"),jt("components",[]),qu("buttonBehaviours",[Af,Zl]),Bt("action"),Bt("role"),jt("eventOrder",{})]}),ed=["input","button","textarea","select"],td=function(e,n,t){n.disableClass.each(function(n){Fe(e.element,n)}),(Sl(e,n)?El:Cl)(e),n.onDisabled(e)},rd=function(e,n,t){n.disableClass.each(function(n){Ne(e.element,n)}),(Sl(e,n)?kl:Tl)(e),n.onEnabled(e)},ic=Object.freeze({__proto__:null,enable:rd,disable:td,isDisabled:Ml,onLoad:Ol,set:function(n,e,t,r){(r?td:rd)(n,e)}}),re=Object.freeze({__proto__:null,exhibit:function(n,e){return nr({classes:e.disabled()?e.disableClass.toArray():[]})},events:function(t,n){return Ro([{key:yo(),value:zt({abort:function(n,e){return Ml(n,t)}})},er(t,n,Ol)])}}),oc=[Lt("disabled",s,Jr),jt("useNative",!0),Bt("disableClass"),n("onDisabled"),n("onEnabled")],od=or({fields:oc,name:"disabling",active:re,apis:ic}),Qn=Object.freeze({__proto__:null,events:function(u){return Ro([Jt(bo(),function(r,n){var e,o=u.channels,t=Qe(o),i=n,t=(t=t,(e=i).universal?t:S(t,function(n){return w(e.channels,n)}));O(t,function(n){var e=o[n],t=e.schema,t=Dt("channel["+n+"] data\nReceiver: "+gr(r.element),t,i.data);e.onReceive(r,t)})})])}}),he=[Rt("channels",qr(Ye.value,Ot([U("onReceive"),jt("schema",Yr())])))],id=or({fields:he,name:"receiving",active:Qn}),zr="tox-button--naked",ud="tox-comment",Tr="tox-comment__body",Hn="tox-textarea",kr="tox-comment__reply",rc="tox-comment__overlay",pe="tox-comment__overlaytext",Xe="tox-comment__buttonspacing",Ju="tox-button",Fu="tox-comment__edit",Nr="tox-comment__scroll",Kr="tox-collection__item",oc="tox-conversations",re="tox-comment__expander",ic=re+" p:contains("+Pf.tc_comment_buttons_showmore+")",he=re+" p:contains("+Pf.tc_comment_buttons_showless+")",Qn=function(e){return function(n){return["."+ud+":contains("+n+")","."+e].join(" ")}},cd={selectors:{kebab:"."+zr,cardBody:"."+ud+" ."+Tr,card:"."+ud,showMore:"."+ic,showLess:"."+he,editItem:[".tox-menu",'[role="menuitem"]:contains('+Pf.tc_kebab_edit+")"].join(" "),deleteItem:[".tox-menu",'[role="menuitem"]:contains('+Pf.tc_kebab_delete+")"].join(" "),outerScroll:"."+Nr,outerContainer:"."+oc,cardFor:function(n){return"."+ud+':contains("'+n+'")'},cardN:function(n){return"."+ud+":nth-child("+(n+1)+")"},kebabFor:Qn(zr),showMoreFor:Qn(ic),showLessFor:Qn(he),saveButton:".tox-button:contains("+Pf.tc_edit_buttons_save+")",cancelButton:".tox-button:contains("+Pf.tc_edit_buttons_cancel+")",clearButton:".tox-button:contains("+Pf.tc_reply_buttons_clear+")",deleteButton:".tox-button:contains("+Pf.tc_delete_buttons_proceed+")",commentText:["."+oc,"."+Nr,"."+ud,"textarea"].join(" "),editActions:"."+Fu,cardDeleteButtons:"."+Xe,cardDeleteOverlay:"."+rc,cardDeleteOverlayText:"."+pe,replyTextarea:"."+kr+" textarea",editTextarea:["."+ud,"textarea"].join(" "),kebabMenu:".tox-menu",textarea:"."+Hn},states:{kebabOpen:zr+"--open",bodyExpanded:"tox-comment__body--expanded",bodyNotAnimating:"tox-comment__body--pending",disappearing:"tox-comment--disappearing",deletingConversation:"tox-comment-thread__overlay",bodyHasGradient:"tox-comment__gradient",editingTextarea:Hn},classes:{showMoreLessContainer:[re],outerContainer:[oc],date:["tox-comment__date"],replySection:[kr],replyTextarea:[Hn],textarea:[],editTextarea:[],kebab:[zr,Ju,"tox-button--icon"],kebabIcon:["tox-icon"],card:[ud],cardHead:["tox-comment__header"],cardHeadMeta:["tox-comment__meta"],cardBody:[Tr],cardDelete:[rc],cardDeleteLabel:[pe],cardDeleteButtons:[Xe],button:[Ju],secondaryButton:[Ju,"tox-button--secondary"],editActions:[Fu],outerScroll:[Nr],commentList:["tox-comment-thread"],avatar:["tox-user__avatar"],username:["tox-user__name"],user:["tox-user"],showMoreLess:[],kebabItem:[Kr],kebabItemLabel:["tox-collection__item-label"],kebabDisabledItem:["tox-collection__item--state-disabled"],kebabMenu:["tox-menu","tox-collection","tox-collection--list"],kebabMenuGroup:["tox-collection__group"],roots:{silver:[oc],modern:[oc,"tox"]}},markers:{kebab:{backgroundMenu:"tox-menu--background",selectedMenu:"tox-menu--active",menu:"tox-menu",item:Kr,selectedItem:"tox-collection__item--active"}}},oc=function(t){return function(n){var e=n.eventName.map(function(e){return{action:function(n){Gt(n,e)}}}).getOr({});return nd.sketch(Z(Z({dom:{tag:"button",classes:t,styles:void 0!==n.styles?n.styles:{},innerHtml:n.text}},e),{buttonBehaviours:Lo(en([Rf.config({})],Fl({})))}))}},ad={withClasses:oc,primary:oc(cd.classes.button),secondary:oc(cd.classes.secondaryButton)},sd="tc-save-edit",fd="tc-cancel-edit",ld=wi("tc-patch-comment-state-event"),dd=wi("tc-purge-comment-state-event"),md=wi("tc-force-patch-state-event");(Kr=Ll=Ll||{}).None="Prop_None",Kr.Collapsed="Prop_Collapsed",Kr.Expanded="Prop_Expanded",Kr.CollapsedOrNone="Prop_CollapsedOrNone",Kr.ExpandOrNone="Prop_ExpandOrNone",Kr.Edit="Prop_Edit",(oc=Vl=Vl||{})[oc.BeforeDelete=0]="BeforeDelete",oc[oc.QueryingDelete=1]="QueryingDelete",oc[oc.PostDelete=2]="PostDelete";function gd(n){return x(n.conversation,function(n){return n.props})}function pd(n,e){return Z(Z({},n),{targetUid:e})}function hd(n){if(0<n.queue.length){n=(n=x((e=n).conversation,function(t){return k(e.queue,function(n){return n.uid===t.uid}).fold(function(){return t},function(n){var e=void 0!==n.patch.content?{mode:Ll.ExpandOrNone}:{};return{uid:t.uid,props:Z(Z(Z({},t.props),n.patch),e)}})}),{targetUid:e.targetUid,conversation:n,queue:[]});return H.some({world:n,props:gd(n)})}return H.none();var e}function vd(){return{targetUid:H.none(),conversation:[],queue:[]}}function bd(t,r){return function(n){var e=r(n);Yt(n,ld,{uid:t,patch:e})}}function yd(t,r){return function(n){var e=r(n);Yt(n,ld,{uid:t,patch:e}),Gt(n,md)}}function _d(n){return Gt(n,md),0}function wd(n,e){nu(n.element,"visibility","hidden"),Lf.set(n,!1);var t=n.element.dom.clientHeight;return Lf.set(n,!0),iu(n.element,"visibility"),n.element.dom.scrollHeight>t+1?e:Ll.None}function xd(n){Lf.off(n),Fe(n.element,cd.states.bodyHasGradient)}function Od(n){Lf.on(n),Ne(n.element,cd.states.bodyHasGradient)}function Sd(n){return vn(n.element)}function Ed(n){Ne(n.element,cd.states.bodyHasGradient),Lf.set(n,!1),Ce(n.element,"aria-expanded")}function kd(r){function n(n){return function(e){O(n,function(n){return n(e)})}}function e(t){Sd(t).each(function(n){var e=ou(n,"width");nu(n,"width",Ac(n)+"px"),iu(n,"height"),Ff.setValue(t,r.props.content),t.getSystem().triggerEvent(so(),n,{}),tu(n,{width:e})})}function t(n){return e=n,n=r.memCommands,Id(e),void n.getOpt(e).each(function(n){Cf.remove(e,n)});var e}var o=((o={})[Ll.None]=n([t,e,Ed]),o[Ll.Expanded]=n([t,e,Od]),o[Ll.Collapsed]=n([t,e,xd]),o[Ll.ExpandOrNone]=n([t,e,function(n){Fe(n.element,cd.states.bodyNotAnimating);var e=wd(n,Ll.Expanded);bd(r.props.uid,function(){return{mode:e}})(n)}]),o[Ll.CollapsedOrNone]=n([t,e,function(n){Fe(n.element,cd.states.bodyNotAnimating);var e=wd(n,Ll.Collapsed);e===Ll.Collapsed&&(Fe(n.element,cd.states.bodyHasGradient),Lf.off(n)),n.element.dom.offsetWidth,bd(r.props.uid,function(){return{mode:e}})(n)}]),o[Ll.Edit]=n([function(n){return e=n,n=r.memCommands,Lf.on(e),Ne(e.element,cd.states.bodyHasGradient),void Hd(e,n);var e},e]),o);return tt(o,r.props.mode)}function Cd(n){return Z(Z({},(e=n,Lo([Af.config({onFocus:e.selectOnFocus?function(n){var e=n.element,n=uu(e);e.dom.setSelectionRange(0,n.length)}:h})]))),pu(n.inputBehaviours,[Ff.config({store:Z(Z({mode:"manual"},n.data.map(function(n){return{initialValue:n}}).getOr({})),{getValue:function(n){return uu(n.element)},setValue:function(n,e){uu(n.element)!==e&&cu(n.element,e)}}),onSetValue:n.onSetValue})]));var e}function Td(n){return Zl.config({mode:"cyclic",onEscape:n.onEscape,useTabstopAt:function(n){return"textarea"!==fn(n)||"true"!==Se(n,"readonly")}})}function Md(e){return Zl.config({mode:"special",onEnter:function(n){return Gt(n,e.enterEvent),H.some(!0)},onShiftEnter:function(n,e){return e.cut(),H.none()},onSpace:function(n,e){return e.cut(),H.none()}})}function Dd(n){var e=(t=n.element.dom).clientHeight,t=t.scrollHeight;e<t&&(e=Mc(n.element)-e,nu(n.element,"height",t+e+"px"))}function Ad(n){var e,r=(e=fe(H.none()),{clear:function(){return e.set(H.none())},set:function(n){return e.set(H.some(n))},isSet:function(){return e.get().isSome()},on:function(n){return e.get().each(n)}});return Gd.sketch({tag:"textarea",inputAttributes:Z(Z({},Kd),{readonly:"true"}),inputClasses:cd.classes.editTextarea,inputStyles:Z(Z({},Xd),{"overflow-y":"hidden",border:"none !important"}),data:n,inputBehaviours:Lo([ir("textarea-events",[No(function(n){r.set(Ac(n.element)),Dd(n)}),Jt(so(),Dd)]),id.config({channels:Z(Z({},Al()),((n={})[Mf]={onReceive:function(e){var t=Ac(e.element);r.on(function(n){n!==t&&iu(e.element,"height")}),r.set(t),Dd(e)}},n))}),Md({enterEvent:zd}),Rf.config({}),Dl({})])})}function Fd(n,e){function t(n){Gt(n,jd),yd(r.get().uid,function(n){return{mode:Ll.ExpandOrNone}})(n)}var r=fe(n),o={updateWith:function(e,n,t){r.set(n),kd({props:n,memCommands:i}).each(function(n){Ne(e.element,cd.states.bodyNotAnimating),n(e)})}},i=vc((r.get(),e=e,{dom:{tag:"div",classes:cd.classes.editActions},components:[ad.withClasses(cd.classes.secondaryButton)({text:e.translate(Pf.tc_edit_buttons_cancel),eventName:H.some(fd)}),ad.withClasses(cd.classes.button)({text:e.translate(Pf.tc_edit_buttons_save),eventName:H.some(sd)})]}));return{dom:{tag:"div",classes:cd.classes.cardBody},components:[Ad(r.get().content)],behaviours:Lo([ir("overflow-events",[No(function(n){o.updateWith(n,r.get(),{})}),Jt(sd,function(n){Gt(n,zd)}),Jt(fd,t),(e=lo(),Qt(e)(function(e,n){"max-height"===n.event.raw.propertyName&&vn(e.element).each(function(n){e.getSystem().triggerEvent(so(),n,{})})}))]),Td({onEscape:function(n,e){return t(n),H.some(!0)}}),Lf.config({toggleClass:cd.states.bodyExpanded,selected:!1,toggleOnExecute:!1,aria:{mode:"expanded"}}),Ff.config({store:{mode:"manual",getValue:function(n){return vn(n.element).map(uu).getOr("")},setValue:function(n,e){vn(n.element).each(function(n){cu(n,e)})}}}),Cf.config({})]),apis:o}}function Rd(){return(new Date).toISOString()}var Nd,Bd=function(n,e){return Z(Z({},n),{queue:n.queue.concat(e)})},Hd=function(n,e){Pd(n),e.getOpt(n).fold(function(){Cf.append(n,e.asSpec())},function(){})},Pd=function(n){Sd(n).each(function(n){Ce(n,"readonly"),Fe(n,cd.states.editingTextarea),ur(n);var e=n.dom,n=uu(n).length;e.setSelectionRange(n,n)}),n.element.dom.scrollTop=0},Id=function(n){Sd(n).each(function(n){Oe(n,"readonly","true"),Ne(n,cd.states.editingTextarea)})},jd=wi("tc-focus-outer-comment"),Ld=wi("tc-start-delete-comment"),Vd=wi("tc-start-delete-conversation"),Wd=wi("tc-commit-delete"),Ud=wi("tc-start-edit-comment"),zd=wi("tc-commit-edit-comment"),qd=(wi("tc-cancel-edit-comment"),wi("tc-long-content"),wi("tc-short-content"),wi("tc-toggle-comment")),Kr=y([Bt("data"),jt("inputAttributes",{}),jt("inputStyles",{}),jt("tag","input"),jt("inputClasses",[]),n("onSetValue"),jt("styles",{}),jt("eventOrder",{}),zu("inputBehaviours",[Ff,Af]),jt("selectOnFocus",!0)]),Gd=Ci({name:"Input",configFields:Kr(),factory:function(n,e){return{uid:n.uid,dom:{tag:(t=n).tag,attributes:Z({type:"text"},t.inputAttributes),styles:t.inputStyles,classes:t.inputClasses},components:[],behaviours:Cd(n),eventOrder:n.eventOrder};var t}}),Yd="data-alloy-widget-tabstop",Kd={"data-gramm_editor":"false",rows:"2"},Xd={"white-space":"pre-wrap"},Jd=6048e5,Qd=4*Jd;(oc=Nd=Nd||{})[oc.LessThanMinute=0]="LessThanMinute",oc[oc.Minute=1]="Minute",oc[oc.Hour=2]="Hour",oc[oc.Day=3]="Day",oc[oc.Week=4]="Week",oc[oc.Month=5]="Month",oc[oc.Year=6]="Year";function $d(n,o){function r(n){return[function(n){n=I.fromText(n);return Lu({element:n})}(""+(e=dm(n.createdAt),t=o,r=mm[e.interval],r=1===e.intervalUnits?r[0]:r[1],t.translate([r,e.intervalUnits]))),{dom:{tag:"span",classes:[],innerHtml:n.modifiedAt===n.createdAt?"":o.translate(Pf.tc_date_comment_edited)}}];var e,t,r}var e={updateWith:function(n,e,t){Cf.set(n,r(e))}};return{dom:{tag:"div",classes:cd.classes.date},components:r(n),behaviours:Lo([Cf.config({})]),apis:e}}function Zd(n){var e=I.fromHtml(n),t=pn(e),r=function(n){n=void 0!==n.dom.attributes?n.dom.attributes:[];return E(n,function(n,e){return"class"===e.name?n:Z(Z({},n),((n={})[e.name]=e.value,n))},{})}(e),n=(n=e,Array.prototype.slice.call(n.dom.classList,0)),t=0===t.length?{}:{innerHtml:lr(e)};return Z({tag:fn(e),classes:n,attributes:r},t)}function nm(e,t){function r(n){Gt(n,jd),yd(e(),function(n){return{isDeleting:Vl.BeforeDelete}})(n)}function n(n,e,t){return nd.sketch({dom:{tag:"button",classes:n?cd.classes.button:cd.classes.secondaryButton,innerHtml:e},eventOrder:((e={})[io()]=["alloy.base.behaviour","focusing","overlay-button-events"],e),action:t,buttonBehaviours:Lo(pm)})}var o={setText:function(n,e){Cf.set(n,x(e,function(n){n="string"==typeof n?t.translate(n):t.translate([n.pattern].concat(n.targets));return{dom:{tag:"div"},components:[{dom:Zd("<p>"+n+"</p>")}]}}).concat([i.asSpec()]))}},i=vc({dom:{tag:"div",classes:cd.classes.cardDeleteButtons},components:[n(!0,t.translate(Pf.tc_delete_buttons_cancel),r),n(!1,t.translate(Pf.tc_delete_buttons_proceed),function(n){Gt(n,Wd)})]});return{dom:{tag:"div",styles:{display:"none"},classes:cd.classes.cardDeleteLabel},components:[],apis:o,behaviours:Lo([Cf.config({}),ir("card-delete-events",[Jt(ho(),function(n){ar(n.element).fold(function(){yd(e(),function(n){return{isDeleting:Vl.BeforeDelete}})(n)},function(){})})]),Zl.config({mode:"cyclic",onEscape:function(n){return r(n),H.some(!0)}})])}}function em(n,e,t){t?(iu(e.element,"display"),e.hasConfigured(Zl)&&Zl.focusIn(e)):nu(e.element,"display","none")}function tm(e,n){return ni(n,function(n){if(!$n(n))return!1;n=Se(n,"id");return void 0!==n&&-1<n.indexOf("aria-owns")}).bind(function(n){var e=Se(n,"id"),n=ie(n);return ti(n,'[aria-owns="'+e+'"]')}).exists(function(n){return hm(e,n)})}function rm(e,n,t,r){return t.get().each(function(n){pc(e)}),n=n.getAttachPoint(e),dc(n,e),r=e.getSystem().build(r),dc(e,r),t.set(r),r}function om(n,e,t,r){return r=rm(n,e,t,r),e.onOpen(n,r),r}function im(e,t,r){r.get().each(function(n){pc(e),gc(e),t.onClose(e,n),r.clear()})}function um(n,e,t){return t.isOpen()}function cm(n){(ar(n.element).isNone()||Af.isFocused(n))&&(Af.isFocused(n)||Af.focus(n),Yt(n,wm,{item:n}))}function am(n){Yt(n,xm,{item:n})}function sm(){return"item-widget"}function fm(n,e){var t={};Ze(n,function(n,e){O(n,function(n){t[n]=e})});var r=e,o=nt(e,function(n,e){return{k:n,v:e}}),i=ye(o,function(n,e){return[e].concat(Mm(t,r,o,e))});return ye(t,function(n){return tt(i,n).getOr([n])})}var lm,dm=d(function(n,e){e=n()-Date.parse(e);return e<6e4?{interval:Nd.LessThanMinute,intervalUnits:e,intervalMs:e}:e<36e5?{interval:Nd.Minute,intervalUnits:Math.floor(e/6e4),intervalMs:e}:e<864e5?{interval:Nd.Hour,intervalUnits:Math.floor(e/36e5),intervalMs:e}:e<Jd?{interval:Nd.Day,intervalUnits:Math.floor(e/864e5),intervalMs:e}:e<Qd?{interval:Nd.Week,intervalUnits:Math.floor(e/Jd),intervalMs:e}:e<290304e5?{interval:Nd.Month,intervalUnits:Math.floor(e/Qd),intervalMs:e}:{interval:Nd.Year,intervalUnits:Math.floor(e/290304e5),intervalMs:e}},function(){return Date.now()}),Kr=Nd,mm=((oc={})[Kr.LessThanMinute]=[Pf.tc_date_less_than_a_minute_ago,Pf.tc_date_less_than_a_minute_ago],oc[Kr.Minute]=[Pf.tc_date_1_minute_ago,Pf.tc_date_x_minutes_ago],oc[Kr.Hour]=[Pf.tc_date_1_hour_ago,Pf.tc_date_x_hours_ago],oc[Kr.Day]=[Pf.tc_date_1_day_ago,Pf.tc_date_x_days_ago],oc[Kr.Week]=[Pf.tc_date_1_week_ago,Pf.tc_date_x_weeks_ago],oc[Kr.Month]=[Pf.tc_date_1_month_ago,Pf.tc_date_x_months_ago],oc[Kr.Year]=[Pf.tc_date_1_year_ago,Pf.tc_date_x_years_ago],oc),gm={editCommentEvent:wi("tc-conversation-edit-comment"),deleteCommentEvent:wi("tc-conversation-delete-comment"),deleteConversationEvent:wi("tc-conversation-delete-all"),createCommentEvent:wi("tc-conversation-create")},pm=[Rf.config({}),ir("overlay-button-events",[Jt(io(),function(n,e){e.event.prevent()})])],hm=function(e,n){return Ea(n,function(n){return sn(n,e.element)},s)||tm(e,n)},Kr=Object.freeze({__proto__:null,getCoupled:function(n,e,t,r){return t.getOrCreate(n,e,r)}}),oc=[Rt("others",qr(Ye.value,Yr()))],vm=or({fields:oc,name:"coupling",apis:Kr,state:Object.freeze({__proto__:null,init:function(){var o={},n=y({});return Zr({readState:n,getOrCreate:function(e,t,r){var n=Qe(t.others);if(n)return tt(o,r).getOrThunk(function(){var n=tt(t.others,r).getOrDie("No information found for coupled component: "+r)(e),n=e.getSystem().build(n);return o[r]=n});throw new Error("Cannot find coupled component: "+r+". Known coupled components: "+JSON.stringify(n,null,2))}})}})}),bm=function(n,e,t){var r,o,i=e.getAttachPoint(n);nu(n.element,"position",Ef.getMode(i)),r=n,n="visibility",o=e.cloakVisibilityAttr,e="hidden",ou(r.element,n).fold(function(){Ce(r.element,o)},function(n){Oe(r.element,o,n)}),nu(r.element,n,e)},ym=function(n,e,t){var r,o,i;r=n.element,p(["top","left","right","bottom"],function(n){return ou(r,n).isSome()})||iu(n.element,"position"),o=n,i="visibility",e=e.cloakVisibilityAttr,Ee(o.element,e).fold(function(){return iu(o.element,i)},function(n){return nu(o.element,i,n)})},qr=Object.freeze({__proto__:null,cloak:bm,decloak:ym,open:om,openWhileCloaked:function(n,e,t,r,o){bm(n,e),om(n,e,t,r),o(),ym(n,e)},close:im,isOpen:um,isPartOf:function(e,t,n,r){return um(0,0,n)&&n.get().exists(function(n){return t.isPartOf(e,n,r)})},getState:function(n,e,t){return t.get()},setContent:function(n,e,t,r){return t.get().map(function(){return rm(n,e,t,r)})}}),oc=Object.freeze({__proto__:null,events:function(t,r){return Ro([Jt(Oo(),function(n,e){im(n,t,r)})])}}),Kr=[n("onOpen"),n("onClose"),Ft("isPartOf"),Ft("getAttachPoint"),jt("cloakVisibilityAttr","data-precloak-visibility")],_m=or({fields:Kr,name:"sandboxing",active:oc,apis:qr,state:Object.freeze({__proto__:null,init:function(){var e=fe(H.none()),n=y("not-implemented");return Zr({readState:n,isOpen:function(){return e.get().isSome()},clear:function(){e.set(H.none())},set:function(n){e.set(H.some(n))},get:function(){return e.get()}})}})}),wm="alloy.item-hover",xm="alloy.item-focus",Om=y(wm),Sm=y(xm),oc=[Ft("data"),Ft("components"),Ft("dom"),jt("hasSubmenu",!1),Bt("toggling"),qu("itemBehaviours",[Lf,Af,Zl,Ff]),jt("ignoreFocus",!1),jt("domModification",{}),bu("builder",function(n){return{dom:n.dom,domModification:Z(Z({},n.domModification),{attributes:Z(Z(Z({role:n.toggling.isSome()?"menuitemcheckbox":"menuitem"},n.domModification.attributes),{"aria-haspopup":n.hasSubmenu}),n.hasSubmenu?{"aria-expanded":!1}:{})}),behaviours:Gu(n.itemBehaviours,[n.toggling.fold(Lf.revoke,function(n){return Lf.config(Z({aria:{mode:"checked"}},n))}),Af.config({ignore:n.ignoreFocus,stopMousedown:n.ignoreFocus,onFocus:function(n){am(n)}}),Zl.config({mode:"execution"}),Ff.config({store:{mode:"memory",initialValue:n.data}}),ir("item-type-events",en(Ks(),[Jt(uo(),cm),Jt(_o(),Af.focus)]))]),components:n.components,eventOrder:n.eventOrder}}),jt("eventOrder",{})],qr=[Ft("dom"),Ft("components"),bu("builder",function(n){return{dom:n.dom,components:n.components,events:Ro([(n=_o(),Jt(n,function(n,e){e.stop()}))])}})],Em=y([eo({name:"widget",overrides:function(e){return{behaviours:Lo([Ff.config({store:{mode:"manual",getValue:function(n){return e.data},setValue:function(){}}})])}}})]),eo=[Ft("uid"),Ft("data"),Ft("components"),Ft("dom"),jt("autofocus",!1),jt("ignoreFocus",!1),qu("widgetBehaviours",[Ff,Af,Zl]),jt("domModification",{}),Mu(Em()),bu("builder",function(t){function r(n){return function(n,e,t){t=e.partUids[t];return n.getSystem().getByUid(t).toOptional()}(n,t,"widget").map(function(n){return Zl.focusIn(n),n})}var n=ku(sm(),t,Em()),e=Cu(sm(),t,n.internals()),n=function(n,e){return Xf(e.event.target)||t.autofocus&&e.setSource(n.element),H.none()};return{dom:t.dom,components:e,domModification:t.domModification,events:Ro([Po(function(n,e){r(n).each(function(n){e.stop()})}),Jt(uo(),cm),Jt(_o(),function(n,e){t.autofocus?r(n):Af.focus(n)})]),behaviours:Gu(t.widgetBehaviours,[Ff.config({store:{mode:"memory",initialValue:t.data}}),Af.config({ignore:t.ignoreFocus,onFocus:function(n){am(n)}}),Zl.config({mode:"special",focusIn:t.autofocus?function(n){r(n)}:Uo(),onLeft:n,onRight:n,onEscape:function(n,e){return Af.isFocused(n)||t.autofocus?(t.autofocus&&e.setSource(n.element),H.none()):(Af.focus(n),H.some(!0))}})])}})],km=At("type",{widget:eo,item:oc,separator:qr}),uc=y([uc({factory:{sketch:function(n){n=Dt("menu.spec item",km,n);return n.builder(n)}},name:"items",unit:"item",defaults:function(n,e){return e.hasOwnProperty("uid")?e:Z(Z({},e),{uid:Ri("item")})},overrides:function(n,e){return{type:e.type,ignoreFocus:n.fakeFocus,domModification:{classes:[n.markers.item]}}}})]),Bn=y([Ft("value"),Ft("items"),Ft("dom"),Ft("components"),jt("eventOrder",{}),zu("menuBehaviours",[Yf,Ff,Df,Zl]),Lt("movement",{mode:"menu",moveOnTab:!0},At("mode",{grid:[Bn(),bu("config",function(n,e){return{mode:"flatgrid",selector:"."+n.markers.item,initSize:{numColumns:e.initSize.numColumns,numRows:e.initSize.numRows},focusManager:n.focusManager}})],matrix:[bu("config",function(n,e){return{mode:"matrix",selectors:{row:e.rowSelector,cell:"."+n.markers.item},focusManager:n.focusManager}}),Ft("rowSelector")],menu:[jt("moveOnTab",!0),bu("config",function(n,e){return{mode:"menu",selector:"."+n.markers.item,moveOnTab:e.moveOnTab,focusManager:n.focusManager}})]})),Rt("markers",Xu()),jt("fakeFocus",!1),jt("focusManager",lf()),n("onHighlight")]),Cm=y("alloy.menu-focus"),Tm=Ti({name:"Menu",configFields:Bn(),partFields:uc(),factory:function(n,e,t,r){return{uid:n.uid,dom:n.dom,markers:n.markers,behaviours:pu(n.menuBehaviours,[Yf.config({highlightClass:n.markers.selectedItem,itemClass:n.markers.item,onHighlight:n.onHighlight}),Ff.config({store:{mode:"memory",initialValue:n.value}}),Df.config({find:H.some}),Zl.config(n.movement.config(n,n.movement))]),events:Ro([Jt(Sm(),function(e,t){var n=t.event;e.getSystem().getByDom(n.target).each(function(n){Yf.highlight(e,n),t.stop(),Yt(e,Cm(),{menu:e,item:n})})}),Jt(Om(),function(n,e){e=e.event.item;Yf.highlight(n,e)})]),components:e,eventOrder:n.eventOrder,domModification:{attributes:{role:"menu"}}}}}),Mm=function(t,r,o,n){return tt(o,n).bind(function(n){return tt(t,n).bind(function(n){var e=Mm(t,r,o,n);return H.some([n].concat(e))})}).getOr([])},Dm=function(n){return"prepared"===n.type?H.some(n.menu):H.none()},Am={init:function(){function c(t){return function(n,e){for(var t=Qe(n),r=0,o=t.length;r<o;r++){var i=t[r],u=n[i];if(e(u,i,n))return H.some(u)}return H.none()}(o.get(),function(n,e){return n===t})}var o=fe({}),i=fe({}),a=fe({}),s=fe(H.none()),u=fe({}),f=function(n){return e(n).bind(Dm)},e=function(n){return tt(i.get(),n)},t=function(n){return tt(o.get(),n)};return{setMenuBuilt:function(n,e){var t;i.set(Z(Z({},i.get()),((t={})[n]={type:"prepared",menu:e},t)))},setContents:function(n,e,t,r){s.set(H.some(n)),o.set(t),i.set(e),u.set(r);t=fm(r,t);a.set(t)},expand:function(t){return tt(o.get(),t).map(function(n){var e=tt(a.get(),t).getOr([]);return[n].concat(e)})},refresh:function(n){return tt(a.get(),n)},collapse:function(n){return tt(a.get(),n).bind(function(n){return 1<n.length?H.some(n.slice(1)):H.none()})},lookupMenu:e,lookupItem:t,otherMenus:function(n){var e=u.get();return F(Qe(e),n)},getPrimary:function(){return s.get().bind(f)},getMenus:function(){return i.get()},clear:function(){o.set({}),i.set({}),a.set({}),s.set(H.none())},isClear:function(){return s.get().isNone()},getTriggeringPath:function(n,u){var e=S(t(n).toArray(),function(n){return f(n).isSome()});return tt(a.get(),n).bind(function(n){var i=A(e.concat(n));return function(n){for(var e=[],t=0;t<n.length;t++){var r=n[t];if(!r.isSome())return H.none();e.push(r.getOrDie())}return H.some(e)}(M(i,function(n,e){return t=n,r=u,o=i.slice(0,e+1),f(t).bind(function(e){return c(t).bind(function(n){return r(n).map(function(n){return{triggeredMenu:e,triggeringItem:n,triggeringPath:o}})})}).fold(function(){return s.get().is(n)?[]:[H.none()]},function(n){return[H.some(n)]});var t,r,o}))})}}},extractPreparedMenu:Dm},Fm=y("collapse-item"),Rm=Ci({name:"TieredMenu",configFields:[Nn("onExecute"),Nn("onEscape"),U("onOpenMenu"),U("onOpenSubmenu"),n("onRepositionMenu"),n("onCollapseMenu"),jt("highlightImmediately",!0),Nt("data",[Ft("primary"),Ft("menus"),Ft("expansions")]),jt("fakeFocus",!1),n("onHighlight"),n("onHover"),hu(),Ft("dom"),jt("navigateOnHover",!0),jt("stayInDom",!1),zu("tmenuBehaviours",[Zl,Yf,Df,Cf]),jt("eventOrder",{})],apis:{collapseMenu:function(n,e){n.collapseMenu(e)},highlightPrimary:function(n,e){n.highlightPrimary(e)},repositionMenus:function(n,e){n.repositionMenus(e)}},factory:function(u,n){function t(n){var r,o,n=(r=n,o=u.data.primary,e=u.data.menus,ye(e,function(n,e){function t(){return Tm.sketch(Z(Z({},n),{value:e,markers:u.markers,fakeFocus:u.fakeFocus,onHighlight:u.onHighlight,focusManager:(u.fakeFocus?df:lf)()}))}return e===o?{type:"prepared",menu:r.getSystem().build(t())}:{type:"notbuilt",nbMenu:t}})),e=g();return m.setContents(u.data.primary,n,u.data.expansions,e),m.getPrimary()}function c(n){return Ff.getValue(n).value}function i(e,n){Yf.highlight(e,n),Yf.getHighlighted(n).orThunk(function(){return Yf.getFirst(n)}).each(function(n){Xt(e,n.element,_o())})}function a(e,n){return uf(x(n,function(n){return e.lookupMenu(n).bind(function(n){return"prepared"===n.type?H.some(n.menu):H.none()})}))}function s(e,n,t){t=a(n,n.otherMenus(t)),O(t,function(n){Ji(n.element,[u.markers.backgroundMenu]),u.stayInDom||Cf.remove(e,n)})}function f(n,t){var e,n=(e=n,r.get().getOrThunk(function(){var t={},n=He(e.element,"."+u.markers.item),n=S(n,function(n){return"true"===Se(n,"aria-haspopup")});return O(n,function(n){e.getSystem().getByDom(n).each(function(n){var e=c(n);t[e]=n})}),r.set(H.some(t)),t}));Ze(n,function(n,e){e=w(t,e);Oe(n.element,"aria-expanded",e)})}function l(t,r,o){return H.from(o[0]).bind(function(n){return r.lookupMenu(n).bind(function(n){if("notbuilt"===n.type)return H.none();var e=n.menu,n=a(r,o.slice(1));return O(n,function(n){Fe(n.element,u.markers.backgroundMenu)}),ot(e.element)||Cf.append(t,Uu(e)),Ji(e.element,[u.markers.backgroundMenu]),i(t,e),s(t,r,o),H.some(e)})})}var d,r=fe(H.none()),m=Am.init(),g=function(n){return ye(u.data.menus,function(n,e){return M(n.items,function(n){return"separator"===n.type?[]:[n.data.value]})})};(b=d=d||{})[b.HighlightSubmenu=0]="HighlightSubmenu",b[b.HighlightParent=1]="HighlightParent";function o(r,o,i){void 0===i&&(i=d.HighlightSubmenu);var n=c(o);return m.expand(n).bind(function(t){return f(r,t),H.from(t[0]).bind(function(e){return m.lookupMenu(e).bind(function(n){n=function(n,e,t){if("notbuilt"!==t.type)return t.menu;t=n.getSystem().build(t.nbMenu());return m.setMenuBuilt(e,t),t}(r,e,n);return ot(n.element)||Cf.append(r,Uu(n)),u.onOpenSubmenu(r,o,n,A(t)),i===d.HighlightSubmenu?(Yf.highlightFirst(n),l(r,m,t)):(Yf.dehighlightAll(n),H.some(o))})})})}function p(e,t){var n=c(t);return m.collapse(n).bind(function(n){return f(e,n),l(e,m,n).map(function(n){return u.onCollapseMenu(e,t,n),n})})}function e(t){return function(e,n){return ri(n.getSource(),"."+u.markers.item).bind(function(n){return e.getSystem().getByDom(n).toOptional().bind(function(n){return t(e,n).map(function(){return!0})})})}}function h(n){return Yf.getHighlighted(n).bind(Yf.getHighlighted)}var v=Ro([Jt(Cm(),function(e,t){var n=t.event.item;m.lookupItem(c(n)).each(function(){var n=t.event.menu;Yf.highlight(e,n);n=c(t.event.item);m.refresh(n).each(function(n){return s(e,m,n)})})}),Po(function(e,n){n=n.event.target;e.getSystem().getByDom(n).each(function(n){0===c(n).indexOf("collapse-item")&&p(e,n),o(e,n,d.HighlightSubmenu).fold(function(){u.onExecute(e,n)},function(){})})}),No(function(e,n){t(e).each(function(n){Cf.append(e,Uu(n)),u.onOpenMenu(e,n),u.highlightImmediately&&i(e,n)})})].concat(u.navigateOnHover?[Jt(Om(),function(n,e){e=e.event.item;!function(e,n){n=c(n);m.refresh(n).bind(function(n){return f(e,n),l(e,m,n)})}(n,e),o(n,e,d.HighlightParent),u.onHover(n,e)})]:[])),b={collapseMenu:function(e){h(e).each(function(n){p(e,n)})},highlightPrimary:function(e){m.getPrimary().each(function(n){i(e,n)})},repositionMenus:function(t){m.getPrimary().bind(function(e){return h(t).bind(function(n){var e=c(n),n=(n=m.getMenus(),et(n,function(n){return n})),t=uf(x(n,Am.extractPreparedMenu));return m.getTriggeringPath(e,function(n){return e=n,N(t,function(n){if(!n.getSystem().isConnected())return H.none();n=Yf.getCandidates(n);return k(n,function(n){return c(n)===e})});var e})}).map(function(n){return{primary:e,triggeringPath:n}})}).fold(function(){var n;n=t,H.from(n.components()[0]).filter(function(n){return"menu"===Se(n.element,"role")}).each(function(n){u.onRepositionMenu(t,n,[])})},function(n){var e=n.primary,n=n.triggeringPath;u.onRepositionMenu(t,e,n)})}};return{uid:u.uid,dom:u.dom,markers:u.markers,behaviours:pu(u.tmenuBehaviours,[Zl.config({mode:"special",onRight:e(function(n,e){return Xf(e.element)?H.none():o(n,e,d.HighlightSubmenu)}),onLeft:e(function(n,e){return Xf(e.element)?H.none():p(n,e)}),onEscape:e(function(n,e){return p(n,e).orThunk(function(){return u.onEscape(n,e).map(function(){return n})})}),focusIn:function(e,n){m.getPrimary().each(function(n){Xt(e,n.element,_o())})}}),Yf.config({highlightClass:u.markers.selectedMenu,itemClass:u.markers.menu}),Df.config({find:function(n){return Yf.getHighlighted(n)}}),Cf.config({})]),eventOrder:u.eventOrder,apis:b,events:v}},extraApis:{tieredData:function(n,e,t){return{primary:n,menus:e,expansions:t}},singleData:function(n,e){return{primary:n,menus:Hr(n,e),expansions:{}}},collapseItem:function(n){return{value:wi(Fm()),meta:{text:n}}}}}),Nm=y("sink"),mo=y(mo({name:Nm(),overrides:y({dom:{tag:"div"},behaviours:Lo([Ef.config({useFixed:v})]),events:Ro([$t(co()),$t(io()),$t(fo())])})})),Bm=Ot([jt("isExtraPart",s),Pt("fireEventInstead",[jt("event",Co())])]),Hm=Ot([Pt("fireEventInstead",[jt("event",To())]),Qr("doReposition")]);(Qr=lm=lm||{})[Qr.HighlightFirst=0]="HighlightFirst",Qr[Qr.HighlightNone=1]="HighlightNone";function Pm(n,e){var t=n.getHotspot(e).getOr(e),r=n.getAnchorOverrides();return n.layouts.fold(function(){return{anchor:"hotspot",hotspot:t,overrides:r}},function(n){return{anchor:"hotspot",hotspot:t,overrides:r,layouts:n}})}function Im(n,e,t,r,o,i,u){var c,a,s,f,l,d,m=Pm(n,t);return(c=m,s=r,f=o,l=u,e=u=e,u=a=t,e=(0,(n=n).fetch)(u).map(e),d=Km(a,n),e.map(function(n){return n.bind(function(n){return H.from(Rm.sketch(Z(Z({},f.menu()),{uid:Ri(""),data:n,highlightImmediately:l===lm.HighlightFirst,onOpenMenu:function(n,e){var t=d().getOrDie();Ef.position(t,c,e),_m.decloak(s)},onOpenSubmenu:function(n,e,t){var r=d().getOrDie();Ef.position(r,{anchor:"submenu",item:e},t),_m.decloak(s)},onRepositionMenu:function(n,e,t){var r=d().getOrDie();Ef.position(r,c,e),O(t,function(n){Ef.position(r,{anchor:"submenu",item:n.triggeringItem},n.triggeredMenu)})},onEscape:function(){return Af.focus(a),_m.close(s),H.some(!0)}})))})})).map(function(n){return n.fold(function(){_m.isOpen(r)&&_m.close(r)},function(n){_m.cloak(r),_m.open(r,n),i(r)}),r})}function jm(n,e,t,r,o,i,u){return _m.close(r),ze(r)}function Lm(n,e,t,r,o,i){var u=vm.getCoupled(t,"sandbox");return(_m.isOpen(u)?jm:Im)(n,e,t,u,r,o,i)}function Vm(n,e,t){e=Df.getCurrent(e).getOr(e),n=Ac(n.element),t?nu(e.element,"min-width",n+"px"):(e=e.element,n=n,Ta.set(e,n))}function Wm(n){_m.getState(n).each(function(n){Rm.repositionMenus(n)})}function Um(r,o,i){var e,n,t,u,c={id:e=wi("aria-owns"),link:function(n){Oe(n,"aria-owns",e)},unlink:function(n){Ce(n,"aria-owns")}},a=Km(o,r);return{dom:{tag:"div",classes:r.sandboxClasses,attributes:{id:c.id,role:"listbox"}},behaviours:Gu(r.sandboxBehaviours,[Ff.config({store:{mode:"memory",initialValue:o}}),_m.config({onOpen:function(n,e){var t=Pm(r,o);c.link(o.element),r.matchWidth&&Vm(t.hotspot,e,r.useMinWidth),r.onOpen(t,n,e),void 0!==i&&void 0!==i.onOpen&&i.onOpen(n,e)},onClose:function(n,e){c.unlink(o.element),void 0!==i&&void 0!==i.onClose&&i.onClose(n,e)},isPartOf:function(n,e,t){return hm(e,t)||hm(o,t)},getAttachPoint:function(){return a().getOrDie()}}),Df.config({find:function(n){return _m.getState(n).bind(function(n){return Df.getCurrent(n)})}}),id.config({channels:Z(Z({},(u=Dt("Dismissal",Bm,t={isExtraPart:s}),(t={})[_()]={schema:Ot([Ft("target")]),onReceive:function(e,n){_m.isOpen(e)&&(_m.isPartOf(e,n.target)||u.isExtraPart(e,n.target)||u.fireEventInstead.fold(function(){return _m.close(e)},function(n){return Gt(e,n.event)}))}},t)),(n=Dt("Reposition",Hm,t={doReposition:Wm}),(t={})[l()]={onReceive:function(e){_m.isOpen(e)&&n.fireEventInstead.fold(function(){return n.doReposition(e)},function(n){return Gt(e,n.event)})}},t))})])}}function zm(n,c,e,t,r){void 0===e&&(e='<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><path d="M12 24C5.373 24 0 18.627 0 12S5.373 0 12 0s12 5.373 12 12-5.373 12-12 12zm-8.693-5.259A10.981 10.981 0 0 0 12 23c3.535 0 6.68-1.668 8.693-4.259C19.432 16.251 14.591 15 12 15c-2.59 0-7.432 1.25-8.693 3.741zm8.7-5.741C14.224 13 16 10.99 16 8.5S14.224 4 12.007 4C9.79 4 8 6.01 8 8.5S9.79 13 12.007 13z" fill-rule="nonzero"></path></svg>');var o,i,u,a,s=fe(n),f=T([t.deleteConversation?[eg]:[],t.editComment?[Zm]:[],t.deleteComment?[ng]:[]]),l=vc($d(n,r.universe)),t={updateWith:function(n,e,t){var r,o,i,u;s.set(e),d.getOpt(n).each(function(n){n.getApis().updateWith(n,e,{})}),m.getOpt(n).each(function(n){n.getApis().updateWith(n,e,{})}),r=n,o=t.numComments,i=e.isDeleting===Vl.QueryingDelete,u=c,g.getOpt(r).each(function(n){return em(0,n,i)}),p.getOpt(r).each(function(n){var e=u?[Pf.tc_delete_prompts_conversation,1===o?Pf.tc_delete_prompts_conversation_detail_sing:{pattern:Pf.tc_delete_prompts_conversation_detail_pl,targets:[o]}]:[Pf.tc_delete_prompts_comment];n.getApis().setText(n,e),em(0,n,i)}),e.isDeleting===Vl.PostDelete&&Hf.getCurrentRoute(n).filter(function(n){return n.destination===$m}).fold(function(){Hf.progressTo(n,$m)},function(n){}),l.getOpt(n).each(function(n){n.getApis().updateWith(n,e,{})})}},f={dom:{tag:"div",classes:cd.classes.cardHead},components:T([[{dom:{tag:"div",classes:cd.classes.cardHeadMeta},components:[function(n,e){e={dom:{tag:"div",classes:cd.classes.avatar,innerHtml:e}},n={dom:{tag:"div",classes:cd.classes.username,innerHtml:n}};return{dom:{tag:"div",classes:cd.classes.user},components:[e,{dom:{tag:"div"},components:[n,l.asSpec()]}]}}(s.get().authorName,e)]}],0<f.length?[(o=r,i=x(f,function(e){var n;return{type:"item",data:{value:e.eventName},dom:{tag:"div",classes:cd.classes.kebabItem},components:[{dom:{tag:"span",classes:cd.classes.kebabItemLabel,innerHtml:o.universe.translate(e.textKey)}}],itemBehaviours:Lo(en([ir("item-events",[Po(function(n){Yt(n,Jm,{eventName:e.eventName}),Gt(n,Oo())})])],Fl({disableClass:cd.classes.kebabDisabledItem.join(" ")}))),eventOrder:((n={})[yo()]=["disabling","alloy.base.behaviour","toggling","typeaheadevents","item-events"],n)}}),Xm.sketch({dom:{tag:"button",attributes:{type:"button"},classes:cd.classes.kebab},components:[{dom:{tag:"div",classes:cd.classes.kebabIcon,innerHtml:'<svg width="24" height="24" data-name="icon-image-options"><g id="icon-image-options" stroke="none" stroke-width="1" fill-rule="evenodd"><path d="M6 10a2 2 0 0 0-2 2c0 1.1.9 2 2 2a2 2 0 0 0 2-2 2 2 0 0 0-2-2zm12 0a2 2 0 0 0-2 2c0 1.1.9 2 2 2a2 2 0 0 0 2-2 2 2 0 0 0-2-2zm-6 0a2 2 0 0 0-2 2c0 1.1.9 2 2 2a2 2 0 0 0 2-2 2 2 0 0 0-2-2z" id="Shape" fill-rule="nonzero"></path></g></svg>'}}],lazySink:function(n){return o.getSink(n).fold(function(){return Ye.error("Could not find sink for kebab")},Ye.value)},dropdownBehaviours:Lo(en(Fl({}),[Rf.config({tabAttr:Yd})])),sandboxBehaviours:Lo([ir("kebab-sandbox-events",[Jt(Jm,function(n,e){n=Ff.getValue(n);Gt(n,e.event.eventName)})])]),parts:{menu:{dom:{tag:"div"},markers:cd.markers.kebab}},toggleClass:cd.states.kebabOpen,layouts:{onLtr:function(){return[la]},onRtl:function(){return[fa]}},fetch:function(n){var e=wi("kebab-id");return ze(H.some(Rm.singleData(e,{value:wi(e),dom:{tag:"div",classes:cd.classes.kebabMenu},components:[{dom:{tag:"div",classes:cd.classes.kebabMenuGroup},components:[Tm.parts.items({})]}],items:i})))}}))]:[]])},d=vc((n={uid:n.uid,mode:n.mode},u=fe(n),n={updateWith:function(n,e,t){switch(u.set(e),e.mode){case Ll.None:nu(n.element,"display","none");break;case Ll.Collapsed:iu(n.element,"display"),vn(n.element).each(function(n){return dr(n,Pf.tc_comment_buttons_showmore)});break;case Ll.Expanded:iu(n.element,"display"),vn(n.element).each(function(n){return dr(n,Pf.tc_comment_buttons_showless)});break;case Ll.ExpandOrNone:case Ll.CollapsedOrNone:break;case Ll.Edit:nu(n.element,"display","none")}}},{dom:{tag:"div",classes:cd.classes.showMoreLessContainer,styles:{display:"none"}},components:[nd.sketch({dom:{tag:"p",classes:cd.classes.showMoreLess,innerHtml:Pf.tc_comment_buttons_showmore},action:function(n){yd(u.get().uid,function(n){return{mode:u.get().mode===Ll.Collapsed?Ll.Expanded:Ll.Collapsed}})(n)},buttonBehaviours:Lo([Rf.config({tabAttr:Yd})])})],apis:n})),m=vc(Fd(s.get(),r.universe)),g=vc((r.universe,{dom:{tag:"div",classes:cd.classes.cardDelete,styles:{display:"none"}}})),p=vc(nm(function(){return s.get().uid},r.universe));return{dom:{tag:"div",classes:cd.classes.card,styles:{position:"relative"}},components:[f,m.asSpec(),g.asSpec(),p.asSpec(),d.asSpec()],behaviours:Lo([Af.config({}),Rf.config({}),(a={escapeEvent:jd},Zl.config({mode:"cyclic",selector:"["+Yd+'="true"]',focusInside:bf.OnEnterOrSpaceMode,onEscape:function(n,e){return Ut(n,e)?H.none():(Gt(n,a.escapeEvent),H.some(!0))}})),Hf.config({initialState:Qm,onTransition:function(n,e){var t;t=s.get().uid,function(n){Yt(n,dd,{uid:t})}(n)},routes:((r={})[Qm]=((f={})[$m]={transition:{property:"opacity",transitionClass:cd.states.disappearing}},f),r)}),Ff.config({store:{mode:"memory",initialValue:s.get().uid}}),ir("comment-events",[Jt(qd,function(n){return yd(s.get().uid,function(){return{mode:s.get().mode===Ll.Collapsed?Ll.Expanded:Ll.Collapsed}})(n)}),Jt(jd,Af.focus),Jt(Ld,function(n,e){yd(s.get().uid,function(n){return{isDeleting:Vl.QueryingDelete}})(n)}),Jt(Vd,function(n,e){yd(s.get().uid,function(n){return{isDeleting:Vl.QueryingDelete}})(n)}),Jt(Ud,function(n){return yd(s.get().uid,function(n){return{mode:Ll.Edit}})(n)}),Jt(zd,function(e){m.getOpt(e).each(function(n){n=Ff.getValue(n);Yt(e,gm.editCommentEvent,{uid:s.get().uid,content:n})})}),Jt(Wd,function(n,e){Yt(n,c?gm.deleteConversationEvent:gm.deleteCommentEvent,{uid:s.get().uid})})])]),apis:t}}function qm(n,r){return x(n,function(n,e){var t=0===e;return zm(n,t,void 0,{editComment:!0,deleteComment:0<e,deleteConversation:t},r)})}function Gm(n,o){var e={setComments:function(n,e){Df.getCurrent(n).each(function(n){Cf.set(n,qm(e,o))}),_d(n)},updateWith:function(e,r,n){Df.getCurrent(e).each(function(n){var t=Cf.contents(n);(R(r).exists(function(n){return n.isDeleting===Vl.QueryingDelete})?Fe:Ne)(n.element,cd.states.deletingConversation),t.length!==r.length?Cf.set(n,qm(r,o)):O(t,function(n,e){t[e].getApis().updateWith(t[e],r[e],{numComments:r.length})}),_d(e)})},scrollToEnd:function(r){Df.getCurrent(r).each(function(n){n=Cf.contents(n);(0===(n=n).length?H.none():H.some(n[n.length-1])).each(function(n){var e=r.element.dom,t=e.getBoundingClientRect(),n=n.element.dom.getBoundingClientRect();n.top<t.top?e.scrollTop=n.top-t.top:n.bottom>t.bottom&&(e.scrollTop=n.bottom-t.bottom)})})}};return{dom:{tag:"div",classes:cd.classes.outerScroll},components:[{dom:{tag:"div",classes:cd.classes.commentList},components:qm(n,o),behaviours:Lo([(n={selector:cd.selectors.card},Zl.config({mode:"flow",allowVertical:!0,selector:n.selector})),Cf.config({})])}],behaviours:Lo([Df.config({find:function(e){return vn(e.element).bind(function(n){return e.getSystem().getByDom(n).toOptional()})}})]),apis:e}}var Ym,Km=function(e,n){return e.getSystem().getByUid(n.uid+"-"+Nm()).map(function(n){return function(){return Ye.value(n)}}).getOrThunk(function(){return n.lazySink.fold(function(){return function(){return Ye.error(new Error("No internal sink is specified, nor could an external sink be found"))}},function(n){return function(){return n(e)}})})},zu=y([Ft("dom"),Ft("fetch"),n("onOpen"),W("onExecute"),jt("getHotspot",H.some),jt("getAnchorOverrides",y({})),Pn(),zu("dropdownBehaviours",[Lf,vm,Zl,Af]),Ft("toggleClass"),jt("eventOrder",{}),Bt("lazySink"),jt("matchWidth",!1),jt("useMinWidth",!1),Bt("role")].concat([jt("sandboxClasses",[]),qu("sandboxBehaviours",[Df,id,_m,Ff])])),mo=y([go({schema:[hu()],name:"menu",defaults:function(n){return{onExecute:n.onExecute}}}),mo()]),Xm=Ti({name:"Dropdown",configFields:zu(),partFields:mo(),factory:function(e,n,t,r){function o(n){_m.getState(n).each(function(n){Rm.highlightPrimary(n)})}var i,u={expand:function(n){Lf.isOn(n)||Lm(e,function(n){return n},n,r,h,lm.HighlightNone).get(h)},open:function(n){Lf.isOn(n)||Lm(e,function(n){return n},n,r,h,lm.HighlightFirst).get(h)},isOpen:Lf.isOn,close:function(n){Lf.isOn(n)&&Lm(e,function(n){return n},n,r,h,lm.HighlightFirst).get(h)},repositionMenus:function(n){Lf.isOn(n)&&function(n){n=vm.getCoupled(n,"sandbox");Wm(n)}(n)}},c=function(n,e){return Kt(n),H.some(!0)};return{uid:e.uid,dom:e.dom,components:n,behaviours:pu(e.dropdownBehaviours,[Lf.config({toggleClass:e.toggleClass,aria:{mode:"expanded"}}),vm.config({others:{sandbox:function(n){return Um(e,n,{onOpen:function(){Lf.on(n)},onClose:function(){Lf.off(n)}})}}}),Zl.config({mode:"special",onSpace:c,onEnter:c,onDown:function(n,e){var t;return Xm.isOpen(n)?(t=vm.getCoupled(n,"sandbox"),o(t)):Xm.open(n),H.some(!0)},onEscape:function(n,e){return Xm.isOpen(n)?(Xm.close(n),H.some(!0)):H.none()}}),Af.config({})]),events:Xs(H.some(function(n){Lm(e,function(n){return n},n,r,o,lm.HighlightFirst).get(h)})),eventOrder:Z(Z({},e.eventOrder),((c={})[yo()]=["disabling","toggling","alloy.base.behaviour"],c)),apis:u,domModification:{attributes:Z(Z({"aria-haspopup":"true"},e.role.fold(function(){return{}},function(n){return{role:n}})),"button"===e.dom.tag?{type:(i="type",tt(e.dom,"attributes").bind(function(n){return tt(n,i)}).getOr("button"))}:{})}}},apis:{open:function(n,e){return n.open(e)},expand:function(n,e){return n.expand(e)},close:function(n,e){return n.close(e)},isOpen:function(n,e){return n.isOpen(e)},repositionMenus:function(n,e){return n.repositionMenus(e)}}}),Jm=wi("tc-kebab-action"),Qm="basecamp",$m="deleting",Zm={textKey:Pf.tc_kebab_edit,eventName:Ud},ng={textKey:Pf.tc_kebab_delete,eventName:Ld},eg={textKey:Pf.tc_kebab_deleteconversation,eventName:Vd};(mo=Ym=Ym||{})[mo.Start=0]="Start",mo[mo.Reply=1]="Reply";function tg(n){return e=fg,n=n,Gd.sketch({tag:"textarea",inputClasses:cd.classes.replyTextarea,inputAttributes:Z(Z({},Kd),{placeholder:n.translate(Pf.tc_reply_placeholders)}),inputStyles:Xd,inputBehaviours:Lo(en([Md({enterEvent:e}),Rf.config({}),ir("reply-events",[Jt(so(),Dd)])],Fl({})))});var e}function rg(t,n){var r=vc(tg(n)),e={focusEditor:function(n){r.getOpt(n).each(Af.focus)},updateWith:function(n,e,t){p(e,function(n){return n.mode===Ll.Edit})?nu(n.element,"display","none"):iu(n.element,"display")}};return{dom:{tag:"div",classes:cd.classes.replySection},behaviours:Lo([ir("reply-events",[Jt(sg,function(n){r.getOpt(n).each(function(n){Ff.setValue(n,"")})}),Jt(fg,function(e){r.getOpt(e).map(Ff.getValue).each(function(n){0<n.length&&Yt(e,gm.createCommentEvent,{content:n,mode:t})})})])]),components:[r.asSpec(),ad.secondary({text:n.translate(Pf.tc_reply_buttons_clear),eventName:H.some(sg)}),ad.primary({text:n.translate(Pf.tc_reply_buttons_save),eventName:H.some(fg)})],apis:e}}function og(n){return{type:"redraw-sidebar",view:n}}function ig(n,e){if(n.conversation.uid!==e.conversation.uid)return og(e);var t,r,o=x(n.conversation.comments,function(n){return n.uid}),i=x(e.conversation.comments,function(n){return n.uid});return t=o,n=i,void 0===r&&(r=V),a(r).eq(t,n)?{type:"refresh-comments",comments:x(e.conversation.comments,function(n,e){return{uid:n.uid,patch:Z({},n)}})}:0!==F(i,o).length?og(e):{type:"remove-comments",adjustments:x(o,function(n){return{type:w(i,n)?"to-remain":"to-delete",uid:n}})}}function ug(n,t,r,e){function o(e){m.get().each(function(n){"comments"===n.type&&e(n.conversation.uid)})}function i(){f.processQueue().each(function(e){b(e),g.getOpt(l).each(function(n){n.getApis().updateWith(n,e,{})})})}function u(e){o(function(n){return e(n).get(function(n){n.fold(function(n){return n()},function(n){return n()})})})}function c(n){n.get(function(n){n.fold(function(n){n()},function(n){f.setTargetUid(n.targetUid),n.fn()})})}function a(n){Cf.set(l,[p.asSpec()].concat(n))}function s(n){"splash"===n.view.type?(f.clearAll(),a([h.asSpec()])):"comments"===n.view.type&&(f.recalibrate(n.view.conversation),a([g.asSpec(),v.asSpec()]),g.getOpt(l).each(function(n){var e=f.getProps();b(e),n.getApis().setComments(n,e),n.getApis().scrollToEnd(n)}))}var f=lg(),l=Wu({dom:{tag:"div",classes:n,styles:{position:"relative"}},behaviours:Lo([Cf.config({}),Td({onEscape:function(n){return Gt(n,dg),H.some(!0)}}),ir("sidebar-events",[Jt(ld,function(n,e){f.enqueuePatch(e.event.uid,e.event.patch)}),Jt(dd,function(n,e){f.purge(e.event.uid),g.getOpt(l).each(function(n){var e=f.getProps();b(e),n.getApis().updateWith(n,e,{})})}),Jt(md,function(n){i()}),Jt(dg,function(n,e){t.onEscape()}),Jt(gm.deleteConversationEvent,function(n,e){u(function(n){return r.deleteConversation(n)})}),Jt(gm.deleteCommentEvent,function(n,e){u(function(n){return r.deleteComment(n,e.event.uid)})}),Jt(gm.editCommentEvent,function(n,e){o(function(n){c(r.editComment(n,e.event.uid,e.event.content))})}),Jt(gm.createCommentEvent,function(n,e){e.event.mode===Ym.Reply?o(function(n){c(r.reply(n,e.event.content))}):c(r.createConversation(e.event.content))})])])}),d=hc(l),m=fe(H.none()),g=vc(Gm([],{getSink:function(n){return p.getOpt(n)},universe:e})),p=vc({dom:{tag:"div",styles:{"z-index":"100"}},behaviours:Lo([Ef.config({})])}),h=vc(rg(Ym.Start,e)),v=vc(rg(Ym.Reply,e)),b=function(e){v.getOpt(l).each(function(n){n.getApis().updateWith(n,e,{})})};return{system:d,root:l,element:d.element,update:function(t){g.getOpt(l).bind(function(e){return m.get().map(function(n){return{convUi:e,pview:n}})}).fold(function(){return s(t)},function(n){n.convUi;var e=n.pview,e=(n=e,e=t.view,n.type!==e.type?og(e):"splash"===e.type?{type:"no-change"}:ig(n,e));switch(e.type){case"add-comment":s(t);break;case"no-change":break;case"redraw-sidebar":s(t);break;case"remove-comments":f.adjust(e.adjustments),i();break;case"refresh-comments":f.enqueuePatches(e.comments),i()}}),m.set(H.some(t.view)),Ns(l.element)&&t.grabFocus()&&v.getOpt(l).orThunk(function(){return h.getOpt(l)}).each(function(n){n.getApis().focusEditor(n)})},hasFocus:function(){return ar(l.element).isSome()},setReadonly:function(n){n&&d.broadcastOn([_()],{target:d.element}),d.broadcastOn([Tf],{readonly:n})},getSink:p.getOpt}}function cg(n,e,t,r){return n.fold(function(n){return Ye.error({message:e,error:n,refresh:r})},function(n){return t(n).fold(function(n){return Ye.error({message:n.permError,error:n.reason,refresh:r})},Ye.value)})}function ag(n){return tt(n,"reason").getOr("")}var sg=wi("tc-clear-input"),fg=wi("tc-submit-input"),lg=function(){var t=fe(vd()),n=function(n){t.set(n(t.get()))};return{clearAll:function(){t.set(vd())},recalibrate:function(e){n(function(n){return t=n,n=e.comments,{targetUid:t.targetUid,conversation:x(n,function(n,e){return{uid:n.uid,props:Z(Z({},n),{isDeleting:Vl.BeforeDelete,mode:t.targetUid.is(n.uid)?Ll.ExpandOrNone:Ll.CollapsedOrNone})}}),queue:[]};var t})},enqueuePatch:function(e,t){n(function(n){return Bd(n,[{uid:e,patch:t}])})},enqueuePatches:function(e){n(function(n){return Bd(n,e)})},setTargetUid:function(e){n(function(n){return pd(n,H.some(e))})},clearTargetUid:function(){n(function(n){return pd(n,H.none())})},processQueue:function(){return hd(t.get()).map(function(n){var e=n.world,n=n.props;return t.set(e),n})},purge:function(t){n(function(n){var e=S(n.conversation,function(n){return n.uid!==t});return Z(Z({},n),{conversation:e})})},getProps:function(){return gd(t.get())},adjust:function(e){n(function(n){return Bd(n,x(e,function(n){return{uid:n.uid,patch:"to-delete"===n.type?{isDeleting:Vl.PostDelete}:{}}}))})}}},dg=wi("tc-escapeInSidebar"),mg=function(r,t,o){function i(n){return function(){o().showError(n.message,n.error),n.refresh&&n.refresh()}}return{createConversation:function(n){return Je.wrap(r.create({content:n,createdAt:Rd()}).map(function(n){var e=Ye.value;return cg(n,Pf.tc_create_problem,e,h).fold(function(n){return Ye.error(i(n))},function(e){return Ye.value({targetUid:e.conversationUid,fn:function(){o().refreshReadonly()?tt(e,"onError").each(function(n){return n(new Error("Unable to create a new conversation"))}):(t.annotator.annotate(ct(),{uid:e.conversationUid}),o().refreshSidebar(H.some(e.conversationUid),3),tt(e,"onSuccess").each(function(n){return n(e.conversationUid)}))}})})}))},reply:function(t,n){return Je.wrap(r.reply({conversationUid:t,content:n,createdAt:Rd()}).map(function(n){var e=Ye.value;return cg(n,Pf.tc_reply_problem,e,h).fold(function(n){return Ye.error(i(n))},function(n){return Ye.value({targetUid:n.commentUid,fn:function(){o().refreshSidebar(H.some(t),3)}})})}))},editComment:function(e,t,n){return Je.wrap(r.editComment({conversationUid:e,commentUid:t,content:n,modifiedAt:Rd()}).map(function(n){return cg(n,Pf.tc_edit_problem_comment,function(n){return n.canEdit?Ye.value(n):Ye.error({permError:Pf.tc_edit_unauthorised_comment,reason:ag(n)})},h).fold(function(n){return Ye.error(i(n))},function(n){return Ye.value({targetUid:t,fn:function(){o().refreshSidebar(H.some(e),4)}})})}))},deleteComment:function(e,n){return Je.wrap(r.deleteComment({conversationUid:e,commentUid:n}).map(function(n){return cg(n,Pf.tc_delete_problem_comment,function(n){return n.canDelete?Ye.value(n):Ye.error({permError:Pf.tc_delete_unauthorised_comment,reason:ag(n)})},function(){o().refreshSidebar(H.some(e),4)}).fold(function(n){return Ye.error(i(n))},function(n){return Ye.value(function(){o().refreshSidebar(H.some(e),4)})})}))},deleteConversation:function(e){return Je.wrap(r.deleteConversation({conversationUid:e}).map(function(n){return cg(n,Pf.tc_delete_problem_conversation,function(n){return n.canDelete?Ye.value(n):Ye.error({permError:Pf.tc_delete_unauthorised_conversation,reason:ag(n)})},function(){o().refreshSidebar(H.some(e),4)}).fold(function(n){return Ye.error(i(n))},function(n){return Ye.value(function(){var n;n=e,t.execCommand("tc-delete-conversation-at-cursor",null,{conversationUid:n}),o().refreshSidebar(H.none(),2)})})}))}}};tinymce.Resource.add("tinymce.plugins.tinycomments.sidebar",function(n,e,t,r,o,i){function u(n){g.system.broadcastOn([_()],{target:I.fromDom(n.target)})}var c,a,s,f,l,d,m=mg(t,e,function(){return p}),i=n===Cn.Modern?(f=e,l=m,d=i,{createSidebar:function(){return ug(cd.classes.roots.modern,{onEscape:function(){return f.focus()}},l,d)},createController:at}):(c=e,a=m,s=i,{createSidebar:function(){return ug(cd.classes.roots.silver,{onEscape:function(){return c.focus()}},a,s)},createController:at}),g=i.createSidebar(),p=i.createController(t,r,e,g);e.on("ResizeEditor",function(){g.system.broadcastOn([Mf],{})});var h=xn(I.fromDom(document),"mousedown",function(n){g.system.broadcastOn([_()],{target:n.target})});e.on("mousedown",u),e.on("remove",function(){e.off("mousedown",u),h.unbind()});o({controller:p,attachTo:function(n){se(n,g.element)}})})}();