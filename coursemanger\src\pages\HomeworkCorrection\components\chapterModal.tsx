import React, { FC, useState, useEffect } from 'react';
import { Modal, Tree, message, Tabs } from 'antd';
import { getChapter } from '@/api/homeworkCorrection'
import CourseItem from './courseItem'
import { Empty } from "antd";
interface ChapterModalProps {
    modalVisible: boolean;
    onCloseModal: (val?: any) => void;
    setModalVisible: (val: any) => void;
}
const ChapterModal: FC<ChapterModalProps> = ({
    modalVisible,
    onCloseModal,
    setModalVisible
}) => {
    const [chapterVisible, setChapterVisible] = useState<boolean>(false)

    const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
    const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
    const [autoExpandParent, setAutoExpandParent] = useState<boolean>(true);
    const [chapterList, setChapterList] = useState<any>([])
    const [selectCourse, setSelectCourse] = useState<any>({})
    const [chapterInfo, setChapterInfo] = useState<any>([])
    const [activeKey, setActiveKey] = useState<string>('1');

    useEffect(() => {
        if (!modalVisible) {
            setChapterVisible(false)
            setCheckedKeys([])
        }
    }, [modalVisible])

    const onCheck: any = (checkedKeys: React.Key[], info: any) => {
        // 只保留第一个选中的节点，实现单选
        const singleCheckedKey = info.node ? [info.node.id] : [];
        setCheckedKeys(singleCheckedKey);

        // 定义递归查找函数
        const findNodeRecursively: any = (nodes: any[], targetKey: React.Key) => {
            for (const node of nodes) {
                if (node.id === targetKey) {
                    return node;
                }
                if (node.children && node.children.length > 0) {
                    const foundInChildren = findNodeRecursively(node.children, targetKey);
                    if (foundInChildren) {
                        return foundInChildren;
                    }
                }
            }
            return null;
        };

        for (const item of singleCheckedKey) {
            // 使用递归函数查找当前选中的节点
            const currentNode = findNodeRecursively(chapterList, item);
            if (currentNode) {
                // 判断为根节点
                if (!currentNode?.children?.length) {
                    const parentNode = findNodeRecursively(chapterList, currentNode?.parentId);
                    const obj = {
                        ...parentNode,
                        child: currentNode
                    }
                    setChapterInfo(obj)
                }
            }
        }
    };

    // 获取章节
    const getChapterList = async () => {
        getChapter({
            courseId: selectCourse.id,
            status: selectCourse.courseStatus,
            courseSemester: selectCourse.courseSemester,
            courseType: selectCourse.courseType
        }).then((res: any) => {
            if (res?.status == 200) {
                // 递归处理树形数据
                const processTreeData = (data: any[]) => {
                    return data.reduce((acc: any[], item: any) => {
                        // 递归处理子节点
                        const newChildren = item.children && item.children.length > 0 ? processTreeData(item.children) : [];
                        // 检查当前节点或其子节点是否包含作业类型
                        const hasHomework = item.resourseType === 'homework' || newChildren.length > 0;
                        if (hasHomework) {
                            const newItem = {
                                ...item,
                                name_: item.name,
                                name: item.resourseType === 'homework' ? `【作业】${item.name}` : `${'第' + item.order + '章'} ${item.name}`,
                                children: newChildren,
                                checkable: item.resourseType === 'homework' ? true : false
                            };
                            acc.push(newItem);
                        }
                        return acc;
                    }, []);
                };
                const chapterData = processTreeData(res.data);
                setChapterList(chapterData);
            }
        });
    };

    return (
        <>
            <Modal
                width={'90%'}
                wrapClassName='chapter-modal-wrapper'
                title="选择课程"
                open={modalVisible}
                onCancel={() => {
                    onCloseModal(null)
                    setSelectCourse({})
                    setModalVisible(false)
                }}
                onOk={() => {
                    if (!selectCourse.id) {
                        message.error('请选择课程')
                        return
                    }
                    setChapterVisible(true)
                    getChapterList()
                }}
                okText="下一步"
            >
                <Tabs activeKey={activeKey} onChange={(tabKey) => setActiveKey(tabKey)}>
                    <Tabs.TabPane tab="班级课" key="1">
                        <CourseItem
                            modalVisible={modalVisible}
                            activeKey={activeKey}
                            emitSelectCourse={(val: any) => {
                                setSelectCourse(val)
                            }}
                        />
                    </Tabs.TabPane>
                    <Tabs.TabPane tab="公开课" key="2">
                        <CourseItem
                            modalVisible={modalVisible}
                            activeKey={activeKey}
                            emitSelectCourse={(val: any) => {
                                setSelectCourse(val)
                            }}
                        />
                    </Tabs.TabPane>
                    {/* <Tabs.TabPane tab="微课" key="3">
                        <CourseItem
                        activeKey={activeKey}
                        emitSelectCourse={(val: any) => {
                            setSelectCourse(val)
                        }}
                        />
                    </Tabs.TabPane> */}
                    <Tabs.TabPane tab="培训课" key="4">
                        <CourseItem
                            modalVisible={modalVisible}
                            activeKey={activeKey}
                            emitSelectCourse={(val: any) => {
                                setSelectCourse(val)
                            }}
                        />
                    </Tabs.TabPane>
                </Tabs>
            </Modal>
            <Modal
                width={600}
                wrapClassName='chapter-modal-tree-wrapper'
                title="选择课程作业"
                open={chapterVisible}
                onCancel={() => {
                    setChapterVisible(false)
                    setCheckedKeys([])
                }}
                onOk={() => {
                    // 判断是否选择了章节
                    if (checkedKeys.length == 0) {
                        message.error('请选择课程作业')
                        return
                    }
                    onCloseModal({ chapterInfo, selectCourse })
                }}
                okText="确定"
                cancelText="上一步"
            >
                {
                    chapterList?.length > 0 ? <>
                        <p className='course-name'>{selectCourse.name}</p>
                        <Tree
                            checkable
                            expandedKeys={expandedKeys}
                            onExpand={(expandedKeysValue) => setExpandedKeys(expandedKeysValue)}
                            autoExpandParent={autoExpandParent}
                            checkedKeys={checkedKeys}
                            onCheck={onCheck}
                            fieldNames={{ title: 'name', key: 'id' }}
                            treeData={chapterList}
                        />
                    </> : <Empty description="该课程下无作业信息" />
                }
            </Modal>
        </>
    );
};

export default ChapterModal;