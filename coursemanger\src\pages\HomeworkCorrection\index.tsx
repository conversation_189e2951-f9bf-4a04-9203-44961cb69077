import React, { useEffect, useState } from "react";
import { CloseCircleOutlined, PlusCircleFilled } from '@ant-design/icons'
import { Empty, InputNumber, Input, Button, Collapse, message } from 'antd'
import LoadingPage from './components/loadingPage'
import ChapterModal from './components/chapterModal';
import Empty2 from './components/empty'
import ContentRight from "./components/ContentRight";
import { getStudentAnswer, reqMessage, getHomeworkDetail } from '@/api/homeworkCorrection'
import { getUuid } from "@/utils/utils";
import { data, studentAnswers } from './utils'
import { getWrite } from './AiGenerate/common'
import { submitScoreCorrection } from '@/api/homework';
import './index.less'

const { Panel } = Collapse;
const HomeworkCorrection = () => {
    // 控制显示课程弹窗
    const [modalVisible, setModalVisible] = useState(false)
    // 是否选择了作业
    const [isSelect, setIsSelect] = useState<boolean>(false)
    // 当前选择的课程
    const [selectCourse, setSelectCourse] = useState<any>({})
    // 当前选择的作业
    const [selectWork, setSelectWork] = useState<any>({})
    // 题目列表
    const [questionList, setQuestionList] = useState<any[]>([])
    // 折叠面板
    const [activeKey, setActiveKey] = useState<string[]>([]);
    // 评分标准设置
    const [scoreStandard, setScoreStandard] = useState<any>([
        {
            point: '知识点理解与运用',
            proportion: 50,
            id: getUuid()
        },
        {
            point: '答题逻辑',
            proportion: 50,
            id: getUuid()
        },
    ])

    // 开始批改按钮的loading
    const [btnLoading, setBtnLoading] = useState(false)
    // 未开始批改时右侧的空页面
    const [showEmpty, setShowEmpty] = useState<boolean>(true)
    // 右侧的主体内容
    const [showContent, setShowContent] = useState<boolean>(false)
    //  正在批改时右侧的loading
    const [showLoading, setShowLoading] = useState<boolean>(false)
    // 评分标准警告提示
    const [warnFalg, setWarnFalg] = useState<boolean>(false)

    // 选择作业的回调
    const closeChapterModal = (val: any) => {
        if (val) {
            // chapterInfo代表选择的作业， selectCourse代表选中的课程
            const { selectCourse, chapterInfo } = val
            console.log(chapterInfo, 'chapterInfo----')
            setSelectCourse(selectCourse)
            setSelectWork(chapterInfo)
            // homeworkId parentId courseId
            getHomeworkDetail("resource", chapterInfo.child.describe, chapterInfo.id, selectCourse.id).then((res: any) => {
                console.log(res, 'res') // 接口返回的res
                if (res.status === 200) {
                    const questionList = res.data?.questions?.map((item: any, index: number) => {
                        return {
                            index: index + 1, // 第几题
                            id: item.id,  // questionId 题目id
                            homeworkId: item.homeworkId, // homeworkId 作业id:
                            questions_type: item.question.questions_type, // 题目类型 1 主观题 2 客观题 3 简答题 4 论述题 5 计算题 6 编程题 7 其他
                            questions_analysis: item.question.questions_analysis?.replace(/<[^>]*>/g, ''), // 解析
                            questions_content: item.question.questions_content?.replace(/<[^>]*>/g, ''), // 题目
                            score: item.score // 总分
                        }
                    }).filter((item: any) => item.questions_type == 3)
                    if (questionList.length === 0) {
                        message.error('该作业下没有主观题')
                        return;
                    } else {
                        setModalVisible(false)
                        setIsSelect(true)
                    }
                    setQuestionList(questionList);
                    setActiveKey([questionList[0]?.id])
                } else {
                    message.error(res?.message)
                }
            });
        }
    }

    // 输入解析
    const handleChangeAnalysis = (e: any, item: any) => {
        const value = e.target.value;
        setQuestionList(questionList.map((question: any) => {
            if (question.id === item.id) {
                return { ...question, questions_analysis: value };
            } else {
                return question;
            }
        }));
    }

    // 评分标准input输入
    const handleChangeInput = (e: any, item: any) => {
        const value = e.target.value;
        setScoreStandard(scoreStandard.map((score: any) => {
            if (score.id === item.id) {
                return { ...score, point: value };
            } else {
                return score;
            }
        }));
    };

    // 评分标准百分比输入
    const handleChangeInputNumber = (value: any, item: any) => {
        const scoreStandardTmp = scoreStandard.map((score: any) => {
            if (score.id === item.id) {
                return { ...score, proportion: value };
            } else {
                return score;
            }
        })
        const sum = scoreStandardTmp.reduce((total: number, item: any) => total + item.proportion, 0);
        if (sum <= 100) {
            setScoreStandard(scoreStandardTmp);
            setWarnFalg(false)
        } else {
            message.warning('评分标准之和需等于100%')
            setWarnFalg(true)
        }
    }

    // 删除评分标准
    const closeStandard  = (item: any) => {
        const scoreStandardTmp = scoreStandard.filter((score: any) => score.id !== item.id)
        setScoreStandard(scoreStandardTmp)

        const sum = scoreStandardTmp.reduce((total: number, item: any) => total + item.proportion, 0);
        if (sum <= 100) {
            setWarnFalg(false)
        } else {
            message.warning('评分标准之和需等于100%')
            setWarnFalg(true)
        }
    }

    // 重新选择作业
    const reset = () => {
        setIsSelect(false)
        setSelectCourse(null)
        setSelectWork(null)
        setQuestionList([])
        setShowEmpty(true)
        setShowContent(false)
    }

    // 消息函数返回体结构
    const chatMessageFunction = (fun: any, id: string, studentCdoe: string) => {
        return new Promise((resolve) => {
            fun.then((response: any) => {
                const reader = response.body.getReader();
                if (response.status !== 200) throw (response);
                // 处理流数据
                const write = getWrite(
                    reader,
                    response.headers.get('Content-Type') !== 'application/json',
                    (content: any, config: any, finish: boolean, firstChatInfo) => {
                        if (finish) {
                            setQuestionList((pre) => {
                                const index: any = pre.findIndex(item => item.id === id);
                                const eleIndex = pre[index].studentAnswer.findIndex((item: any) => item.stuCode === studentCdoe);
                                pre[index].studentAnswer[eleIndex].score = content?.score || '-';
                                pre[index].studentAnswer[eleIndex].comment = content?.comment || '-';
                                pre[index].studentAnswer[eleIndex].isLoading = false
                                pre[index].studentAnswer[eleIndex].unconfirmId = getUuid()
                                return pre;
                            });
                            resolve(true);
                        } else {
                            if (content?.statusText != '大模型返回格式异常') {
                                message.error(content?.statusText || '服务器异常！');
                            }
                            setQuestionList((pre) => {
                                const index: any = pre.findIndex(item => item.id === id);
                                const eleIndex = pre[index].studentAnswer.findIndex((item: any) => item.stuCode === studentCdoe);
                                pre[index].studentAnswer[eleIndex].score = '-';
                                pre[index].studentAnswer[eleIndex].comment = '-';
                                pre[index].studentAnswer[eleIndex].isLoading = false
                                pre[index].studentAnswer[eleIndex].unconfirmId = getUuid()
                                return [...pre];
                            });
                            resolve(true);
                        }
                    }
                )
                reader.read().then(write)
            }).catch((e: any) => {
                if (e?.statusText != '大模型返回格式异常') {
                    message.error(e?.statusText || '服务器异常！');
                }
                setQuestionList((pre) => {
                    const index: any = pre.findIndex(item => item.id === id);
                    const eleIndex = pre[index].studentAnswer.findIndex((item: any) => item.stuCode === studentCdoe);
                    pre[index].studentAnswer[eleIndex].score = '-';
                    pre[index].studentAnswer[eleIndex].comment = '-';
                    pre[index].studentAnswer[eleIndex].isLoading = false
                    pre[index].studentAnswer[eleIndex].unconfirmId = getUuid()
                    return [...pre];
                });
                resolve(true);
            })
        })
    }

    // 开始批改
    const handleGenerate = async () => {
        // 校验百分比
        const sum = scoreStandard.reduce((total: number, item: any) => total + item.proportion, 0);
        if (sum !== 100) {
            message.warning('评分标准之和需等于100%');
            return;
        }
        // 校验题目解析是否为空，给出提示哪一道题
        // const isEmpty = questionList.filter((item: any) => item.questions_analysis === '');
        // if (isEmpty.length > 0) {
        //     const text = isEmpty.map((item: any) => `第${item.index}题`).join('、') + '的题目解析不能为空';
        //     message.warning(text);
        //     return;
        // }

        setBtnLoading(true);
        setShowLoading(true);
        // 请求学生信息答案
        const answerPromises = questionList.map((item: any, index: number) => {
            const paramsAnswer = {
                homeworkId: item?.homeworkId || '',
                questionId: item?.id || ''
            };
            return getStudentAnswer(paramsAnswer).then((res: any) => {
                if (res.status === 200) {
                    if (index == 0) {
                        setShowContent(true) // 显示右侧的内容
                        setShowEmpty(false) // 隐藏右侧的空页面
                        setShowLoading(false)
                    }
                    const studentAnswer = res.data?.map((ele: any) => {
                        return {
                            ...ele,
                            answer: ele.answer?.replace(/<[^>]*>/g, ''),
                            subId: ele.id
                        }
                    })
                    return {
                        ...item,
                        studentAnswer
                    };
                }
                return item;
            });
        });
        // 等待所有请求完成
        const hasAnswerList = await Promise.all(answerPromises);
        setQuestionList(hasAnswerList)

        //  请求批改后的答案
        const results: any[] = []
        for (const item of hasAnswerList) {
            if (item.studentAnswer) {
                for (const ele of item.studentAnswer) {
                    setQuestionList((pre) => {
                        const index: any = pre.findIndex(ele => ele.id === item.id);
                        const eleIndex = pre[index].studentAnswer.findIndex((element: any) => element.stuCode === ele.stuCode);
                        pre[index].studentAnswer[eleIndex].score = <span style={{ fontSize: 12, color: '#6251FE' }}>AI批改中...</span>;
                        pre[index].studentAnswer[eleIndex].comment = <span style={{ fontSize: 12, color: '#6251FE', minWidth: '100' }}>AI批改中...</span>;
                        pre[index].studentAnswer[eleIndex].isLoading = true
                        pre[index].studentAnswer[eleIndex].unconfirmId = getUuid()
                        return [...pre];
                    });
                    //  请求批改后的答案 // 请求批改后的答案
                    let params = {
                        content: item.questions_content,
                        analysis: item.questions_analysis,
                        answer: ele.answer,
                        totalScore: item.score,
                        scorePoint: scoreStandard
                    }
                    const requestApi = reqMessage(params)
                    const response = await chatMessageFunction(requestApi, item.id, ele.stuCode);
                    results.push(response)
                }
            }
        }
        Promise.all(results).then(() => {
            setBtnLoading(false)
        })
    };

    console.log(questionList, '---------------------------------')
    return (
        <div className="homework-correction">
            <div className="left">
                <div className='left-title'>
                    <img src={require("@/assets/imgs/teachingPlan/ai_logo1.png")} alt="title" />
                    <img src={require("@/assets/imgs/teachingPlan/homework_title.png")} alt="title" />
                </div>
                {
                    isSelect && (
                        questionList?.length > 0 ? <div className="left-content-wrap">
                            <div className="left-content">
                                <div className="top-title">
                                    <img src={require('@/assets/imgs/teachingPlan/shezhi.png')} alt="" />
                                    <span className="bigTitle" title={selectWork.name}>{selectWork.name}</span>
                                    <span className="smallTitle" title={`（来自《 ${selectCourse.name} 》）`}>（来自《 {selectCourse.name} 》）</span>
                                    <CloseCircleOutlined onClick={reset} />
                                </div>
                                <div className="left-list">

                                    <Collapse expandIconPosition="right" activeKey={activeKey} onChange={(val) => setActiveKey(typeof val === 'string' ? [val] : val)} accordion>
                                        {
                                            questionList.map((item: any) => {
                                                return <Panel header={`第${item.index}题（主观题${item.score}分）`} key={item.id}>
                                                    <div className="all-content">
                                                        <div className="title">题目是：{item.questions_content}</div>
                                                        <div className="analysis">
                                                            <div className="des">
                                                                <div className="dot"></div>
                                                                <span>题目解析：</span>
                                                            </div>
                                                            <Input.TextArea placeholder="你可在此输入题目解析提供参考" bordered={false} value={item.questions_analysis} onChange={(e) => handleChangeAnalysis(e, item)} />
                                                        </div>
                                                    </div>
                                                </Panel>
                                            })
                                        }
                                    </Collapse>

                                </div>
                                <div className="top-title">
                                    <img src={require('@/assets/imgs/teachingPlan/moban_icon.png')} alt="" />
                                    <span className="bigTitle">评分标准设置</span>
                                    <span className="smallTitle">(选填)</span>
                                </div>

                                <div className="bottom">
                                    {
                                        scoreStandard?.map((item: any) => {
                                            return <div className="bottom-item" key={item.id}>
                                                <Input placeholder="请输入评分点" value={item.point} onChange={(e) => handleChangeInput(e, item)} />
                                                <InputNumber className={warnFalg ? 'warn' : ''} value={item.proportion} min={0} max={100} addonAfter='%' onChange={(e) => handleChangeInputNumber(e, item)} />
                                                {
                                                    scoreStandard?.length > 1 && <CloseCircleOutlined onClick={() => closeStandard(item)} />
                                                }
                                            </div>
                                        })
                                    }
                                    <div className='add-btn' onClick={() => setScoreStandard([...scoreStandard, { point: '', proportion: 0, id: getUuid() }])}>
                                        <PlusCircleFilled />
                                        添加
                                    </div>
                                </div>
                            </div>
                            <Button disabled={!questionList?.length} loading={btnLoading} onClick={handleGenerate}>开始批改</Button>
                        </div> : <Empty description="无题目信息" />
                    )
                }
                {
                    !isSelect && <div className="select-wrap">
                        <div className="select">
                            <PlusCircleFilled onClick={() => setModalVisible(true)} />
                            <span onClick={() => setModalVisible(true)}>选择作业</span>
                        </div>
                    </div>
                }
            </div>
            <div className="right">
                {
                    showContent && <ContentRight
                        questionList={questionList}
                        changeQuestionList={(list: any) => setQuestionList(list)}
                        btnLoading={btnLoading}
                        ids={{
                            homeworkId: selectWork.child.describe,
                            parentId: selectWork.id,
                            courseId: selectCourse.id,
                            courseType: selectCourse.courseType
                        }}
                    />
                }

                {/* 为批改时的空提示 */}
                {showEmpty && <Empty2 />}

                {/* 正在批改时的loading效果 */}
                <LoadingPage showLoading={showLoading} />
            </div>

            <ChapterModal
                setModalVisible={setModalVisible}
                modalVisible={modalVisible}
                onCloseModal={closeChapterModal}
            />
        </div >
    );
};

export default HomeworkCorrection;
