import {
    getSubmstudentList,
    downloadSubmstudents
} from '@/api/homework';
import { LeftOutlined, LoadingOutlined } from '@ant-design/icons';
import { Button, Popover, Spin, Table, TablePaginationConfig, message } from 'antd';
import React, { FC, useEffect, useState } from 'react';
import { useDispatch, useLocation, useSelector } from 'umi';

import './HomeworkObeject.less';

import useLocale from '@/hooks/useLocale';
import RenderHtml from '@/components/renderHtml';


interface IHomeworkDetail {
    homeworkDetail: any;
    submissionData: any;
    handleBack: (isRefresh?: boolean) => void;
    // query: any;
    hasNext?: boolean;
    homeworkItem: any;
    isStu?: boolean;
    onViewDetail: (record: any,type:string) => void;
    onHandOver?: () => void;
}

const HomeworkDetail: FC<IHomeworkDetail> = ({
    homeworkDetail,
    submissionData,
    // query,
    onHandOver,
    onViewDetail,
    hasNext = true,
    handleBack,
    homeworkItem,
    isStu
}) => {
    const location: any = useLocation();
    const [loading, setLoading] = useState<boolean>(false);
    const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;
    const dispatch = useDispatch();
    const { t } = useLocale();
    const [dataTeamSource, setdataTeamSource] = useState<any>([]);
    const [total, setTotal] = useState<number>(0);
    const handleViewDetail = (record: any) => {
        console.log(record, 'record');
    };
    const teamColumns = [
        {
            title: t("姓名"),
            dataIndex: 'stuName',
            key: 'stuName',
        },
        {
            title: t("学号"),
            dataIndex: 'stuCode',
            key: 'stuCode',
        },
        {
            title: t("学院"),
            dataIndex: 'collegeName',
            key: 'collegeName',
        },
        {
            title: t("专业"),
            dataIndex: 'majorName',
            key: 'majorName',
        },
        {
            title: t("提交状态"),
            dataIndex: 'submitState',
            key: 'submitState',
            render: (value: number) => value === 1 ? t("未提交") : value === 2 ? t("已提交") : '',
        },
        {
            title: t("得分"),
            dataIndex: 'score',
            key: 'score',
        },
        {
            title: t("操作"),
            dataIndex: 'action',
            key: "action",
            render: (text: string, record: any) => {
                const isHomework = homeworkItem.resourseType == 'homework'
                return <>
                    {record.submitState === 2 ? (
                        <a style={{ marginRight: "10px" }} onClick={() => onViewDetail(record,'subject')}>{t("查看详情")}</a>
                    ) : (
                        <a style={{ marginRight: "10px", color: '#999', cursor: 'not-allowed' }}>{t("查看详情")}</a>
                    )}
                </>
            }
        },
    ]
    const [query, setQuery] = useState<any>({
        page: 1,
        pageSize: 10,
    });

    useEffect(() => {
        getSubmissionquestion();
    }, [homeworkDetail, query, submissionData]);

    const getSubmissionquestion = () => {
        getSubmstudentList({ ...query, homeworkId: submissionData.homeworkId, resourceQuestionId: submissionData.resourceQuestionId, }).then((res: any) => {
            if (res.status === 200) {
                setdataTeamSource([...res.data.data]);
                setTotal(res.data.totalCount);
            }
        }).finally(() => { setLoading(false); });
    };

    const handleTableChange = (pagination: TablePaginationConfig, filters: Record<string, any>, sorter: any) => {
        setQuery({
            ...query,
            pageSize: pagination.pageSize,
            page: pagination.current,
        });
    };

    //作业导出
    const handleExport = () => {
        downloadSubmstudents({ questionName: submissionData.questions_content, homeworkId: submissionData.homeworkId, resourceQuestionId: submissionData.resourceQuestionId }).then((res: any) => {
            if (res.status === 400) {
                message.error(res.message);
            } else {
                let link = document.createElement('a');
                link.style.display = 'none';
                link.href = `/exam-api/resource/homework/download/question/students?courseId=${location.query.id}&homeworkId=${submissionData?.id}&resourceQuestionId=${submissionData?.resourceQuestionId}&questionName=${submissionData?.questions_content}`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link); //下载完成移除元素
            }
        });
    };

    return (
        <Spin indicator={antIcon} spinning={loading}>
            <div className="homeworkobejct-container">
                <div className="header-container">
                    <div className="back-btn" onClick={() => handleBack()}>
                        <LeftOutlined />
                        {t('返回')}
                    </div>
                </div>
                <div className="content-container">
                    <div className='header_boxs' >
                        {/* <Popover className='timu' content={<div style={{ maxWidth: '40%' }}><RenderHtml dangerouslySetInnerHTML={{__html: homeworkDetail.questions_content  }} /></div>} overlayStyle={{ maxWidth:'40%' }}> */}
                            <div className='timu'  style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', maxWidth: '40%' }}>
                                <RenderHtml dangerouslySetInnerHTML={{ __html: homeworkDetail.questions_content  }} className='special-dom' />
                            </div>
                        {/* </Popover> */}
                        <div className='grade-time'>
                            <div className="grade">{t("认知层面：")}<span>{homeworkDetail.cognitive_level || '--'}</span></div>
                            <div className="grade">{t("难度：")}<span>{homeworkDetail.questions_difficulty || '--'}</span></div>
                            <div className="grade">{t("平均得分：")}<span>{homeworkDetail.averageScore || 0}{t("分")}</span></div>
                            <div className="grade">{t("满分：")}<span>{homeworkDetail.totalScore || 0}{t("分")}</span></div>
                            <div className="grade">{t("正确率：")}<span>{homeworkDetail.correctness || 0}% </span></div>
                            <div className="time">{t("提交率：")}<span>{homeworkDetail.submissionRate || 0}% </span></div>
                        </div>
                    </div>
                    <div className="table_box">
                        <div className='table_btn'>
                            <Button type='primary' onClick={handleExport} ghost >{t("导出Excel")}</Button>
                        </div>
                        <Table
                            dataSource={dataTeamSource}
                            columns={teamColumns}
                            rowKey="id"
                            loading={loading}
                            pagination={{
                                size: "small",
                                total: total,
                                showTotal: (total) => t("共{name}条", String(total)),
                                showSizeChanger: true,
                                showQuickJumper: true,
                                pageSize: query.pageSize,
                                current: query.page
                            }}
                            scroll={{ y: "calc(100vh - 536px)" }}
                            onChange={handleTableChange}
                        />
                    </div>
                </div>
            </div>
        </Spin>
    );
};
export default HomeworkDetail;
