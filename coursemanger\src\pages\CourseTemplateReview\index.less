.my-review-container {
  padding: 19px 20px 0px;

  .btn-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
    margin-bottom: 16px;
    position: relative;

    .ant-space>.ant-space-item:last-child {
      position: absolute;
      right: 1%;
    }
  }

  .btn-wrp {
    display: flex;
    align-items: center;

    button.ant-btn.ant-btn-text:not(:last-child) {
      margin: 0;
    }

    .disabled {
      color: rgba(0, 0, 0, .25);
    }

    .item_ {
      position: relative;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        .ant-btn {
          color: var(--primary-color);
        }

        color:var(--primary-color);
      }

      &.disabled {
        cursor: no-drop;
        color: rgba(0, 0, 0, 0.25) !important;

        .ant-btn {
          color: rgba(0, 0, 0, 0.25) !important;
        }
      }
    }

    .item_:not(:last-child) {
      &::after {
        content: "";
        display: inline-block;
        width: 1px;
        height: 16px;
        background: #D8D8D8;
        right: 0;
        top: 8px;
        position: absolute;
      }
    }
  }

  .mode_switch_container {
    float: right;
    display: flex;
    align-items: center;

    .mode_switch {
      cursor: pointer;
      font-size: 16px;
      margin-left: 10px;

      .active,
      &:hover {
        color: var(--primary-color);
      }
    }
  }

  .data_wrapper {
    display: flex;
    flex-wrap: wrap;
    height: calc(100vh - 260px);
    overflow-y: auto;
    align-content: flex-start;

    .ant-empty {
      width: 100%;
    }
  }

  .splitLine {
    border-bottom: 2px #F7F9FA solid;
    margin-top: 8px;
  }

  .button_box .ant-space>.ant-space-item:last-child {
    position: absolute;
    right: 1%;
  }

  .ant-form {
    .ant-form-item {
      margin-bottom: 16px;
    }
  }

  .reset-wrp {
    cursor: pointer;
    color: #525252;
    margin: 6px 18px 0 -6px;

    .anticon {
      margin-left: 5px;
    }
  }
}

.checkbox-container {
  margin-bottom: 10px;
}
