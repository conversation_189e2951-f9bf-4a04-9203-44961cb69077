/*
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2021-06-22 16:24:11
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2021-06-29 11:51:07
 */
tinymce.PluginManager.add('kityformula-editor', function (editor, url) {
    var pluginName = '插入数学公式';
    var baseURL = tinymce.baseURL + '/plugins/kityformula-editor/kityFormula.html';
    editor.on('dblclick', function () {
        var sel = editor.selection.getContent();
        var path = /\<img(.*?)src="data:image\/png;base64,[A-Za-z0-9+/=]*"(.*?)data-latex="(.*?)" \/>/g;
        var path2 = /data-latex="(.*?)"/g;

        if (sel.search(path) == 0) {
            sel.replace(path2, function ($0, $1) {
                var param = encodeURIComponent($1);
                openDialog(param);
                return $0;
            });
        };
    });

    var openDialog = function (param) {
        return editor.windowManager.openUrl({
            title: pluginName,
            size: 'large',
            width: 785,
            height: 475,
            url: param ? baseURL + "?c=" + param : baseURL,
            buttons: [
                {
                    type: 'cancel',
                    text: 'Close'
                },
                {
                    type: 'custom',
                    text: 'Save',
                    name: 'save',
                    primary: true
                },
            ],
            onAction: function (api, details) {
                switch (details.name) {
                    case 'save':
                        api.sendMessage("save");
                        break;
                    default:
                        break;
                };
            }
        });
    };

    editor.ui.registry.getAll().icons.picturetitle || editor.ui.registry.addIcon('kityformula-editor', '<svg t="1624938071574" class="icon" viewBox="0 0 1194 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15112" width="30" height="30"><path d="M86.443 688.043l-84.779 1.152L0.256 580.693l158.08-2.133 40.747 97.664L368.384 0.64h825.344v108.501H452.907l-228.182 910.763-138.24-331.861zM502.4 939.179L688.896 623.06 507.861 276.352h492.971l68.48 162.73-100.01 42.07-40.534-96.512h-241.92l126.08 241.365-120.661 204.672h237.781l39.979-87.466 98.56 45.141-68.95 150.827H502.4z" p-id="15113"></path></svg>');

    editor.ui.registry.addButton('kityformula-editor', {
        icon: 'kityformula-editor',
        tooltip: pluginName,
        onAction: function () {
            openDialog();
        }
    });
    editor.ui.registry.addMenuItem('kityformula-editor', {
        icon: 'kityformula-editor',
        text: pluginName,
        onAction: function () {
            openDialog();
        }
    });
    return {
        getMetadata: function () {
            return {
                name: pluginName,
                url: "http://hgcserver.gitee.io",
            };
        }
    };
});