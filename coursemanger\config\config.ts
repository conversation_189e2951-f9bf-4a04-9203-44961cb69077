/*
 * @Description:
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2021-05-18 14:58:49
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2021-12-15 17:29:46
 */
import { defineConfig } from 'umi';
import {proxyConfig} from './env.proxy';
import routes from './config.routes';
export default defineConfig({
  // favicon: '/websitebg.png',
  nodeModulesTransform: {
    type: 'none',
    exclude: [],
  },
  // fastRefresh: {},
  // cheap-module-source-map
  devtool: process.env.NODE_ENV === 'development' ? 'eval-cheap-module-source-map' : false,
  publicPath: process.env.NODE_ENV == 'development' ? './' :'/learn/workbench/',
  history: {
    // 路由模式
    type: 'hash', //
  },
  routes: routes, //concat(routes, routesPublisher as any),
  proxy: proxyConfig,
  targets: {
    ie: 10,
  },
  dynamicImport: {
    loading: '@/components/loading/loading',
  },
  theme: {
    //   // 主题颜色
    //   '@primary-color': '#2F5AFC',
    //   // '@primary-color': '#208bfa',
    //   '@second-color': '#4ca2fb',
    //   '@third-color': '#4071d7',
    //   '@fourth-color': '#4071d7',
    //   // 川大出版社
    '@primary-color-publisher': '#2F5AFC',
    //   // '@second-color': '#4ca2fb',
    //
    //   // '@primary-color': '#BA2A17',
    //   // '@second-color': '#faece6',
    //   // '@third-color': '#FAD81E',
    //   // '@fourth-color': '#F3960D',
  },
  hash: true,
  locale: {
    // 国际化配置
    default: 'zh-CN',
    antd: true,
    title: true,
  },
  dva: {
    // dva配置
    immer: true,
    hmr: false,
  },
  // fastRefresh : {

  // },
  // mfsu: {
  //   development : {
  //     output : "./.mfsu-dev",
  //   },
  //   production : {
  //     output : "./.mfsu-prod",
  //   }
  // },
  webpack5: {},
  // esbuild: {},
  metas: [
    {
      httpEquiv: 'Cache-Control',
      content: 'no-cache',
    },
    {
      httpEquiv: 'Pragma',
      content: 'no-cache',
    },
    {
      httpEquiv: 'Expires',
      content: '0',
    },
  ],
  // externals: process.env.NODE_ENV === 'development' ?{
  //   'react': 'React',
  //   'react-dom': 'ReactDOM',
  //   moment: 'moment',
  //   "lodash":'window._',
  //   'moment/locale/zh-cn': 'moment.locale',
  //   ...externalsAntd,
  //   // '@antv/x6': 'window.X6',
  //   // '@antv/layout':'window.layout',
  //   // "echarts":"echarts",
  //   // "jsplumb":"window.jsPlumb",
  //   // "jquery": "jQuery",
  //   // "@wangeditor/editor":"wangEditor",
  //   // "@wangeditor/editor-for-react":"WangEditorForReact",
  //   // "html2canvas":"html2canvas",    
  //   // "antd":"antd"
  // }:{},
  // styles: ['http://localhost:8000/js/antd/4.23.6/antd.min.css'],
  // scripts: [
  //   'http://localhost:8000/js/react/react.production.min.js',
  //   'http://localhost:8000/js/react-dom/react-dom.production.min.js',
  //   'http://localhost:8000/js/moment/moment.js',
  //   'http://localhost:8000/js/lodash/lodash.js',
  //   'http://localhost:8000/js/moment/zh-cn.min.js',
  //   'http://localhost:8000/js/antd/antd.min.js'
  // ],
  // [
    // 'https://gw.alipayobjects.com/os/lib/react/16.14.0/umd/react.production.min.js',
    // 'https://gw.alipayobjects.com/os/lib/react-dom/16.12.0/umd/react-dom.production.min.js',
    // 'https://unpkg.com/@antv/x6@1.34.1/dist/x6.js',
    // 'https://unpkg.com/@antv/layout@0.3.23/dist/layout.min.js',
    // 'https://unpkg.com/echarts@5.0.2/dist/echarts.js',
    // 'https://unpkg.com/jsplumb@2.15.6/dist/js/jsplumb.js',
    // 'https://unpkg.com/lodash@4.17.21/lodash.js',
    // 'https://unpkg.com/jquery@3.7.1/dist/jquery.js',
    // 'https://unpkg.com/@wangeditor/editor@5.1.23/dist/index.js',
    // 'https://unpkg.com/@wangeditor/editor-for-react@1.0.6/dist/index.js',
    // 'https://unpkg.com/html2canvas@1.4.1/dist/html2canvas.js',
    // 'https://unpkg.com/moment@2.30.1/moment.js',
    // 'https://unpkg.com/antd@4.24.16/dist/antd.min.js'
  // ],
  analyze: {
    analyzerMode: 'server',
    analyzerPort: 9999,
    openAnalyzer: true,
    generateStatsFile: false,
    statsFilename: 'stats.json',
    logLevel: 'info',
    defaultSizes: 'parsed',
  },
  chunks:['vendors', 'umi'],
  chainWebpack: function (config, { webpack }) {
    config.merge({
      optimization: {
        // runtimeChunk: true,
        splitChunks: {
          chunks: 'all',
          minSize: 20000,
          minChunks: 2,
          automaticNameDelimiter: '.',
          cacheGroups: {
            vendors: {
              test: /[\\/]node_modules[\\/]/,
              name(module:any) {
                const reg = /(echarts|@antv|@wangeditor|zrender|lodash|html2canvas|jsplumb|jquery|xgplayer|xgplayer-flv|xgplayer-hls|crypto-js|@ant-design|antd|konva|regl|cropperjs|rc-picker|rc-table|rc-tree|rc-tabs|rc-select|react-dom)/;
                if (reg.test(module.identifier())) {
                  const [chunkName] = reg.exec(module.identifier()) || [];
                  return `npm.${chunkName}`;
                };
                return 'vendors';
              },
              priority: 10,
            }            
          },
        },
      }
    });
  },
  // chainWebpack(config,{ webpack }) {
  //   if (process.env.NODE_ENV === 'production') {
  //     config.merge({
  //       optimization: {
  //         minimize: true,
  //         splitChunks: {
  //           chunks: 'async',
  //           minSize: 30000,
  //           minChunks: 2,
  //           automaticNameDelimiter: '.',
  //           cacheGroups: {
  //             vendor: {
  //               name: 'vendors',
  //               test: /^.*node_modules[\\/](?!lodash|react-virtualized|antd).*$/,
  //               chunks: 'all',
  //               priority: -10,
  //             },
  //             "async-vendors": {
  //               test: /[\\/]node_modules[\\/]/,
  //               minChunks: 2,
  //               chunks: "async",
  //               name: "async-vendors",
  //               priority: -20,
  //               reuseExistingChunk: true,
  //             },
  //             antd: {
  //               name: 'antd',
  //               test: /[\\/]node_modules[\\/]antd[\\/]/,
  //               chunks: 'all',
  //               priority: 9,
  //             },
  //             lodash: {
  //               name: 'lodash',
  //               test: /[\\/]node_modules[\\/]lodash[\\/]/,
  //               chunks: 'all',
  //               priority: -2,
  //             },
  //             lib: {
  //               test(module: any) {
  //                 return (
  //                   // module.size() > 50000 &&
  //                   /node_modules[/\\]/.test(module.nameForCondition() || '')
  //                 )
  //               },
  //               name(module: any) {
  //                 const packageNameArr = module.context.match(/[\\/]node_modules[\\/](.*?)([\\/]|$)/);
  //                 const packageName = packageNameArr ? packageNameArr[1] : '';
  //                 // npm package names are URL-safe, but some servers don't like @ symbols
  //                 return `chunk-lib.${packageName.replace("@", "")}`;
  //               },
  //               priority: -3,
  //               minChunks: 1,
  //               reuseExistingChunk: true,
  //             },
  //           },
  //         },
  //       },
  //     });
  //     //过滤掉momnet的那些不使用的国际化文件
  //     config
  //       .plugin('replace')
  //       .use(require('webpack').ContextReplacementPlugin)
  //       .tap(() => {
  //         return [/moment[/\\]locale$/, /zh-cn/];
  //       });
  //   } else {     
  //     // 为开发环境修改配置...      
  //     return false;
  //   }
  // },

  // chainWebpack(memo){
  //   memo.plugin('CompressionPlugin').use(new CompressionPlugin({
  //     // filename: "[path].gz[query]",
  //     algorithm: "gzip",
  //     test: productionGzipExtensions,
  //     // 只处理大于xx字节 的文件，默认：0
  //     threshold: 10240,
  //     // 示例：一个1024b大小的文件，压缩后大小为768b，minRatio : 0.75
  //     minRatio: 0.6, // 默认: 0.8
  //     // 是否删除源文件，默认: false
  //     deleteOriginalAssets: false
  //   }));
  // }
});
