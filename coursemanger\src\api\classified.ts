import HTTP from './index'

namespace classifiedApis {
    export function getClassified() {
        return HTTP.get(`/learn/v1/configure/data/get/classification/config`).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            return error
        })
    }
    export function getClassifiedCourse(data: string) {
        return HTTP.get(`/learn/v1/teaching/course/get/course/by/classification?${data}`).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            return error
        })
    }
    export function getClassifiedconfig(code: string) {
        return HTTP.get(`/learn/v1/teaching/course/get/course/config?courseclassification_code=${code}`).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            return error
        })
    }
    export function getAddConfig(data: IClassified.IgetAddConfig[]) {
        return HTTP.post(`/learn/v1/configure/data/batch/create/course/config`, data).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            return error
        })
    }
    export function getDeleteConfig(data: IClassified.IgetDeleteConfig) {
        return HTTP.post(`/learn/v1/configure/data/batch/delete/course/config/course-id`, data).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            return error
        })
    }
    export function changeOrder(course_id: string, courseclassification_code: string, order: string) {
        return HTTP.post(`/learn/v1/configure/data/update/course/config?course_id=${course_id}&courseclassification_code=${courseclassification_code}&order=${order}`).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            return error
        })
    }

}

export default classifiedApis;
