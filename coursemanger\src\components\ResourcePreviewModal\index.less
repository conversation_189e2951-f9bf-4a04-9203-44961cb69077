.entity-preview {
  position: relative;
  width: calc(100% - 300px);
  .video-wrap {
    // height: 600px;
    border: 1px solid #e6e6e6;
    // padding: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    // height: 300px;
    min-height: 173px;
    width: 100%;
    height: 100%;
    > #videoElement {
      // width: calc(100% - 350px);
      // height: 600px;
      width: 70vw !important;
      height: 600px !important;
    }
    > #previewVideo{
      width: 100%!important;
      padding-top: 56.25%!important;
    }
  }
  .doc-wrp {
    height: calc(100vh - 170px);
  }
  .ant-btn {
    margin-bottom: 15px;
  }

  
  .entity-img>img{
    // max-width: calc(100% - 350px);
    height: auto;
    max-height: calc(100vh - 100px);
    max-width: 100%;
    // max-height: 600px;
  }
}

.preview-modal {
  &.video-test-modal {
    .box #video-dom {
      height: 100%;
    }
  }
  .ant-modal-header {
    padding: 16px 24px 4px;
  }
  .box {
    display: flex;
    // #video-dom {
    //   height: 80vh;
    // }
  }
  .modal-header-wrp {
    height: 30px;
    width: 100%;
    display: flex;
    align-items: flex-start;
    &>span {
      max-width: calc(100% - 96px);
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
  .right-wrp {
    min-height: 300px;
    height: calc(calc(70vw - 348px) * 0.5625);
    border: 1px solid #e6e6e6;
    border-left: none;
    width: 300px;
    padding-bottom: 10px;
    flex-shrink: 0;
    ::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
  
    ::-webkit-scrollbar-thumb {
      background-color: #cecece;
      border-radius: 99px;
    }
    .knowledge-header {
      margin-bottom: 6px;
      border-bottom: 1px solid #e6e6e6;
      span {
        margin-left: 8px;
        border-bottom: 3px solid var(--primary-color);
        display: inline-block;
        padding-bottom: 8px;
      }
    }
    .knowledge-content {
      height: calc(100% - 44px);
      overflow: auto;
    }
    .knowledge-item {
      display: flex;
      padding: 4px 6px;
      &:hover {
        background: var(--third-color);
      }
      .item-left {
        margin-right: 12px;
        img {
          height: 72px;
          width: 128px;
          object-fit: contain;
          background: #0a2e4b;
        }
      }
      .item-right {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .item-title {
          word-break: break-all;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
        .item-btn {
          color: var(--primary-color);
          .anticon {
            margin-right: 5px;
          }
        }
      }
    }
    .ant-tabs-content-holder {
      height: calc(100% - 46px);
    }
    .ant-tabs, .ant-tabs-content, .ant-tabs-tabpane {
      height: 100%;
    }
    .ant-tabs-nav-wrap {
      padding-left: 10px;
    }
    .ant-tabs-nav {
      margin: 0;
    }
    .tp-box {
      height: 100%;
      // padding: 0 5px 5px 5px;
    }
    .btn-wrp {
      display: flex;
      justify-content: space-between;
      margin: 10px 0;
      padding: 0 5px;
      .ant-btn-sm {
        font-size: 12px;
      }
    }
    .tp-header {
      margin-top: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 5px;
    }
    .ant-checkbox-group {
      height: calc(100% - 63px);
      display: block;
    }
    .tp-list-wrp {
      padding-left: 5px;
      height: 100%;
      overflow: auto;
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
    .tp-item-wrp {
      background: #f7f8fa;
      padding: 5px;
      .tp-hd-lf {
        display: flex;
        align-items: center;
        gap: 6px;
      }
      .tp-item-header {
        display: flex;
        font-size: 12px;
        justify-content: space-between;
        align-items: center;
        .time {
          color: #aaa;
        }
        .tp-btn {
          display: flex;
          gap: 8px;
        }
      }
      .tp-content {
        overflow : hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        white-space: nowrap;
        .tp-type {
          background: var(--third-color);
          color: #000;
          padding: 2px 6px;
          font-size: 12px;
        }
        .tp-question {
          display: inline;
          p {
            display: inline;
          }
        }
      }
    }
    .disable-on {
      display: flex;
      align-items: center;
      color: #aaa;
      font-size: 12px;
      .ant-switch {
        margin-right: 5px;
      }
    }
    .version-name {
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #aaa;
      .tip {
        max-width: 130px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .tips {
      text-align: center;
      color: #aaa;
    }
  }
  .knowledge-empty, .tp-empty {
    position: relative;
    margin-top: 50px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    .text {
      color: #afafaf;
    }
  }
}