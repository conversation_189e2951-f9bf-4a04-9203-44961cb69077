/* Tiny Comments plugin
*
* Copyright 2010-2020 Tiny Technologies Inc. All rights reserved.
*
* Version: 2.3.0-117
*/
!function(){"use strict";var f=function(){return(f=Object.assign||function(n){for(var e,t=1,o=arguments.length;t<o;t++)for(var r in e=arguments[t])Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n}).apply(this,arguments)};function i(){for(var n=0,e=0,t=arguments.length;e<t;e++)n+=arguments[e].length;for(var o=Array(n),r=0,e=0;e<t;e++)for(var i=arguments[e],u=0,c=i.length;u<c;u++,r++)o[r]=i[u];return o}function a(){}function s(n){return function(){return n}}function n(n){return n}var u=s(!1),c=s(!0),e=function(){return l},l={fold:function(n,e){return n()},is:u,isSome:u,isNone:c,getOr:r,getOrThunk:o,getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:s(null),getOrUndefined:s(void 0),or:r,orThunk:o,map:e,each:a,bind:e,exists:u,forall:c,filter:e,equals:t,equals_:t,toArray:function(){return[]},toString:s("none()")};function t(n){return n.isNone()}function o(n){return n()}function r(n){return n}function m(n,e){return n=n,e=e,-1<fn.call(n,e)}function d(n,e){for(var t=n.length,o=new Array(t),r=0;r<t;r++){var i=n[r];o[r]=e(i,r)}return o}function v(n,e){for(var t=0,o=n.length;t<o;t++){e(n[t],t)}}function h(n,e){for(var t=[],o=0,r=n.length;o<r;o++){var i=n[o];e(i,o)&&t.push(i)}return t}function p(n,e){return function(n,e,t){for(var o=0,r=n.length;o<r;o++){var i=n[o];if(e(i,o))return tn.some(i);if(t(i,o))break}return tn.none()}(n,e,u)}function _(n){var e=n;return{get:function(){return e},set:function(n){e=n}}}function g(n){return parseInt(n,10)}function y(n,e){return 0==(e=n-e)?0:0<e?1:-1}function w(n,e,t){return{major:n,minor:e,patch:t}}function b(n){return(n=/([0-9]+)\.([0-9]+)\.([0-9]+)(?:(\-.+)?)/.exec(n))?w(g(n[1]),g(n[2]),g(n[3])):w(0,0,0)}function S(n,e){var t=y(n.major,e.major);return 0!==t||0!==(t=y(n.minor,e.minor))?t:0!==(e=y(n.patch,e.patch))?e:0}function T(n){return b([(n=n).majorVersion,n.minorVersion].join(".").split(".").slice(0,3).join("."))}function x(n,e){return!!n&&-1===S(T(n),b(e))}function C(n){var e=(new Date).getTime();return n+"_"+Math.floor(1e9*Math.random())+ ++ln+String(e)}function O(n,e){for(var t=mn(n),o=0,r=t.length;o<r;o++){var i=t[o];e(n[i],i)}}function D(n,t){return dn(n,function(n,e){return{k:e,v:t(n,e)}})}function E(n){return n.dom.nodeType}function A(n,e,t){if(!(rn(t)||cn(t)||sn(t)))throw console.error("Invalid call to Attribute.set. Key ",e,":: Value ",t,":: Element ",n),new Error("Attribute value was not simple");n.setAttribute(e,t+"")}function k(n,e,t){A(n.dom,e,t)}function R(n,e){n.dom.removeAttribute(e)}function M(n,e){return-1!==n.indexOf(e)}function j(n,e){var t=String(e).toLowerCase();return p(n,function(n){return n.search(t)})}function L(n){return window.matchMedia(n).matches}function U(n){return 1!==n.nodeType&&9!==n.nodeType&&11!==n.nodeType||0===n.childElementCount}function F(n){return gn.fromDom(n.dom.ownerDocument)}function N(n){return d(n.dom.childNodes,gn.fromDom)}function P(){return Pn(gn.fromDom(document))}function I(n,e,t){if(!rn(t))throw console.error("Invalid call to CSS.set. Property ",e,":: Value ",t,":: Element ",n),new Error("CSS value must be a string: "+t);var o;void 0!==(o=n).style&&an(o.style.getPropertyValue)&&n.style.setProperty(e,t)}function B(e,t){var n;(n=e,tn.from(n.dom.parentNode).map(gn.fromDom)).each(function(n){n.dom.insertBefore(t.dom,e.dom)})}function H(n,e){n.dom.appendChild(e.dom)}function V(n){n.dom.textContent="",v(N(n),function(n){In(n)})}function q(n){var e,t=N(n);0<t.length&&(e=n,v(t,function(n){B(e,n)})),In(n)}function W(n,e){var t,o=F(n).dom,r=gn.fromDom(o.createDocumentFragment()),o=function(n,e){e=(e||document).createElement("div");return e.innerHTML=n,N(gn.fromDom(e))}(e,o);t=r,v(o,function(n){H(t,n)}),V(n),H(n,r)}function Y(n,e){return function(n,e){e=void 0===e?document:e.dom;return U(e)?tn.none():tn.from(e.querySelector(n)).map(gn.fromDom)}(e,n)}function X(n){var e,t,o=Y(P(),"#"+Hn).getOrThunk(function(){var t,n,o,e=gn.fromTag("span");return n={id:Hn,"aria-live":"polite","aria-atomic":"true",role:"alert"},t=e.dom,O(n,function(n,e){A(t,e,n)}),n={position:"absolute",left:"-10000px",top:"-1000px"},o=e.dom,O(n,function(n,e){I(o,e,n)}),H(P(),e),e});return Vn++,e=n,t=o,{onComment:function(){W(t,e.translate(Bn.tc_announce_sidebar_available))},notOnComment:function(){W(t,"")},getMarker:function(){return t},release:function(){0===--Vn&&In(t)}}}var z,G,J,$,K,Q,Z,nn,en=function(t){function n(){return r}function e(n){return n(t)}var o=s(t),r={fold:function(n,e){return e(t)},is:function(n){return t===n},isSome:c,isNone:u,getOr:o,getOrThunk:o,getOrDie:o,getOrNull:o,getOrUndefined:o,or:n,orThunk:n,map:function(n){return en(n(t))},each:function(n){n(t)},bind:e,exists:e,forall:e,filter:function(n){return n(t)?r:l},toArray:function(){return[t]},toString:function(){return"some("+t+")"},equals:function(n){return n.is(t)},equals_:function(n,e){return n.fold(u,function(n){return e(t,n)})}};return r},tn={some:en,none:e,from:function(n){return null==n?l:en(n)}},on=function(t){return function(n){return n=typeof(e=n),(null===e?"null":"object"==n&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"==n&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":n)===t;var e}},e=function(e){return function(n){return typeof n===e}},rn=on("string"),un=on("object"),cn=e("boolean"),an=e("function"),sn=e("number"),fn=Array.prototype.indexOf,ln=0,mn=Object.keys,dn=function(n,t){var o={};return O(n,function(n,e){e=t(n,e);o[e.k]=e.v}),o},vn=("undefined"!=typeof window||Function("return this;")(),z=9,function(n){return E(n)===z}),on=function(e){return function(n){return n.replace(e,"")}},hn=on(/^\s+|\s+$/g),pn=on(/^\s+/g),_n=function(n){if(null==n)throw new Error("Node cannot be null or undefined");return{dom:n}},gn={fromHtml:function(n,e){e=(e||document).createElement("div");if(e.innerHTML=n,!e.hasChildNodes()||1<e.childNodes.length)throw console.error("HTML does not have a single root node",n),new Error("HTML must have a single root node");return _n(e.childNodes[0])},fromTag:function(n,e){n=(e||document).createElement(n);return _n(n)},fromText:function(n,e){n=(e||document).createTextNode(n);return _n(n)},fromDom:_n,fromPoint:function(n,e,t){return tn.from(n.dom.elementFromPoint(e,t)).map(_n)}},yn=function(){return wn(0,0)},wn=function(n,e){return{major:n,minor:e}},bn={nu:wn,detect:function(n,e){e=String(e).toLowerCase();return 0===n.length?yn():function(n,e){var t=function(n,e){for(var t=0;t<n.length;t++){var o=n[t];if(o.test(e))return o}}(n,e);if(!t)return{major:0,minor:0};n=function(n){return Number(e.replace(t,"$"+n))};return wn(n(1),n(2))}(n,e)},unknown:yn},Sn=function(n,t){return j(n,t).map(function(n){var e=bn.detect(n.versionRegexes,t);return{current:n.name,version:e}})},Tn=function(n,t){return j(n,t).map(function(n){var e=bn.detect(n.versionRegexes,t);return{current:n.name,version:e}})},e=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,on=function(e){return function(n){return M(n,e)}},e=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(n){return M(n,"edge/")&&M(n,"chrome")&&M(n,"safari")&&M(n,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,e],search:function(n){return M(n,"chrome")&&!M(n,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(n){return M(n,"msie")||M(n,"trident")}},{name:"Opera",versionRegexes:[e,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:on("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:on("firefox")},{name:"Safari",versionRegexes:[e,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(n){return(M(n,"safari")||M(n,"mobile/"))&&M(n,"applewebkit")}}],on=[{name:"Windows",search:on("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(n){return M(n,"iphone")||M(n,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:on("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:on("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:on("linux"),versionRegexes:[]},{name:"Solaris",search:on("sunos"),versionRegexes:[]},{name:"FreeBSD",search:on("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:on("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],xn={browsers:s(e),oses:s(on)},Cn="Firefox",On=function(n){var e=n.current,t=n.version,n=function(n){return function(){return e===n}};return{current:e,version:t,isEdge:n("Edge"),isChrome:n("Chrome"),isIE:n("IE"),isOpera:n("Opera"),isFirefox:n(Cn),isSafari:n("Safari")}},Dn={unknown:function(){return On({current:void 0,version:bn.unknown()})},nu:On,edge:s("Edge"),chrome:s("Chrome"),ie:s("IE"),opera:s("Opera"),firefox:s(Cn),safari:s("Safari")},En="Windows",An="Android",kn="Solaris",Rn="FreeBSD",Mn="ChromeOS",jn=function(n){var e=n.current,t=n.version,n=function(n){return function(){return e===n}};return{current:e,version:t,isWindows:n(En),isiOS:n("iOS"),isAndroid:n(An),isOSX:n("OSX"),isLinux:n("Linux"),isSolaris:n(kn),isFreeBSD:n(Rn),isChromeOS:n(Mn)}},Ln={unknown:function(){return jn({current:void 0,version:bn.unknown()})},nu:jn,windows:s(En),ios:s("iOS"),android:s(An),linux:s("Linux"),osx:s("OSX"),solaris:s(kn),freebsd:s(Rn),chromeos:s(Mn)},Un=function(n,e){var t,o,r=xn.browsers(),i=xn.oses(),u=Sn(r,n).fold(Dn.unknown,Dn.nu),c=Tn(i,n).fold(Ln.unknown,Ln.nu);return{browser:u,os:c,deviceType:(t=u,o=n,r=e,u=(i=c).isiOS()&&!0===/ipad/i.test(o),n=i.isiOS()&&!u,e=i.isiOS()||i.isAndroid(),c=e||r("(pointer:coarse)"),r=u||!n&&e&&r("(min-device-width:768px)"),e=n||e&&!r,t=t.isSafari()&&i.isiOS()&&!1===/safari/i.test(o),o=!e&&!r&&!t,{isiPad:s(u),isiPhone:s(n),isTablet:s(r),isPhone:s(e),isTouch:s(c),isAndroid:i.isAndroid,isiOS:i.isiOS,isWebView:s(t),isDesktop:s(o)})}},Fn=($=!(G=function(){return Un(navigator.userAgent,L)}),function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return $||($=!0,J=G.apply(null,n)),J}),Nn=an(Element.prototype.attachShadow)&&an(Node.prototype.getRootNode)?function(n){return gn.fromDom(n.dom.getRootNode())}:function(n){return vn(n)?n:F(n)},Pn=function(n){n=n.dom.body;if(null==n)throw new Error("Body is not available yet");return gn.fromDom(n)},In=function(n){n=n.dom;null!==n.parentNode&&n.parentNode.removeChild(n)},e=Fn().os.isOSX()?"⌘":"Ctrl",Bn=D({tc_menu_name:"TinyComments",tc_announce_sidebar_available:"Comment. Sidebar available. Press "+e+" + Alt + M to switch to sidebar",tc_items_addcomment:"Add comment",tc_items_showcomments:"Show comments",tc_items_deleteall:"Delete all conversations",tc_edit_buttons_save:"Save",tc_edit_buttons_cancel:"Cancel",tc_reply_buttons_save:"Save",tc_reply_buttons_clear:"Clear",tc_reply_placeholders:"Say something ...",tc_kebab_deleteconversation:"Delete conversation",tc_kebab_delete:"Delete",tc_kebab_edit:"Edit",tc_edit_problem_comment:"An error occurred editing this comment. See the Console for details.",tc_edit_unauthorised_comment:"You are not allowed to edit this comment",tc_delete_buttons_cancel:"Cancel",tc_delete_buttons_proceed:"Delete",tc_create_problem:"An error occurred while creating a comment. See the Console for details",tc_reply_problem:"An error occurred while replying to a comment. See the Console for details",tc_delete_prompts_conversation:"Delete this conversation?",tc_delete_prompts_conversation_detail_sing:"1 comment will be deleted. You can't undo this action.",tc_delete_prompts_conversation_detail_pl:"{0} comments will be deleted. You can't undo this action.",tc_delete_prompts_all:"Delete all conversations in the content? This cannot be undone",tc_delete_prompts_comment:"Are you sure you want to delete this comment?",tc_delete_problem_all:"An error occurred deleting all the conversations. See the Console for details.",tc_delete_problem_conversation:"An error occurred deleting the conversation. See the Console for details.",tc_delete_problem_comment:"An error occurred deleting the comment. See the Console for details.",tc_delete_unauthorised_all:"You are not allowed to delete all the conversations",tc_delete_unauthorised_conversation:"You are not allowed to delete this conversation",tc_delete_unauthorised_comment:"You are not allowed to delete this comment",tc_date_less_than_a_minute_ago:"a moment ago",tc_date_1_minute_ago:"1 minute ago",tc_date_x_minutes_ago:"{0} minutes ago",tc_date_1_hour_ago:"1 hour ago",tc_date_x_hours_ago:"{0} hours ago",tc_date_1_day_ago:"1 day ago",tc_date_x_days_ago:"{0} days ago",tc_date_1_week_ago:"1 week ago",tc_date_x_weeks_ago:"{0} weeks ago",tc_date_1_month_ago:"1 month ago",tc_date_x_months_ago:"{0} months ago",tc_date_1_year_ago:"1 year ago",tc_date_x_years_ago:"{0} years ago",tc_date_comment_edited:" (edited)",tc_comment_buttons_showmore:"SHOW MORE",tc_comment_buttons_showless:"SHOW LESS"},n),Hn=C("aria-comment-description"),Vn=0,on=function(o,r){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var t=o.console;t&&r in t&&t[r].apply(t,arguments)}},qn={log:on(window,"log"),error:on(window,"error"),warn:on(window,"warn")},e={},on={exports:e};Q=e,Z=on,nn=K=void 0,function(n){"object"==typeof Q&&void 0!==Z?Z.exports=n():"function"==typeof K&&K.amd?K([],n):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).EphoxContactWrapper=n()}(function(){return function o(r,i,u){function c(e,n){if(!i[e]){if(!r[e]){var t="function"==typeof nn&&nn;if(!n&&t)return t(e,!0);if(a)return a(e,!0);throw(t=new Error("Cannot find module '"+e+"'")).code="MODULE_NOT_FOUND",t}t=i[e]={exports:{}},r[e][0].call(t.exports,function(n){return c(r[e][1][n]||n)},t,t.exports,o,r,i,u)}return i[e].exports}for(var a="function"==typeof nn&&nn,n=0;n<u.length;n++)c(u[n]);return c}({1:[function(n,e,t){var o,r,e=e.exports={};function i(){throw new Error("setTimeout has not been defined")}function u(){throw new Error("clearTimeout has not been defined")}function c(e){if(o===setTimeout)return setTimeout(e,0);if((o===i||!o)&&setTimeout)return o=setTimeout,setTimeout(e,0);try{return o(e,0)}catch(n){try{return o.call(null,e,0)}catch(n){return o.call(this,e,0)}}}!function(){try{o="function"==typeof setTimeout?setTimeout:i}catch(n){o=i}try{r="function"==typeof clearTimeout?clearTimeout:u}catch(n){r=u}}();var a,s=[],f=!1,l=-1;function m(){f&&a&&(f=!1,a.length?s=a.concat(s):l=-1,s.length&&d())}function d(){if(!f){var n=c(m);f=!0;for(var e=s.length;e;){for(a=s,s=[];++l<e;)a&&a[l].run();l=-1,e=s.length}a=null,f=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===u||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(n){try{return r.call(null,e)}catch(n){return r.call(this,e)}}}(n)}}function v(n,e){this.fun=n,this.array=e}function h(){}e.nextTick=function(n){var e=new Array(arguments.length-1);if(1<arguments.length)for(var t=1;t<arguments.length;t++)e[t-1]=arguments[t];s.push(new v(n,e)),1!==s.length||f||c(d)},v.prototype.run=function(){this.fun.apply(null,this.array)},e.title="browser",e.browser=!0,e.env={},e.argv=[],e.version="",e.versions={},e.on=h,e.addListener=h,e.once=h,e.off=h,e.removeListener=h,e.removeAllListeners=h,e.emit=h,e.prependListener=h,e.prependOnceListener=h,e.listeners=function(n){return[]},e.binding=function(n){throw new Error("process.binding is not supported")},e.cwd=function(){return"/"},e.chdir=function(n){throw new Error("process.chdir is not supported")},e.umask=function(){return 0}},{}],2:[function(n,l,e){(function(e){function o(){}function i(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],f(n,this)}function r(t,o){for(;3===t._state;)t=t._value;0!==t._state?(t._handled=!0,i._immediateFn(function(){var n,e=1===t._state?o.onFulfilled:o.onRejected;if(null!==e){try{n=e(t._value)}catch(n){return void c(o.promise,n)}u(o.promise,n)}else(1===t._state?u:c)(o.promise,t._value)})):t._deferreds.push(o)}function u(e,n){try{if(n===e)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"==typeof n||"function"==typeof n)){var t=n.then;if(n instanceof i)return e._state=3,e._value=n,void a(e);if("function"==typeof t)return void f((o=t,r=n,function(){o.apply(r,arguments)}),e)}e._state=1,e._value=n,a(e)}catch(n){c(e,n)}var o,r}function c(n,e){n._state=2,n._value=e,a(n)}function a(n){2===n._state&&0===n._deferreds.length&&i._immediateFn(function(){n._handled||i._unhandledRejectionFn(n._value)});for(var e=0,t=n._deferreds.length;e<t;e++)r(n,n._deferreds[e]);n._deferreds=null}function s(n,e,t){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof e?e:null,this.promise=t}function f(n,e){var t=!1;try{n(function(n){t||(t=!0,u(e,n))},function(n){t||(t=!0,c(e,n))})}catch(n){if(t)return;t=!0,c(e,n)}}var n,t;n=this,t=setTimeout,i.prototype.catch=function(n){return this.then(null,n)},i.prototype.then=function(n,e){var t=new this.constructor(o);return r(this,new s(n,e,t)),t},i.all=function(n){var c=Array.prototype.slice.call(n);return new i(function(r,i){if(0===c.length)return r([]);var u=c.length;for(var n=0;n<c.length;n++)!function e(t,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var o=n.then;if("function"==typeof o)return void o.call(n,function(n){e(t,n)},i)}c[t]=n,0==--u&&r(c)}catch(n){i(n)}}(n,c[n])})},i.resolve=function(e){return e&&"object"==typeof e&&e.constructor===i?e:new i(function(n){n(e)})},i.reject=function(t){return new i(function(n,e){e(t)})},i.race=function(r){return new i(function(n,e){for(var t=0,o=r.length;t<o;t++)r[t].then(n,e)})},i._immediateFn="function"==typeof e?function(n){e(n)}:function(n){t(n,0)},i._unhandledRejectionFn=function(n){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",n)},i._setImmediateFn=function(n){i._immediateFn=n},i._setUnhandledRejectionFn=function(n){i._unhandledRejectionFn=n},void 0!==l&&l.exports?l.exports=i:n.Promise||(n.Promise=i)}).call(this,n("timers").setImmediate)},{timers:3}],3:[function(a,n,s){(function(n,e){var o=a("process/browser.js").nextTick,t=Function.prototype.apply,r=Array.prototype.slice,i={},u=0;function c(n,e){this._id=n,this._clearFn=e}s.setTimeout=function(){return new c(t.call(setTimeout,window,arguments),clearTimeout)},s.setInterval=function(){return new c(t.call(setInterval,window,arguments),clearInterval)},s.clearTimeout=s.clearInterval=function(n){n.close()},c.prototype.unref=c.prototype.ref=function(){},c.prototype.close=function(){this._clearFn.call(window,this._id)},s.enroll=function(n,e){clearTimeout(n._idleTimeoutId),n._idleTimeout=e},s.unenroll=function(n){clearTimeout(n._idleTimeoutId),n._idleTimeout=-1},s._unrefActive=s.active=function(n){clearTimeout(n._idleTimeoutId);var e=n._idleTimeout;0<=e&&(n._idleTimeoutId=setTimeout(function(){n._onTimeout&&n._onTimeout()},e))},s.setImmediate="function"==typeof n?n:function(n){var e=u++,t=!(arguments.length<2)&&r.call(arguments,1);return i[e]=!0,o(function(){i[e]&&(t?n.apply(null,t):n.call(null),s.clearImmediate(e))}),e},s.clearImmediate="function"==typeof e?e:function(n){delete i[n]}}).call(this,a("timers").setImmediate,a("timers").clearImmediate)},{"process/browser.js":1,timers:3}],4:[function(n,e,t){var o=n("promise-polyfill"),n="undefined"!=typeof window?window:Function("return this;")();e.exports={boltExport:n.Promise||o}},{"promise-polyfill":2}]},{},[4])(4)});function Wn(){function c(n,e,t){function o(t){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];r||(r=!0,null!==i&&(clearTimeout(i),i=null),t.apply(null,n))}}void 0===t&&(t=1e3);var r=!1,i=null,n=o(n),u=o(e);return{reject:u,resolve:n,start:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];r||(i=setTimeout(function(){return u.apply(null,n)},t))}}}var t={},a={};tinymce.Resource={add:function(n,e){a[n]&&(a[n](e),delete a[n]),t[n]=Xn.resolve(e)},load:function(o,r){var i='Script at URL "'+r+'" failed to load',u='Script at URL "'+r+"\" did not call `tinymce.Resource.add('"+o+"', data)` within 1 second";if(void 0!==t[o])return t[o];var n=new Xn(function(n,e){var t=c(n,e);a[o]=t.resolve,tinymce.ScriptLoader.loadScripts([r],function(){return t.start(u)},function(){return t.reject(i)})});return t[o]=n}}}var Yn,Xn=on.exports.boltExport;(e=Yn=Yn||{})[e.Modern=0]="Modern",e[e.Silver=1]="Silver";function zn(n,e){return function(n,e){e=void 0===e?document:e.dom;return U(e)?[]:d(e.querySelectorAll(n),gn.fromDom)}(e,n)}function Gn(n,e,t,o){return W(n,e),v(zn(n,t),o),n.dom.innerHTML}function Jn(o,r,i){for(var u=gn.fromTag("div"),n=0;n<o.length;n++)!function(t){o[t].content=Gn(u,o[t].content,r,i),v(o[t].fragments||[],function(n,e){o[t].fragments[e]=Gn(u,o[t].fragments[e],r,i)})}(n)}function $n(t,o,r){var i=fe();t.addCommand(he,function(n,e){t.windowManager.confirm(Bn.tc_delete_prompts_all,function(n){if(n)return r.deleteAllConversations({}).get(function(n){n.fold(function(n){return o.showError(Bn.tc_delete_problem_all,n)},function(n){var e;n.canDelete?(e=t.selection.getBookmark(),t.focus(),n=t.annotator.getAll(i),O(n,function(n,e){(0===(n=n).length?tn.none():tn.some(n[0])).each(function(n){t.selection.select(n),t.annotator.remove(i)})}),t.selection.moveToBookmark(e),de(t.undoManager.data),o.refreshSidebar(tn.none(),2),t.focus()):o.showError(Bn.tc_delete_unauthorised_all)})})})})}function Kn(n,e,u,c){n.on("init",function(){var o,r,t,i;o=e,r=c,n.addCommand(ve,function(n,e){var t=r.get();o.refreshSidebar(t,e.grabFocus?1:0)}),i=e,(t=n).addCommand("tc-delete-conversation-at-cursor",function(n,e){t.undoManager.transact(function(){return t.annotator.remove(fe())}),me(t.undoManager.data,e.conversationUid),i.refreshSidebar(tn.none(),2),t.focus()}),$n(n,e,u)})}function Qn(n,e){return n.execCommand(ve,null,{grabFocus:e})}function Zn(n){n.execCommand(he,null,{})}function ne(n){setTimeout(function(){throw n},0)}function ee(n){return xe(ye(n))}function te(t){return function(n){return Ce(function(e){return t(n,function(n){e(Te.value(n))},function(n){e(Te.error(n))})})}}function oe(e,t){return function(n){return tn.from(n.getParam(e)).getOrThunk(function(){return t(n)})}}function re(n){var o,e=Ee(n),t=Ae(n),r=Le(n),i=ke(n),u=Re(n),c=Me(n),n=je(n);return{create:te(e),reply:te(t),lookup:te((o=r,function(n,t,e){o(n,function(n){var e=d(n.conversation.comments,function(n){return f({authorName:n.author},n)});t(Ve(n,{conversation:{comments:e}}))},e)})),deleteConversation:te(i),deleteAllConversations:te(u),editComment:te(n),deleteComment:te(c),lifecycleHooks:{onSetContent:a,onGetContent:tn.none}}}function ie(u,n){return n(function(o){var r=[],i=0;0===u.length?o([]):v(u,function(n,e){var t;n.get((t=e,function(n){r[t]=n,++i>=u.length&&o(r)}))})})}var ue,ce=s("tox-comment"),ae=s("data-mce-annotation-uid"),se=s("data-mce-annotation"),fe=s("tinycomments"),le=s("tox-comments-visible"),me=function(n,e){Jn(n,(e=e,"["+ae()+'="'+e+'"]'),q)},de=function(n){Jn(n,"["+se()+'="'+fe()+'"]',q)},ve="tc-open-comment",he="tc-try-delete-all-conversations",pe=function(n){var t=tn.none(),e=[],o=function(n){r()?u(n):e.push(n)},r=function(){return t.isSome()},i=function(n){v(n,u)},u=function(e){t.each(function(n){setTimeout(function(){e(n)},0)})};return n(function(n){r()||(t=tn.some(n),i(e),e=[])}),{get:o,map:function(t){return pe(function(e){o(function(n){e(t(n))})})},isReady:r}},_e={nu:pe,pure:function(e){return pe(function(n){n(e)})}},ge=function(t){function n(n){t().then(n,ne)}return{map:function(n){return ge(function(){return t().then(n)})},bind:function(e){return ge(function(){return t().then(function(n){return e(n).toPromise()})})},anonBind:function(n){return ge(function(){return t().then(function(){return n.toPromise()})})},toLazy:function(){return _e.nu(n)},toCached:function(){var n=null;return ge(function(){return null===n&&(n=t()),n})},toPromise:t,get:n}},ye=function(n){return ge(function(){return new Xn(n)})},we=function(n){return ge(function(){return Xn.resolve(n)})},be=function(t){return{is:function(n){return t===n},isValue:c,isError:u,getOr:s(t),getOrThunk:s(t),getOrDie:s(t),or:function(n){return be(t)},orThunk:function(n){return be(t)},fold:function(n,e){return e(t)},map:function(n){return be(n(t))},mapError:function(n){return be(t)},each:function(n){n(t)},bind:function(n){return n(t)},exists:function(n){return n(t)},forall:function(n){return n(t)},toOptional:function(){return tn.some(t)}}},Se=function(t){return{is:u,isValue:u,isError:c,getOr:n,getOrThunk:function(n){return n()},getOrDie:function(){return n=String(t),function(){throw new Error(n)}();var n},or:function(n){return n},orThunk:function(n){return n()},fold:function(n,e){return n(t)},map:function(n){return Se(t)},mapError:function(n){return Se(n(t))},each:a,bind:function(n){return Se(t)},exists:u,forall:c,toOptional:tn.none}},Te={value:be,error:Se,fromOption:function(n,e){return n.fold(function(){return Se(e)},be)}},xe=function(i){return f(f({},i),{toCached:function(){return xe(i.toCached())},bindFuture:function(e){return xe(i.bind(function(n){return n.fold(function(n){return we(Te.error(n))},function(n){return e(n)})}))},bindResult:function(e){return xe(i.map(function(n){return n.bind(e)}))},mapResult:function(e){return xe(i.map(function(n){return n.map(e)}))},mapError:function(e){return xe(i.map(function(n){return n.mapError(e)}))},foldResult:function(e,t){return i.map(function(n){return n.fold(e,t)})},withTimeout:function(n,r){return xe(ye(function(e){var t=!1,o=setTimeout(function(){t=!0,e(Te.error(r()))},n);i.get(function(n){t||(clearTimeout(o),e(n))})}))}})},on=function(n){return xe(we(Te.value(n)))},Ce=ee,Oe=on,De=function(n){return xe(we(Te.error(n)))},e=function(e){return function(n){n=n.getParam(e);if(an(n))return n;throw new Error(e+" has not been implemented.")}},on=function(t,o){return function(n){var e=oe(t,function(){return o(Ue(n))})(n);return te(e)}},Ee=e("tinycomments_create"),Ae=e("tinycomments_reply"),ke=e("tinycomments_delete"),Re=e("tinycomments_delete_all"),Me=e("tinycomments_delete_comment"),je=e("tinycomments_edit_comment"),Le=e("tinycomments_lookup"),Ue=oe("tinycomments_author",s("Anon")),Fe=oe("tinycomments_author_name",Ue),Ne=on("tinycomments_can_delete",function(o){return function(n,e,t){e({canDelete:0<n.comments.length&&n.comments[0].author===o&&!0})}}),Pe=on("tinycomments_can_delete_comment",function(o){return function(n,e,t){e({canDelete:n.comment.author===o&&!0})}}),Ie=on("tinycomments_can_edit_comment",function(o){return function(n,e,t){e({canEdit:n.comment.author===o&&!0})}}),Be=oe("tinycomments_mode",function(){return"callback"}),He=Object.prototype.hasOwnProperty,Ve=(ue=function(n,e){return un(n)&&un(e)?Ve(n,e):e},function(){for(var n=new Array(arguments.length),e=0;e<n.length;e++)n[e]=arguments[e];if(0===n.length)throw new Error("Can't merge zero objects");for(var t={},o=0;o<n.length;o++){var r,i=n[o];for(r in i)He.call(i,r)&&(t[r]=ue(t[r],i[r]))}return t}),qe=function(e){return function(n){return{uid:n.uid,comments:h(n.comments,function(n){return n.uid!==e})}}},We=function(e,t,o){return function(n){return{uid:n.uid,comments:d(n.comments,function(n){return n.uid===e?f(f({},n),{content:t,modifiedAt:o}):n})}}};var Ye,Xe,ze,Ge=(Ye=function(n){return 8===E(n)||"#comment"===n.dom.nodeName.toLowerCase()},Xe="comment",{get:function(n){if(!Ye(n))throw new Error("Can only get "+Xe+" value of a "+Xe+" node");return ze(n).getOr("")},getOption:ze=function(n){return Ye(n)?tn.from(n.dom.nodeValue):tn.none()},set:function(n,e){if(!Ye(n))throw new Error("Can only set raw "+Xe+" value of a "+Xe+" node");n.dom.nodeValue=e}});var Je={base64:{versions:["2.0","2.1"],encryptor:{encryptText:function(n){var e=new Blob([n],{type:"application/json"}),t=new FileReader;return Ce(function(n){t.addEventListener("loadend",function(){return n(Te.value({encrypted:[t.result]}))}),t.addEventListener("error",function(){return n(Te.error(t.error))}),t.readAsDataURL(e)})},decryptText:function(n){return function(n){var e=n.split(",");if(!(n=/data:([^;]+)/.exec(e[0])))return tn.none();for(var n=n[1],e=e[1],t=atob(e),o=t.length,r=Math.ceil(o/1024),i=new Array(r),u=0;u<r;++u){for(var c=1024*u,a=Math.min(1024+c,o),s=new Array(a-c),f=c,l=0;f<a;++l,++f)s[l]=t[f].charCodeAt(0);i[u]=new Uint8Array(s)}return tn.some(new Blob(i,{type:n}))}(n[0]).fold(function(){return De("Could not decode URI")},function(e){var t=new FileReader;return Ce(function(n){t.addEventListener("loadend",function(){return n(Te.value({decrypted:t.result}))}),t.addEventListener("error",function(){return n(Te.error(t.error.message))}),t.readAsText(e)})})}}}};Je.current=Je.base64;function $e(n){return n=n,Ge.getOption(n).map(function(n){return e=pn(n),t=e,e=0,""===(n=n=ut)||t.length>=n.length&&t.substr(e,e+n.length)===n;var e,t}).isSome()}function Ke(n){return 2<(n=hn((n=n,Ge.get(n))).split("|")).length?Te.value({version:n[1],rest:n.slice(2)}):Te.error("Embedded comments not in expected format.")}function Qe(n){return e=$e,p(n.dom.childNodes,function(n){return e(gn.fromDom(n))}).map(gn.fromDom).fold(function(){return Oe({conversations:{},commentTag:tn.none(),encrypted:{encrypted:[]}})},function(i){return Ke(i).fold(De,function(n){var e,t,o=n.version,r=n.rest;return t=r,ot(e=o).fold(function(){return De("No Encryptor for version: "+e)},function(n){return n.decryptText(t)}).bindResult(function(n){n=n.decrypted;return function(e,n){n=ct(n);return"2.1"!==e?n.map(function(n){return D(n,function(n){return f(f({},n),{comments:d(n.comments,function(n){return it(e,"2.1",n)})})})}):n}(o,n).map(function(n){return{commentTag:tn.some(i),conversations:n,encrypted:{encrypted:r}}})})})});var e}function Ze(n){var e=gn.fromDom(document.createComment(i([ut,"2.1"],n.encrypted).join("|")));return n=e,e=gn.fromTag("div"),n=gn.fromDom(n.dom.cloneNode(!0)),H(e,n),e.dom.innerHTML}function nt(){function n(n){return Object.prototype.hasOwnProperty.call(o,n)?tn.some(o[n]):tn.none()}var o={},t=tn.none();return{get:function(){return Ve(o,{})},clear:function(){o={},t=tn.none()},addData:function(n,e){o[n]=e},setData:function(n,e){o=n,t=tn.some(e)},setEncryptedData:function(n){t=tn.some(n)},getEncrypedData:function(){return t},lookupData:n,removeData:function(n){delete o[n]},updateData:function(e,t){return n(e).map(function(n){n=t(n);return o[e]=n})}}}function et(n){function u(){return s.encryptText(JSON.stringify(c.get())).mapResult(function(n){return c.setEncryptedData(n)})}function t(t,o,n,r){return i=t,c.lookupData(i.conversationUid).fold(function(){return Te.error("Could not find conversation with uid "+i.conversationUid)},function(e){return p(e.comments,function(n){return n.uid===i.commentUid}).fold(function(){return Te.error("Could not find comment with uid "+i.commentUid+" in conversation "+i.conversationUid)},function(n){return Te.value({conversationUid:e.uid,comment:n})})}).fold(function(n){return De(n)},function(e){return n(e).bindFuture(function(n){return!0!==n[o]?Oe(((n={})[o]=!1,n)):c.updateData(t.conversationUid,r(e)).fold(function(){return De("Could not operate on comment ("+o+")")},function(n){return u().mapResult(function(){var n={};return n[o]=!0,n})})})});var i}var o=Ue(n),r=Fe(n),c=nt(),i=Ne(n),e=Pe(n),a=Ie(n),s=rt();return{create:function(n){var e=C("mce-conversation");return c.addData(e,{uid:e,comments:[{uid:e,author:o,authorName:r,content:n.content,createdAt:n.createdAt,modifiedAt:n.createdAt}]}),u().mapResult(function(){return{conversationUid:e}})},reply:function(n){var e=C("mce-reply"),t={uid:e,author:o,authorName:r,content:n.content,createdAt:n.createdAt,modifiedAt:n.createdAt};return c.updateData(n.conversationUid,function(n){return f(f({},n),{comments:n.comments.concat([t])})}).fold(function(){return De("Could not reply to uid: "+n.conversationUid)},function(){return u().mapResult(function(){return{commentUid:e}})})},lookup:function(n){return c.lookupData(n.conversationUid).fold(function(){return De("Could not find uid: "+n.conversationUid)},function(n){return Oe({conversation:n})})},deleteConversation:function(e){return c.lookupData(e.conversationUid).fold(function(){return De("Could not find conversation to delete")},function(n){return i({conversationUid:n.uid,comments:n.comments}).bindFuture(function(n){return n.canDelete?(c.removeData(e.conversationUid),u().mapResult(function(){return{canDelete:!0}})):Oe({canDelete:!1})})})},deleteAllConversations:function(n){var e=mn(c.get()),t=d(e,function(n){return c.lookupData(n).fold(function(){return De("Could not find conversation")},function(n){return i({conversationUid:n.uid,comments:n.comments})})});return Ce(function(e){ie(t,ye).get(function(n){n=function(n,e){for(var t=0,o=n.length;t<o;++t){if(!0!==e(n[t],t))return!1}return!0}(n,function(n){return n.exists(function(n){return n.canDelete})});n&&c.clear(),e(Te.value({canDelete:n}))})})},deleteComment:function(n){return t(n,"canDelete",e,function(n){return qe(n.comment.uid)})},editComment:function(e){return t(e,"canEdit",function(n){return a(f(f({},n),{edit:{modifiedAt:e.modifiedAt,content:e.content}}))},function(n){return We(e.commentUid,e.content,e.modifiedAt)})},lifecycleHooks:{onSetContent:function(n){Qe(gn.fromDom(n.getBody())).get(function(n){return n.fold(function(n){return console.error("Error extracting embedded conversations: "+n)},function(e){e.commentTag.each(function(n){c.setData(e.conversations,e.encrypted),In(n)})})})},onGetContent:function(n,e){return c.getEncrypedData().map(function(n){return e+Ze(n)})}}}}function tt(e,i,u){e.on("init",function(){e.annotator.register(fe(),{decorate:function(){return{classes:[ce()],attributes:{}}}}),e.annotator.annotationChanged(fe(),function(n,e,t){n?(u.onComment(),n=t.uid,t=t.nodes,i.refreshView(tn.some({uid:n,nodes:d(t,gn.fromDom)}))):(u.notOnComment(),i.refreshView(tn.none()))});var t,o,r,n=(t=function(){i.refreshReadonly()},o=50,r=null,{cancel:function(){null!==r&&(clearTimeout(r),r=null)},throttle:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];null!==r&&clearTimeout(r),r=setTimeout(function(){t.apply(null,n),r=null},o)}});e.on("SelectionChange",n.throttle),e.on("remove",n.cancel)})}var ot=function(e){return function(n,e){for(var t=mn(n),o=0,r=t.length;o<r;o++){var i=t[o],u=n[i];if(e(u,i,n))return tn.some(u)}return tn.none()}(Je,function(n){return m(n.versions,e)}).map(function(n){return n.encryptor})},rt=function(){return Je.current.encryptor},it=function(n,e,t){return e===n||"2.0"!==n?t:it("2.1",e,f(f({},t=t),{authorName:t.author}))},ut="tinycomments",ct=function(n){try{var e=JSON.parse(n);return Te.value(e)}catch(n){return Te.error("Could not JSON parse conversations.")}};function at(n,e){return void 0===(e=function(n,e){e=n.dom.getAttribute(e);return null===e?void 0:e}(n,e))||""===e?[]:e.split(" ")}function st(n){return void 0!==n.dom.classList}function ft(n,e){return function(n,e,t){t=at(n,e).concat([t]);return k(n,e,t.join(" ")),!0}(n,"class",e)}function lt(n,e){return o=e,0<(n=h(at(t=n,e="class"),function(n){return n!==o})).length?k(t,e,n.join(" ")):R(t,e),0;var t,o}function mt(n){0===(st(n)?n.dom.classList:at(n,"class")).length&&R(n,"class")}function dt(n){return void 0===n&&(n=gn.fromDom(document)),tn.from(n.dom.activeElement).map(gn.fromDom)}function vt(o,r){return function(n){var e,t;r.set(wt.Open),e=gn.fromDom(o.getBody()),t=le(),st(e)?e.dom.classList.add(t):ft(e,t),o.fire("mce-tinycomments-update",{}),Qn(o,!1)}}function ht(o,r){return function(n){var e,t;e=gn.fromDom(n.element()),dt(Nn(e)).filter(function(n){return e.dom.contains(n.dom)}).each(function(n){o.focus()}),r.set(wt.Closed),t=gn.fromDom(o.getBody()),n=le(),st(t)?t.dom.classList.remove(n):lt(t,n),mt(t),o.fire("mce-tinycomments-update",{})}}function pt(n,e){n===Yn.Modern?e.getContainer().querySelector(".mce-sidebar-toolbar .mce-i-conversation").click():(n="showcomments",e.execCommand("ToggleSidebar",null,n))}function _t(n,e,t){t.get()!==wt.Closed&&t.get()!==wt.Closing||pt(n,e),Qn(e,!0)}function gt(t,e,o){var n,r=(n=tinymce,r="5.0.4",!n||1!==S(T(n),b(r))?"comment":"comment-add");t.ui.registry.addButton("addcomment",{tooltip:Bn.tc_items_addcomment,icon:r,onAction:function(){_t(Yn.Silver,t,o)}}),t.ui.registry.addMenuItem("addcomment",{text:Bn.tc_items_addcomment,shortcut:"meta+Alt+M",icon:r,onAction:function(){_t(Yn.Silver,t,o)}}),t.ui.registry.addMenuItem("deleteallconversations",{text:Bn.tc_items_deleteall,onAction:function(){Zn(t)}}),t.ui.registry.addToggleMenuItem("showcomments",{text:Bn.tc_items_showcomments,icon:"comment",onAction:function(){pt(Yn.Silver,t)},onSetup:function(n){function e(){n.setActive(o.get()===wt.Open)}return t.on("mce-tinycomments-update",e),e(),function(){t.off("mce-tinycomments-update",e)}}}),t.ui.registry.addSidebar("showcomments",{tooltip:Bn.tc_items_showcomments,icon:"comment",onSetup:function(n){return e.rememberSidebar(gn.fromDom(n.element())),function(){}},onShow:vt(t,o),onHide:ht(t,o)})}function yt(e,t,o){e.addButton("showcomments",{icon:"bubble",tooltip:Bn.tc_items_showcomments,onclick:function(){pt(Yn.Modern,e)},onPostRender:function(n){e.on("mce-tinycomments-update",function(){n.control.active(o.get()===wt.Open)})}}),e.addButton("addcomment",{tooltip:Bn.tc_items_addcomment,icon:"bubble",onclick:function(){_t(Yn.Modern,e,o)}}),e.addMenuItem("showcomments",{text:Bn.tc_items_showcomments,context:"view",selectable:!0,onclick:function(){pt(Yn.Modern,e)},onPostRender:function(n){e.on("mce-tinycomments-update",function(){n.control.active(o.get()===wt.Open)})}}),e.addMenuItem("addcomment",{text:Bn.tc_items_addcomment,context:"insert",onclick:function(){_t(Yn.Modern,e,o)}}),e.addMenuItem("deleteallconversations",{text:Bn.tc_items_deleteall,context:"file",onclick:function(){Zn(e)}}),e.addSidebar("showcomments",{tooltip:Bn.tc_items_showcomments,icon:"conversation",onrender:function(n){t.rememberSidebar(gn.fromDom(n.element()))},onshow:vt(e,o),onhide:ht(e,o)})}var wt;(on=wt=wt||{})[on.Open=0]="Open",on[on.Closed=1]="Closed",on[on.Closing=2]="Closing";function bt(n,e,t){var o=e.getParam("tinycomments_css_url"),t=o?o+"/":t+"/css/";n===Yn.Modern&&e.dom.loadCSS(t+"tinymce4-content.css");var t=i([t+"tinycomments.css"],n===Yn.Modern?[t+"tinycomments-tinymce4.css",t+"tinymce4-icons.css"]:[]),r=tn.from(null===(e=e.ui)||void 0===e?void 0:e.styleSheetLoader).getOr(tinymce.DOM.styleSheetLoader);d(t,function(n){return t=n,o=r,new Xn(function(n,e){(o||tinymce.DOM.styleSheetLoader).load(t,n,e)});var t,o})}function St(o,n){var r,t,e,i=_(!1),u=_(tn.none()),c=x(tinymce,"5.0.0")?Yn.Modern:Yn.Silver,a=("embedded"===Be(v=o)?et:re)(v),s=(r=_(tn.none()),t=_(tn.none()),{rememberSidebar:function(e){t.set(tn.some(e)),r.get().fold(function(){var n=gn.fromHtml('<div aria-busy="true" class="tox-conversations" style="position: relative;">\n  <div class="tox-dialog__busy-spinner">\n  <div class="tox-loading-text">\n      <div>\n      <p>Comments are loading</p>\n      </div>\n  <div class="tox-spinner">\n      <div></div>\n      <div></div>\n      <div></div>\n  </div>\n</div></div>');H(e,n)},function(n){n.attachTo(e)})},setUi:function(e){r.set(tn.some(e)),t.get().each(function(n){V(n),e.attachTo(n),e.controller.refreshReadonly()})},refreshView:function(e){r.get().fold(function(){},function(n){n.controller.refreshView(e)})},refreshSidebar:function(e,t){r.get().fold(function(){},function(n){n.controller.refreshSidebar(e,t)})},setReadonly:function(e){r.get().each(function(n){return n.controller.setReadonly(e)})},showError:function(e,t){r.get().each(function(n){return n.controller.showError(e,t)})},refreshReadonly:function(){return r.get().map(function(n){return n.controller.refreshReadonly()}).getOr(!1)}}),f={translate:tinymce.translate};e="tinymce.plugins.tinycomments.sidebar",v=function(n,e){n=n.getParam("tinycomments_js_url");return n?n+"/":e+"/js/"}(o,n)+"tinycomments-sidebar.min.js",tinymce.Resource||Wn(),tinymce.Resource.load(e,v).then(function(n){return n(c,o,a,u,function(n){s.setUi(n),s.refreshSidebar(u.get(),5),i.set(!0)},f)});var l,m,d,v=X(f);return o.on("remove",v.release),Kn(o,s,a,u),tt(o,s,v),l=c,m=o,v=s,d=_(wt.Closed),m.shortcuts.add("meta+alt+m","TinyComments addComment",function(){_t(l,m,d)}),(l===Yn.Modern?yt:gt)(m,v,d),o.on("init",function(){bt(c,o,n),s.refreshSidebar(tn.none(),5)}),o.on("setContent",function(n){n.selection||(a.lifecycleHooks.onSetContent(o),s.refreshView(tn.none()))}),o.on("SwitchMode",function(){s.setReadonly(o.readonly)}),o.on("getContent",function(n){var e=!0===n.source_view,t=!0===n.contextual,t=e||t?tn.none():a.lifecycleHooks.onGetContent(o,n.content);n.content=t.getOr(n.content)}),{hasLoadedUi:function(){return i.get()}}}function Tt(n,e){return x(tinymce,"4.8.0")?(qn.error("The tinycomments plugin requires at least 4.8.0 version of TinyMCE."),{}):St(n,e)}tinymce.PluginManager.requireLangPack("tinycomments","ar,bg_BG,ca,cs,da,de,el,es,eu,fa,fi,fr_FR,he_IL,hr,hu_HU,id,it,ja,kk,ko_KR,nb_NO,nl,pl,pt_PT,pt_BR,ro,ru,sk,sl_SI,sv_SE,th_TH,tr,uk,zh_CN,zh_TW"),tinymce.PluginManager.add("tinycomments",Tt)}();