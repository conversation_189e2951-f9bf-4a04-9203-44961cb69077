.homeworkobejct-container {
  font-family: PingFangSC-Medium, PingFang SC;

  .header-container {
    height: 64px;
    display: flex;
    align-items: center;
    padding-left: 30px;
    border-bottom: 1px solid #D8D8D8;

    .back-btn {
      color: #333;
      font-size: 18px;
      cursor: pointer;
    }
  }

  .content-container {
    padding: 15px 30px;

    .header_boxs {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
    }

    .timu {
      color: #549cff;
      font-size: 16px;
    }

    .grade-time {
      background: #F3F7FF;
      border-radius: 25px;
      display: -ms-flexbox;
      display: flex;
      -ms-flex-pack: justify;
      justify-content: space-between;
      -ms-flex-align: center;
      align-items: center;
      font-size: 16px;
      padding: 0 18px;
      color: #333;

      .grade {
        margin-right: 10px;
      }
    }

    .table_btn {
      display: flex;
      justify-content: flex-end;
      margin: 20px 0;
    }

    .ant-tabs-tab-active {
      font-weight: 700;
    }

    .team_name {
      max-width: 100%;

      &:hover {
        cursor: pointer;
        color: var(--primary-color);
      }
    }

    .search-container {
      display: flex;
      justify-content: space-between;

      .left {
        display: flex;
        margin-bottom: 16px;
      }

      .text {
        line-height: 32px;
        color: #333333;
        font-weight: 600;
        font-size: 18px;
        margin-right: 39px;
      }

      .ant-input-search {
        width: 230px;

      }
    }


  }
}

.right {
  display: flex;
}

.team-person-popverstyle {
  .ant-popover-title {
    color: rgba(0, 0, 0, 0.85);
    border-bottom: 1px solid #f0f0f0;
    background: #f6f6f6;
    font-family: '宋体';
    padding: 6px 16px;
    font-weight: bold;
  }

  .team-person-name {
    font-family: '宋体';
    display: flex;
    flex-direction: column;
    gap: 6px;
    padding-left: 10px;
  }
}

.grade-upload-modal {
  .ant-modal-body {
    padding: 20px 50px;
  }

  .no-file-wrp {
    p {
      display: flex;

      a {
        text-decoration: underline;
      }
    }
  }

  .file-upload-wrp {
    text-align: center;

    p {
      margin-bottom: 35px;
    }
  }
}

.table_box {
  .ant-table-body {
    min-height: 200px;
  }
}
