.teaching-mgmt-bg {
  min-height: calc(100vh - 52px);
  //background: linear-gradient(135deg, #eaf1fb 0%, #f5f8fd 100%);
  padding: 40px 0;
  box-sizing: border-box;
  background: url("../../assets/imgs/homePage2/bg.png") no-repeat center center fixed;
  background-size: cover;
  display: flex;
  justify-content: center;
  align-items: center;
}

.teaching-mgmt-title {
  position: absolute;
  top: -50px;
  left: -60px;
  font-size: 18px;
  font-weight: 600;
  color: #222;
  margin-left: 60px;
  margin-bottom: 24px;
  cursor: pointer;
}

.teaching-mgmt-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  max-width: 1100px;
  margin: 0 auto;
}

.teaching-mgmt-card {
  aspect-ratio: 1/1;
  //background: #fff;
  background-color: rgba(252, 247, 247, 0.58); /* 灰 + 50% 透明 */
  border-radius: 20px;
  box-shadow: 0 4px 24px 0 rgba(0,0,0,0.04);
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-start;
  padding: 32px 24px 24px 24px;
  box-sizing: border-box;
  min-height: 160px;
  transition: box-shadow 0.2s;
  cursor: pointer;
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }
  .icon {
    //margin-bottom: 16px;
  }
  .title {
    font-size: 20px;
    font-weight: 600;
    color: #222;
    margin-bottom: 8px;
  }
  .desc {
    font-size: 14px;
    color: #999;
    line-height: 1.6;
  }
  &:hover {
    box-shadow: 0 8px 32px 0 rgba(45,140,255,0.10);
  }
}

/* 响应式适配 */
@media (max-width: 1100px) {
  .teaching-mgmt-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px 16px;
  }
  .teaching-mgmt-title {
    top: -30px;
    left: -16px;
    margin-left: 16px;
    font-size: 16px;
    margin-bottom: 16px;
    cursor: pointer;
  }
}
@media (max-width: 600px) {
  .teaching-mgmt-bg {
    padding: 16px 0;
  }
  .teaching-mgmt-title {
    top: -20px;
    left: -10px;
    margin-left: 16px;
    font-size: 14px;
    cursor: pointer;
  }
  .teaching-mgmt-grid {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 0 8px;
  }
  .teaching-mgmt-card {
    padding: 20px 12px 12px 12px;
    border-radius: 14px;
    min-height: 80px;
    .title {
      font-size: 16px;
    }
    .desc {
      font-size: 12px;
    }
  }
}
