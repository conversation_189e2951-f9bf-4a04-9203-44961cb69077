import React from 'react';
import HTTP from './index';
const PREFIX = '/learn';
const PYFA_PREFIX = '/pyfa';
export namespace GradeService {
  export function overviewPage(params: any) {
    return HTTP(`${PREFIX}/course-grades/overview/page`, {
      method: 'get',
      params,
    });
  }
  export function managePage(params: any) {
    return HTTP(`${PREFIX}/course-grades/manage/page`, {
      method: 'get',
      params,
    });
  }
  export function permission(params: any) {
    return HTTP(
      `${PREFIX}/v1/teaching/course/get/microProfession/teacher/permission`,
      {
        method: 'get',
        params,
      },
    );
  }
  export enum Role {
    teacher = '授课教师',
    dean = '教务处管理员',
    majorLeader = '专业负责管理员',
  }
  export function approval(data: any) {
    return HTTP(`${PREFIX}/course-grades/approval`, {
      method: 'post',
      data,
    });
  }
  export function downloadTemplate(params: any) {
    return HTTP(`${PREFIX}/course-grades/download/template`, {
      method: 'post',
      params,
      responseType: 'blob',
    });
  }
  export function importTemplate(data: any, params: any) {
    return HTTP(`${PREFIX}/course-grades/import`, {
      method: 'post',
      data,
      params,
    });
  }
  export function submit(data: any) {
    return HTTP(`${PREFIX}/course-grades/submit`, {
      method: 'post',
      data,
    });
  }
  export function edit(data: any) {
    return HTTP(`${PREFIX}/course-grades/edit`, {
      method: 'post',
      data,
    });
  }
  export function groupPage(params: any) {
    return HTTP(`${PREFIX}/course-group/page`, {
      method: 'get',
      params,
    });
  }
  export function groupStudentList(params: any) {
    return HTTP(`${PREFIX}/course-group/student/list`, {
      method: 'get',
      params,
    });
  }
  export function createGroup(data: any) {
    return HTTP(`${PREFIX}/course-group/create`, {
      method: 'post',
      data,
    });
  }
  export function deleteGroup(data: any) {
    return HTTP(`${PREFIX}/course-group/delete`, {
      method: 'post',
      data,
    });
  }
  export function publishGroup(data: any) {
    return HTTP(`${PREFIX}/course-group/publish`, {
      method: 'post',
      data,
    });
  }
  export function microList(params: { mapId: React.Key }) {
    return HTTP(`${PREFIX}/v1/micro/profession/teaching/module/list`, {
      method: 'get',
      params,
    });
  }
}
