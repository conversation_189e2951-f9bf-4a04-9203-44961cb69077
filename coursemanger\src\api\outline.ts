import HTTP from '@/api/index';

// 查询课程列表
export const getCourseList = (data: any) => {
    return HTTP(`/pyfa/v1/course/learn/search`, {
        method: 'POST',
        data,
        headers: {
            'Content-Type': 'application/json',
        },
    })
}

// 某课程学年版本
export const getCourseYear = (courseId: string) => {
    return HTTP.get(`/pyfa/v1/course/grades?courseId=${courseId}`)
}

export const getCourseInfo = (courseId: string, isBaseInfo: boolean) => {
    return HTTP.get(`/pyfa/v1/course?courseId=${courseId}&isBaseInfo=${isBaseInfo}`)
}

// 获取章节内容
export const getCourseContent = (id: string) => {
    return HTTP.get(`/pyfa/v1/course/teachingcontent?id=${id}`)
}

/// 
export const getCourseOutline = (id:string) => {
    return HTTP.post(`/learn/v1/teaching/binding/course/syllabus/chapter`,
        {
            "coures_code": 'd7200a6df4d54e858204d39b2da86755',
            "courseId": '2d544ac3f0984c6a8f9e94e1eabf332e',
            "courseSemester": 1,
            "syllabusContentId": id,   // 大纲id
            "syllabusResourceVOS": [
                {
                    "course_id": '2d544ac3f0984c6a8f9e94e1eabf332e',
                    "resource_id": "e07ff373f6c6433a9f83b932ee4f5986",
                    "resource_name": "测试作业",
                    "resource_show": true,
                    "resource_type": "homework"
                }
            ],
            "syllabus_module_id": "1111"
        }
    )
}

// 判断是否绑定
export const bindCourseOutline = (courseId: string, courseSemester: number) => {
    return HTTP.get(`/learn/v1/teaching/course/syllabus/coursecode/${courseId}?courseSemester=${courseSemester}`)
}

// 新增换绑课程教学大纲
export const addCourseOutline = (courseId: string, course_code: string, course_version: string) => {
    return HTTP.post(`/learn/v1/teaching/course/add/course/syllabus?courseId=${courseId}`, { "coures_code": course_code, "coures_cversion": course_version, "courseSemester": 0 })
}

/// 关联内容（绑定章节）
export const bindCourseContent = (data: any) => {
    return HTTP.post(`/learn/v1/teaching/binding/course/syllabus/chapter/content`, { data })
}

// 课时安排
export const getCourseTime = (courseId: string, id: string, course_code: string) => {
    return HTTP.post(`/learn/v1/teaching/details/course/syllabus/${courseId}`, {
        "coures_code": id,
        "coures_cversion": course_code,
    })
}

// 章节内容
export const getCourseChapter = (courseId: string) => {
    return HTTP.get(`/learn/v1/teaching/course/get/chapter/contents?courseId=${courseId}&status=2&courseSemester=1`)
}

export const getNodeInfo = (mapId: string) => {
    return HTTP.get(`/learn/m1/knowledge/course/statistics/node/info/${mapId}`).then(res => {
        if (res.status == 200) {
            return res?.data
        }
    })
}