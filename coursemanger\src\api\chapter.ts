import HTTP from './index';

interface addSectionParams {
  name: string;
  describe: string;
  parent_id: string;
}

namespace chapterApis {
  export function login() {
    return HTTP.get(`/rman/v1/account/login?loginName=admin&password=123456`)
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        return error;
      });
  }
  export function getChapter(param: any) {
    const data =
      param instanceof Object
        ? Object.keys(param)
            .map(key => `${key}=${param[key]}`)
            .join('&')
        : param;
    return HTTP.get(`/learn/v1/teaching/course/get/chapter/contents?${data}`)
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }
  export function getChapterTwo(mapId: string) {
    return HTTP.get(`/learn/m1/knowledge/course/second/node/list/${mapId}`)
  }
  //节点资源学习统计
  export function getRecourceData(params: { courseId: string,mapId: string,nodeId: string }) {
    return HTTP.get(`/learn/m1/statistics/node/resource/statistics`,{
      params
    })
  }

  export function getTree(params: { mapId: string }) {
    return HTTP.get(`/learn/m1/knowledge/course/find/nodes`, {
      params,
    });
  }
  export function getMapId(params: any) {
    return HTTP(`/learn/m1/course/get/map`, {
      method: 'POST',
      params,
    });
  }
  //添加章
  export function addChapter(data: Chapter.IaddChapterParamsItem) {
    return HTTP.post(`/learn/v1/teaching/course/chapter`, data)
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }

  //添加节 无
  // export function addSection(
  //   contentId: number,
  //   data: Chapter.IaddSectionParamsItem[],
  // ) {
  //   return HTTP.post(
  //     `/cvod/v1/teaching/course/add/section?contentId=${contentId}`,
  //     data,
  //   )
  //     .then(res => {
  //       if (res.status === 200) {
  //         return res.data;
  //       }
  //     })
  //     .catch(error => {
  //       console.error(error);
  //     });
  // }

  //添加内容
  export function addCourse(data: Chapter.IaddCourseParamsItem) {
    return HTTP.post(`/learn/v1/teaching/course/add/resource`, data)
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }

  // 统一编辑
  export function updateAny(data: Chapter.IupdateParam) {
    return HTTP.post(`/learn/v1/teaching/course/update/metadata`, data)
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }
  // 统一删除
  export function deleteAny(data: Chapter.IdeleteParam) {
    return HTTP.post(`/learn/v1/teaching/course/delete/metadata`, data)
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }
  // 统一删除 无
  // export function deleteBatch(data: Chapter.IdeleteBatchParam) {
  //   return HTTP.delete(`/cvod/v1/teaching/course/delete/many/metadata`, {
  //     data,
  //   })
  //     .then(res => {
  //       if (res.status === 200) {
  //         return res.data;
  //       }
  //     })
  //     .catch(error => {
  //       console.error(error);
  //     });
  // }

  // 统一发布
  export function publishAny(contentId: number, data: string[]) {
    return HTTP.post(
      `/learn/v1/teaching/course/publish/resource?contentId=${contentId}`,
      data,
    )
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }

  // 统一取消发布
  export function unpublishAny(contentId: number, data: string[]) {
    return HTTP.post(
      `/learn/v1/teaching/course/offshelf/resource?contentId=${contentId}`,
      data,
    )
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }

  //查询资源
  export function resourceDetail(id: string, params?: any) {
    return HTTP.get(`/rman/v1/entity/base/${id}`, {
      params: { isSysAuth: true, ...params },
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        return error;
      });
  }

  // 查询资源新的接口 防止越权访问

  export function resourceDetailNew(data: [string]) {
    return HTTP.post(`/learn/v1/course/file/info`, data)
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        return error;
      });
  }

  // 移动 无
  export function moveresource(contentId: string, data: Chapter.ImoveParams[]) {
    return HTTP.post(
      `/cvod/v1/teaching/course/move/resource?contentId=${contentId}`,
      data,
    )
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }
  // 把状态从不发布改为待发布
  export function updataChapterOne(contentId: string, data: string[]) {
    return HTTP.post(
      `/learn/v1/teaching/course/publish/resource?contentId=${contentId}`,
      data,
    )
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }
  // 把状态从待发布改为不发布
  export function updataChapterTwo(
    contentId: string,
    state: string,
    data: string[],
  ) {
    return HTTP.post(
      `/learn/v1/teaching/course/offshelf/resource/${state}?contentId=${contentId}`,
      data,
    )
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }

  // 把状态从待发布改为不发布
  export function quoteTemplate(templateId: string, courseId: string) {
    return HTTP.get(
      `/learn/v1/curriculum/center/course/quote/template?templateId=${templateId}&courseId=${courseId}`,
    )
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }

  // // 批量更新章节目录树
  // export function updataLearnTree(courseId: string, list: any[]) {
  //   return HTTP.post(`/learn/v1/teaching/course/chapter/drag`, {
  //     courseId,
  //     list,
  //   })
  //     .then(res => {
  //       if (res.status === 200) {
  //         return res.data;
  //       }
  //     })
  //     .catch(error => {
  //       console.error(error);
  //     });
  // }
  interface ITreeUpdata {
    courseId: string;
    list: any[];
    parentId: string;
    sourceId: string;
  }
  // 批量更新章节目录树
  export function updataLearnTree(data: ITreeUpdata) {
    return HTTP.post(`/learn/v1/teaching/course/chapter/drag`, data)
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }
  //仅导入章节
  export function importChapter(params: { contentId: string; courseSemester: string }, data: any) {
    return HTTP.post(
      `/learn/v1/teaching/import/catalogue`,
      data,
      { params }
    )
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }
  ///同时导入章节和资源
  export function importChapterAll(params: { contentId: string; courseSemester: string }, data: any) {
    return HTTP.post(
      `/learn/v1/teaching/import/catalogue/resources`,
      data,
      { params }
    )
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }
  //导入章节模板下载
  export function downloadChapterModal() {
    return HTTP(`/learn/v1/teaching/template/download`, {
      method: 'POST',
      responseType: 'blob',
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }
  //同时导入章节和资源模板下载
  export function downloadChapterModalAll() {
    return HTTP(`/learn/v1/teaching/template/download/resource`, {
      method: 'POST',
      responseType: 'blob',
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }
  export function updateChapterDisplay(params: any) {
    return HTTP(`/learn/v1/teaching/course/chapter/display`, {
      method: 'GET',
      params,
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }
  export function getKnowledgePoint(contentId: string) {
    return HTTP(`/rman/v1/cata/sequencemeta/select`, {
      method: 'POST',
      data: {
        metadataType: 'model_sobey_cata_sequencemeta',
        contentid: contentId,
      },
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }
  export function deleteKnowledgePoint(contentId: string, guid: string) {
    return HTTP(`/cata/sequencemeta/delete`, {
      method: 'DELETE',
      data: JSON.stringify({
        contentid: contentId,
        metadataType: 'model_sobey_cata_sequencemeta',
        guid_: guid,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }
  export function changeTemplate(data: any) {
    return HTTP(`/learn/v1/teaching/template/change`, {
      method: 'POST',
      data,
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }
  export function addVideoTopic(data: any) {
    return HTTP(`/rman/v1/question/adds`, {
      method: 'POST',
      data,
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }
  export function deleteVideoTopic(data: any) {
    return HTTP(`/rman/v1/question/single`, {
      method: 'DELETE',
      data,
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }
  export function getVideoTopicVersion(data: any) {
    return HTTP(`/rman/v1/question/version`, {
      method: 'GET',
      params: data,
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }

  export function reqCurVersion(params: any) {
    return HTTP(`/rman/v1/question/selected/version`, {
      method: 'GET',
      params,
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }
  export function getVideoTopics(data: any) {
    return HTTP(`/rman/v1/question/selected/version/bank`, {
      method: 'GET',
      params: data,
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }
  export function replaceVideoTopic(data: any) {
    return HTTP(`/rman/v1/question/replace`, {
      method: 'POST',
      data,
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }
  export function useVersion(data: any) {
    return HTTP(`/rman/v1/question/use/version`, {
      method: 'POST',
      params: data,
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }
  export function disabledTopic(data: any) {
    return HTTP(`/rman/v1/question/disable`, {
      method: 'POST',
      params: data,
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }
  export function reqTopicDisabled(params: any) {
    return HTTP(`/rman/v1/question/disable`, {
      method: 'GET',
      params,
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }

  //查询笔记重难点数量
  export function getNotePointInfo(data: any) {
    return HTTP(`/learn/edudatacenter/v1/learning-note-service/keydifficult/point`, {
      method: 'POST',
      data,
    })
    .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }

  //
  export function getKeydifficultPointInfo(params: any) {
    return HTTP(`/edudatacenter/v1/learning-note-service/keydifficult/point`, {
      method: 'GET',
      params,
    })
    .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        console.error(error);
      });
  }
}

export default chapterApis;
