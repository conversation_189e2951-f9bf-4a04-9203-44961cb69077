<?xml version="1.0" encoding="UTF-8"?>
<svg width="28px" height="28px" viewBox="0 0 28 28" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>问号备份</title>
    <defs>
        <linearGradient x1="36.9528033%" y1="0%" x2="56.2640167%" y2="100%" id="linearGradient-1">
            <stop stop-color="#F6A8FF" offset="0%"></stop>
            <stop stop-color="#F196CE" offset="100%"></stop>
        </linearGradient>
        <path d="M14,18.3749942 C14.5188888,18.4073556 14.93974,18.5823898 15.2635071,18.9026194 C15.5881356,19.2219876 15.7500038,19.6297343 15.7500038,20.1249981 C15.7500038,20.6202619 15.5872436,21.0358836 15.2635071,21.3718631 C14.9397707,21.7070121 14.5188888,21.8749942 14,21.8749942 C13.319243,21.8749942 12.8169963,21.5871337 12.4932599,21.0122585 C12.1694927,20.4364913 12.1694927,19.8616162 12.4932599,19.2858489 C12.8169963,18.7110046 13.319243,18.4073863 14,18.3749942 Z M14.0936387,6.3043697 C17.3311258,6.33325496 18.8239001,8.32212361 18.8851159,10.3486139 C18.9262444,11.7013608 18.2034976,13.0016282 16.2277642,14.1688819 C15.8917539,14.3675103 15.6668857,14.5818888 15.5216288,14.8102639 C15.322139,15.1226184 15.2739969,15.4463856 15.3098651,15.8103891 C15.3137016,15.8385106 15.3405037,16.0705949 15.3405037,16.1586119 C15.3427812,16.6290657 15.0931203,17.0647323 14.6860973,17.3006037 C14.2790743,17.5364752 13.7769429,17.5364752 13.3699198,17.3006037 C12.9628968,17.0647323 12.7132359,16.6290657 12.7155134,16.1586426 L12.715478,16.1620071 L12.7155083,16.2061637 C12.715217,16.2255143 12.7137807,16.2171561 12.707784,16.1627963 L12.7023781,16.1131153 C12.604402,15.2022605 12.7303713,14.3062637 13.3069998,13.4006385 C13.6779862,12.8178884 14.2064727,12.3138883 14.8933821,11.9087563 C15.5881049,11.4983949 15.9809936,11.120364 16.1621187,10.7948742 C16.2326588,10.6853514 16.2675119,10.5566541 16.261879,10.4265025 C16.2391153,9.66961027 15.6939867,8.94338744 14.0691216,8.92849876 C12.5877599,8.91539427 11.9656348,9.37300571 11.7739892,10.4842423 C11.6365045,11.1832877 10.9664605,11.6450117 10.2642767,11.5245779 C9.56209283,11.4041441 9.08420097,10.745534 9.1875125,10.0406275 C9.60836367,7.5862416 11.387222,6.27899128 14.0936387,6.3043697 Z" id="path-2"></path>
        <filter x="-72.1%" y="-32.1%" width="244.1%" height="189.9%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.898039216   0 0 0 0 0.588235294   0 0 0 0 0.945098039  0 0 0 0.6 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="切图" transform="translate(-222.000000, -179.000000)" fill-rule="nonzero">
            <g id="问号备份" transform="translate(222.000000, 179.000000)">
                <path d="M14,28 C6.26760944,28 0,21.7323906 0,14 C0,6.26760944 6.26760944,0 14,0 C21.7323906,0 28,6.26760944 28,14 C28,21.7323906 21.7323906,28 14,28 Z" id="路径" fill="#FCE8FF"></path>
                <g id="形状结合">
                    <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                    <use fill="url(#linearGradient-1)" xlink:href="#path-2"></use>
                </g>
            </g>
        </g>
    </g>
</svg>