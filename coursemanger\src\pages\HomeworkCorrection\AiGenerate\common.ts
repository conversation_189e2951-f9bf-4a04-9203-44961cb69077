/**
 * 获取一个递归函数,处理流式数据
 * @param reader  流数据
 * @param stream  是否是流式数据
 */
export const getWrite = (reader: any, stream: boolean, callback: (content: any, config: any, finish: boolean, newChatInfo?: any) => void) => {
    let tempResult = '';

    const write_json = ({ done, value }: { done: boolean; value: any }) => {
        if (done) {
            // 测试
            // let timer = setTimeout(() => {
            //     let data = {
            //         "score": "2.0",
            //         "comment": "一、知识点理解与运用（本题总分50%，本部分应分配2.5分）：学生仅简单地将马克思关于人的本质的论断理解为“人不能独居，要有朋友”，对知识点的理解非常片面和肤浅，没有准确把握“人的本质是一切社会关系的总和”中社会关系丰富内涵这一关键要点，仅得0.5分。\n二、答题逻辑（本题总分50%，本部分应分配2.5分）：学生的回答没有体现出应有的逻辑结构，只是简单陈述了一个简单的观点，没有结合当代社会现象进行分析，逻辑混乱且不完整，得0分。综合两部分，本题最终得分为2.0分。建议学生深入研读马克思主义经典著作，准确理解重要论断的内涵，并学会结合实际社会现象进行分析论述。"
            //     }
            //     callback(data, '', true);
            //     // callback({ text: 'shibai' }, '', false)
            // }, 500)
            // return () => {
            //     clearTimeout(timer)
            // }
            // return
            const result_block = JSON.parse(tempResult)
            console.log(result_block, 'result_block')
            if (result_block.status == 200) {
                callback(result_block.data, '', true);
            } else {
                callback(result_block.message, '', false)
            }
            return
        }
        if (value) {
            const decoder = new TextDecoder('utf-8')
            tempResult += decoder.decode(value)
        }
        return reader.read().then(write_json)
    }
    return write_json
}
