import HTTP from './index';

function reqRecycleList(params: {
  page: number;
  size: number;
  template: 0 | 1;
}) {
  return HTTP.get(`/learn/v1/course/recycle/list`, { params })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
function clearRecycle(template: 0 | 1) {
  return HTTP.get(`/learn/v1/course/recycle/clear`, { params: { template } })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

function deleteCourse(
  ids: string[],
  params: {
    type: 0 | 1; // 0 逻辑删除 1彻底删除
  },
) {
  return HTTP.post(`/learn/v1/course/recycle/course/delete`, ids, { params })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

function restoreCourse(data: string[]) {
  return HTTP.post(`/learn/v1/course/recycle/course/restore`, data)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export default {
  reqRecycleList,
  clearRecycle,
  deleteCourse,
  restoreCourse,
};
