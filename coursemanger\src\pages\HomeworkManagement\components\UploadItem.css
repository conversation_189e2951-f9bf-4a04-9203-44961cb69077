.upload-item {
  line-height: 32px;
  display: flex;
}
.upload-item > div {
  display: flex;
  align-items: center;
}
.upload-item .file-size {
  color: #aaa;
}
.upload-item .annotate-wrp {
  height: 26px;
  background-color: var(--third-color);
  color: var(--primary-color);
  font-size: 12px;
  padding: 0 6px;
  border-radius: 4px;
  line-height: 26px;
  margin-right: 5px;
}
.upload-item .anticon-close {
  color: var(--primary-color);
  margin-left: 10px;
  cursor: pointer;
}
.upload-item .anticon-paper-clip {
  margin-right: 3px;
}
.upload-item .title .required::before {
  display: inline-block;
  margin-right: 4px;
  color: #ff4d4f;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: "*";
}
.upload-item .uploading {
  margin-left: 10px;
}
.upload-item .uploading .anticon-spin {
  margin-right: 3px;
}
.upload-item .upload-progress {
  display: flex;
  flex-direction: column;
  width: 200px;
}
.upload-item .upload-progress .ant-progress {
  margin-bottom: 5px;
}
.upload-item .upload-progress .uploading {
  margin-left: 0;
  font-size: 12px;
}
