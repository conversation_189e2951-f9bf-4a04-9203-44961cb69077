import Http from './index';

// 查询教参教辅
export const reqAllBooks = (data: any, params: any) =>
  Http.post('/rman/v1/textbook/search', data, { params })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 查询分词
export const reqAnalysis = (params: any) =>
  Http.get('/rman/v1/textbook/field/analysis', { params })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 添加到当前课程
export const addBookToCourse = (params: any) =>
  Http.post('/rman/v1/textbook/relate/course', null, { params })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 查询当前课程加入的教参教辅
export const reqCourseBook = (params: any) =>
  Http.get('/rman/v1/textbook/relate/course', { params })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 用户添加到书架
export const addToBookshelf = (data: any) =>
  Http.post('/rman/v1/textbook/relate/user', data)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

//移除教师推荐
export const removeBook = (params: any) =>
  Http.delete('/rman/v1/textbook/relate/course', { params })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
export const reqAllMaterials = (params: any) =>
  Http.get('/learn/v1/course/obtain/publishing/house/data', { params })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
