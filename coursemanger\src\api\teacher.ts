/*
 * @Author: 李武林
 * @Date: 2021-12-03 12:18:28
 * @LastEditors: 李武林
 * @LastEditTime: 2022-03-21 20:44:49
 * @FilePath: \coursemanger\src\api\teacher.ts
 * @Description: 
 * 
 * Copyright (c) 2022 by 李武林/索贝数码科技股份有限公司, All Rights Reserved. 
 */

import HTTP from './index'
// export function getfields() {
//     //获取元数据
//     return HTTP.get(`/rman/v1/metadata/resource/basic/data`).then(res => {
//         if (res.status === 200) {
//             return res.data
//         }
//     }).catch(error => {
//         console.error(error)
//     })
// }
// 获取列表
export function getteacherlist(param: any) {
    // return HTTP.get(`/cvod/v1/teaching/course/get/teacher/list?id=${code}`).then(res => {
    return HTTP.get(`/learn/v1/teaching/course/get/teacher/list`,{
        params: param
    }).then(res => {
        if (res.status === 200) {
            return res.data
        }
    }).catch(error => {
        console.error(error)
    })
}
// 添加
export function addteacher(code: string, data: Array<ITeacher.IaddTeacherParams>) {
    // return HTTP.post(`/cvod/v1/teaching/course/add/teacher?typeId=${code}`, data).then(res => {
    return HTTP.post(`/learn/v1/teaching/course/add/teacher?typeId=${code}`, data).then(res => {
        if (res.status === 200) {
            return res.data
        }
    }).catch(error => {
        console.error(error)
    })
}
// 编辑
export function updateteacher(code: string, data: ITeacher.IupdataTeacherParams) {
    // return HTTP.patch(`/cvod/v1/teaching/course/update/teacher?typeId=${code}`, data).then(res => {
    return HTTP.post(`/learn/v1/teaching/course/update/teacher?typeId=${code}`, data).then(res => {
        if (res.status === 200) {
            return res.data
        }
    }).catch(error => {
        console.error(error)
    })
}
// 删除
export function deleteteacher(code: string, data: Array<string>) {
    // return HTTP.delete(`/cvod/v1/teaching/course/delete/teacher?typeId=${code}`, {
    return HTTP.post(`/learn/v1/teaching/course/delete/teacher?typeId=${code}`, {
        data
    }).then(res => {
        if (res.status === 200) {
            return res.data
        }
    }).catch(error => {
        console.error(error)
    })
}
// 获取全部用户信息
export function userOriginal(data: ITeacher.IsearchTeacher) {
    return HTTP.post(`/cvod/v1/teaching/course/original/list`, data).then(res => {
        if (res.status === 200) {
            return res.data
        }
    }).catch(error => {
        console.error(error)
    })
}

// 更改课程负责人
export function changeCharge(data:any) {
    return HTTP.post(`/learn/v1/teaching/change/responsible`, data).then(res => {
        if (res.status === 200) {
            return res.data
        }
    }).catch(error => {
        console.error(error)
    })
}

export function reqModules(params:any) {
  return HTTP.get(`/learn/v1/micro/profession/teaching/module/list`, { params }).then(res => {
      if (res.status === 200) {
          return res.data
      }
  }).catch(error => {
      console.error(error)
  })
}