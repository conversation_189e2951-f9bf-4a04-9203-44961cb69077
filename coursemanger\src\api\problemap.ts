import HTTP from './index';

export const bindmap = (data: any) =>
    HTTP(`/learn/m1/knowledge/question/map/bind`, {
      method: 'GET',
      params:data
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        return error;
      });
  
  /**
   *  excel导入问题图谱
   * @param params
   */
  export const importProblemMap = (mapId: string, data: any) =>
    HTTP(`/learn/m1/knowledge/course/question/upload/${mapId}`, {
      method: 'POST',
      data: data,
    })
      .then(res => {
        if (res.status === 200) {
          return res.data;
        }
      })
      .catch(error => {
        return error;
      });