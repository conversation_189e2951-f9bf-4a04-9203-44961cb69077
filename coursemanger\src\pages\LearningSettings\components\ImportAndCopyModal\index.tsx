import chapterApis from '@/api/chapter';
import {
  getImportCourseResourceStatus,
  reqImportCourse,
  reqImportCourseResource,
} from '@/api/learnSet';
import { getSpocCourse } from '@/api/mooclist';
import useSemester from '@/hooks/semester';
import useLocale from '@/hooks/useLocale';
import usePermission from '@/hooks/usePermission';
import {
  Button,
  Checkbox,
  Col,
  Modal,
  Radio,
  RadioChangeEvent,
  Row,
  Select,
  message,
} from 'antd';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';
import React, { FC, useEffect, useState } from 'react';
import { useHistory, useSelector } from 'umi';
import './index.less';
import { debounce, reject } from 'lodash';
import { useImmer } from 'use-immer';

const enCourseTypes = {
  spoc: 2,
  mooc: 1,
  training: 3,
};

const courseOptions = [
  '课程信息设置',
  '教学团队',
  '课程学习设置',
  '公告',
  '章节内容',
  '课程地图',
  '课程资料',
]; // , "课程资料"

interface IProps {
  open: boolean;
  isImport: boolean;
  onOk: () => void;
  onClose: () => void;
}

const ImportAndCopyModal: FC<IProps> = ({ open, isImport, onClose, onOk }) => {
  const [currentStep, setCurrentStep] = useState<number>(1);
  const [indeterminate, setIndeterminate] = useState(true);
  const [checkAll, setCheckAll] = useState(false);
  const { t } = useLocale();
  const { getPermission } = usePermission();
  const history: any = useHistory();
  const queryData = history.location.query;
  const courseDetail = useSelector<Models.Store, any>(
    state => state.moocCourse.courseDetail,
  );
  const [checkedList, setCheckedList] = useState<CheckboxValueType[]>([]);
  const { parameterConfig } = useSelector<{ global: any }, any>(
    state => state.global,
  );
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [page, setPage] = useState<number>(1);
  const [searchValue, setSearchValue] = useState<string>('');
  const [courseType, setCourseType] = useState<number>(1); // 课程类型
  const [courseList, setCourseList] = useImmer<any>({});
  const [loading, setLoading] = useState<boolean>(false);
  const courseTypes = [t('公开课'), t('班级课'), t('培训课')];

  // 章节内容添加或覆盖
  const [addOrCover, setAddOrCover] = useState<number>();
  const [addOrCoverByResource, setAddOrCoverByResource] = useState<number>();
  // 选择的内容
  const [typeList, setTypeList] = useState<number[]>([]);
  // // 选中课程类型
  // const [selectCourseType, setSelectCourseType] = useState<number>();
  // // 选中课程id
  // const [selectCourseId, setSelectCourseId] = useState<string>();
  // // 选中课程期数
  // const [period, setPeriod] = useState<number>();
  // 章节id
  const [chapterId, setChapterId] = useState<string>();
  const [courseKeys, setCourseKeys] = useState<string[]>([]);
  // 多选班级课
  // const [copyCourseList, setCopyCourseList] = useState<any[]>([]);
  // const plainOptions02 = spocList.filter(item => item.show).map(item => item.name);

  // const { semesterList, getSemesters } = useSemester(true);
  // const [semester, setSemester] = useState<number | null>(null);

  const onChange = (e: RadioChangeEvent) => {
    setCourseType(e.target.value);
    setCourseKeys([]);
    // setSemester(null);
  };
  const handleChange = (value: string[] | string) => {
    if (typeof value === 'string') {
      // setSemester(null);
      // getSemesters(value);
      setCourseKeys([value]);
    } else {
      setCourseKeys(value);
    }
  };
  // 添加或覆盖
  const handleChangeAdd = (value: number) => {
    setAddOrCover(value);
  };
  const onCheckAllChange = (e: CheckboxChangeEvent) => {
    setCheckedList(
      e.target.checked ? courseOptions.map((_, index) => index) : [],
    );
    setIndeterminate(false);
    setCheckAll(e.target.checked);
  };
  const onChange02 = (list: CheckboxValueType[]) => {
    setCheckedList(list);
    setIndeterminate(!!list.length && list.length < courseOptions.length);
    setCheckAll(list.length === courseOptions.length);
  };
  const handlePopupScroll = (e: any) => {
    const target = e.target;
    if (
      target.scrollTop + target.offsetHeight === target.scrollHeight &&
      hasMore
    ) {
      setLoadingMore(true);
      loadData(page, false, searchValue);
    }
  };
  const loadData = (
    pageIndex: number,
    isFirst?: boolean,
    searchValue?: string,
  ) => {
    getSpocCourse({
      pageIndex,
      pageSize: 20,
      courseType,
      courseName: searchValue ?? '',
    })
      .then((res: any) => {
        if (res.status === 200) {
          const list_ = res.data.results?.map((item: any) => {
            const { entityData } = item;
            return {
              // label: entityData.name,
              label: entityData.description_
                ? `${entityData.name} (${entityData.description_})`
                : entityData.name,
              value: entityData.contentId_,
              courseType: entityData.courseType,
              course_semester_id: entityData.course_semester_id,
            };
          });
          const list = isFirst
            ? list_
            : [...(courseList[courseType] ?? []), ...list_];
          setCourseList((obj: any) => {
            obj[courseType] = list ?? [];
          });
          setPage(pageIndex + 1);
          setHasMore(res.data.total > list.length);
        }
      })
      .finally(() => {
        setLoadingMore(false);
      });
  };
  const handleSearch = debounce((value: string) => {
    loadData(1, true, value);
    setSearchValue(value);
  }, 500);
  useEffect(() => {
    loadData(1, true);
  }, [courseType]);

  const handleOk = () => {
    setCurrentStep(2);
  };
  const handleCancel = () => {
    onClose();
    setCourseType(1);
    setCurrentStep(1);
    setCheckAll(false);
    setIndeterminate(false);
    setCourseKeys([]);
    setCheckedList([]);
    setAddOrCover(undefined);
    setAddOrCoverByResource(undefined);
  };
  const handleSubmit = () => {
    setLoading(true);
    const updateResource = () => {
      const tmp = courseList[courseType];
      const selected = tmp.filter((item: any) =>
        courseKeys.includes(item.value),
      );

      let params = {};
      if (isImport) {
        params = {
          sourceCourseId: selected[0].value,
          copyMode: addOrCoverByResource,
          targetCourses: [
            {
              courseType: queryData.courseType,
              targetCourseId: queryData.id,
              targetCourseSemester: courseDetail.entityData.course_semester_id,
            },
          ],
        };
      } else {
        params = {
          sourceCourseId: queryData.id,
          copyMode: addOrCoverByResource,
          targetCourses: selected.map((item: any) => ({
            courseType,
            targetCourseId: item.value,
            targetCourseSemester: item.course_semester_id,
          })),
        };
      }

      return reqImportCourseResource(params).then(res => {
        if (res.success) {
          return getImportStatus(res.data, 3000);
        }
      });
    };
    const updateCourse = () => {
      const params = {
        courseSemester: queryData.sm,
        courseType: enCourseTypes[queryData.type as keyof typeof enCourseTypes],
        isCopyOrImport: isImport ? 1 : 0,
        sourceCourseId: queryData.id,
        targetCourseList: courseKeys.map((item: string) => ({
          courseType,
          targetCourseId: item,
        })),
        copyTypeList: checkedList.map(item =>
          item === 4
            ? { type: item, operateType: addOrCover }
            : { type: item === 5 ? 6 : item, operateType: 0 },
        ),
      };
      return reqImportCourse(params);
    };
    const getImportStatus = (taskId: string, delay = 8000) => {
      return getImportCourseResourceStatus({ taskId }).then((res: any) => {
        if (res.success) {
          const { data } = res;
          if (data.endTime) {
            if (data.isCompleted) {
              message.success(
                `导入课程资料${data.total}个，成功导入${data.success}${
                  data.fail ? `，失败${data.fail}个` : ''
                }`,
              );
            } else {
              message.error(data.msg);
            }
            return res;
          } else {
            return new Promise((resolve, reject) => {
              setTimeout(() => {
                try {
                  resolve(getImportStatus(taskId));
                } catch (err) {
                  reject(err);
                }
              }, delay);
            });
          }
        }
      });
    };
    if (checkedList.includes(6)) {
      Promise.all([updateResource(), updateCourse()])
        .then(values => {
          console.log('promiseall', values);
          if (values.every(res => res.status === 200 || res.success)) {
            onOk();
            handleCancel();
          }
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      updateCourse()
        .then(res => {
          if (res.status === 200) {
            onOk();
            handleCancel();
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  };
  const confirm = () => {
    if (checkedList.length === 0) {
      message.warning('请选择要导入的内容！');
      return;
    }
    if (checkedList.includes(4) && addOrCover == null) {
      message.warning('请选择添加或覆盖章节内容！');
      return;
    }
    if (checkedList.includes(6) && addOrCoverByResource == null) {
      message.warning('请选择添加或覆盖课程资料！');
      return;
    }
    if (addOrCover === 0) {
      Modal.confirm({
        content: (
          <p>
            您选择了<strong>覆盖</strong>章节内容，若
            {`${isImport ? '当前课程' : '目标课程'}`}
            有已下发的作业且已有学生提交，会导致记录被清除，是否确认覆盖？
          </p>
        ),
        onOk() {
          handleSubmit();
        },
      });
    } else {
      handleSubmit();
    }
  };
  return (
    <Modal
      wrapClassName="import-copy-modal"
      title={<div>{isImport ? t('课程内容导入') : t('课程内容复制')}</div>}
      open={open}
      onCancel={handleCancel}
      footer={[
        <div className="flex-a-c">
          {currentStep === 1 ? (
            <Button
              disabled={!courseKeys || courseKeys?.length === 0}
              type="primary"
              onClick={handleOk}
            >
              下一步
            </Button>
          ) : (
            <div className="flex-a-c">
              <Button
                type="primary"
                disabled={loading}
                onClick={() => setCurrentStep(1)}
              >
                上一步
              </Button>
              <Button type="primary" loading={loading} onClick={confirm}>
                确认
              </Button>
            </div>
          )}
        </div>,
      ]}
    >
      {currentStep === 1 ? (
        <div>
          <p>步骤1：请选择{isImport ? '导入' : '要复制'}的课程</p>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              flexDirection: 'column',
            }}
          >
            <Radio.Group onChange={onChange} value={courseType}>
              {courseTypes.map((item, index) => (
                <Radio value={index + 1}>{item}</Radio>
              ))}
            </Radio.Group>
            <Select
              value={courseKeys}
              mode={isImport ? undefined : 'multiple'}
              allowClear
              showSearch
              filterOption={false}
              onSearch={handleSearch}
              style={{ width: 220, marginTop: '10px' }}
              options={courseList[courseType]}
              placeholder={`请选择${courseTypes[courseType - 1]}`}
              onChange={handleChange}
              onPopupScroll={handlePopupScroll}
            ></Select>
            {/* {courseType === 1 && courseKeys.length > 0 && <Select
          style={{ width: 120 }}
          placeholder={t(`请选择期次`)}
          value={semester}
          onChange={(value: any) => {
            setSemester(value);
          }}>

          {semesterList.map((item: any) => <Select.Option value={item.courseSemesterId} key={item.courseSemesterId}>{item.courseSemesterName}</Select.Option>)}
        </Select>} */}
          </div>
        </div>
      ) : currentStep === 2 ? (
        <div>
          <p>步骤2：请选择要{isImport ? '导入' : '复制'}的内容</p>
          <Checkbox
            indeterminate={indeterminate}
            onChange={onCheckAllChange}
            checked={checkAll}
            style={{ marginBottom: '10px' }}
          >
            全选
          </Checkbox>
          <Checkbox.Group
            style={{ width: '100%' }}
            value={checkedList}
            onChange={onChange02}
          >
            <Row gutter={[0, 15]}>
              {courseOptions.map((item: string, index: number) => (
                <>
                  {![4, 6].includes(index) && (
                    <Col span={8}>
                      <Checkbox value={index}>{item}</Checkbox>
                    </Col>
                  )}
                  {[4].includes(index) && (
                    <div className="flex-a-c">
                      <Checkbox value={index}>{item}</Checkbox>
                      <Select
                        value={addOrCover}
                        placeholder="请选择"
                        style={{ width: 160, height: 22, marginTop: -10 }}
                        onChange={handleChangeAdd}
                        options={[
                          {
                            value: 1,
                            label: '添加',
                          },
                          {
                            value: 0,
                            label: '覆盖',
                          },
                        ]}
                      />
                      <div>(必选)</div>
                    </div>
                  )}
                  {index === 6 && (
                    <div className="flex-a-c">
                      <Checkbox value={index}>{item}</Checkbox>
                      <Select
                        value={addOrCoverByResource}
                        placeholder="请选择"
                        style={{ width: 160, height: 22, marginTop: -10 }}
                        onChange={value => setAddOrCoverByResource(value)}
                        options={[
                          {
                            value: 0,
                            label: '添加(已有资源自动跳过)',
                          },
                          {
                            value: 1,
                            label: '覆盖(已有资源重新复制)',
                          },
                          {
                            value: 2,
                            label: '强制覆盖(移除所有资源再复制)',
                          },
                        ]}
                      />
                      <div>(必选)</div>
                    </div>
                  )}
                </>
              ))}
            </Row>
          </Checkbox.Group>
        </div>
      ) : (
        <div></div>
      )}
    </Modal>
  );
};

export default ImportAndCopyModal;
