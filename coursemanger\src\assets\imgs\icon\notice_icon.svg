<?xml version="1.0" encoding="UTF-8"?>
<svg width="68px" height="68px" viewBox="0 0 68 68" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>公告</title>
    <defs>
        <linearGradient x1="11.5850091%" y1="7.74720245%" x2="128.346621%" y2="131.061142%" id="linearGradient-1">
            <stop stop-color="#B4B9FF" offset="0%"></stop>
            <stop stop-color="#8D69F0" offset="100%"></stop>
        </linearGradient>
        <path d="M54,28.9285714 C53.8573975,27.5714286 52.7165775,26.5714286 51.3618538,26.5714286 L43.5187166,26.5714286 L41.0231729,24.0714286 L37.1016043,20.1428572 C37.600713,19.5 37.8146168,18.7142858 37.8146167,17.8571429 C37.8146167,15.7142857 36.174688,14 34.0356506,14 C31.9679144,14 30.2566845,15.7142857 30.2566845,17.7857143 L30.2566845,18.1428572 C30.3279858,18.8571429 30.5418895,19.5 30.9696969,20.0714286 L24.5525846,26.5 L16.7094474,26.5 C15.2121212,26.5 14,27.7142857 14,29.2142857 L14,51.3571428 C14.0713013,52.7857143 15.2834225,54 16.7807487,54 L51.3618538,54 C52.85918,54 54,52.7857143 54,51.2857143 L54,28.9285714 Z M28.5454545,25.5714286 L32.7522282,21.3571429 C33.6078431,21.7142857 34.5347594,21.7142857 35.4616756,21.3571429 L36.6024955,22.5 L40.5953654,26.5 L27.6185383,26.5 L28.5454545,25.5714286 Z M19.9180036,33.8571429 L33.2513369,33.8571429 C34.0356506,33.8571429 34.6773618,34.5 34.6773618,35.2857143 C34.6773618,36.0714286 34.0356506,36.7142857 33.2513369,36.7142857 L19.9180036,36.7142857 C19.1336898,36.7142857 18.4919786,36.0714286 18.4919786,35.2857143 C18.4919786,34.5 19.1336898,33.8571429 19.9180036,33.8571429 L19.9180036,33.8571429 Z M37.7433155,46.6428571 L19.9180036,46.6428571 C19.1336898,46.6428571 18.4919786,46 18.4919786,45.2142857 C18.4919786,44.4285714 19.1336898,43.7857143 19.9180036,43.7857143 L37.8146167,43.7857143 C38.5989304,43.7857143 39.2406417,44.4285714 39.2406417,45.2142857 C39.2406417,46 38.5276292,46.6428571 37.7433155,46.6428571 L37.7433155,46.6428571 Z M47.1550802,41.7142857 L19.9180036,41.7142857 C19.1336898,41.7142857 18.4919786,41.0714286 18.4919786,40.2857143 C18.4919786,39.5 19.1336898,38.8571428 19.9180036,38.8571428 L47.1550802,38.8571428 C47.9393939,38.8571428 48.5811052,39.5 48.5811052,40.2857143 C48.5811052,41.0714286 47.9393939,41.7142857 47.1550802,41.7142857 Z" id="path-2"></path>
        <filter x="-32.5%" y="-27.5%" width="165.0%" height="165.0%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="4" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.619607843   0 0 0 0 0.607843137   0 0 0 0 0.988235294  0 0 0 0.6 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="切图" transform="translate(-356.000000, -81.000000)">
            <g id="公告" transform="translate(356.000000, 81.000000)">
                <rect id="矩形" fill="#EFF0FF" x="0" y="0" width="68" height="68" rx="18"></rect>
                <g id="形状" fill-rule="nonzero">
                    <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                    <use fill="url(#linearGradient-1)" xlink:href="#path-2"></use>
                </g>
            </g>
        </g>
    </g>
</svg>