import HTTP from './index';

// 获取微专业列表
export const getmicromajorlist = (data: any) =>
  HTTP(`/learn/v1/micro/profession/page`, {
    method: 'GET',
    params: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

/**
 * 新建微专业
 * @param params
 */
export const addmicromajor = (data: any) =>
  HTTP(`/learn/v1/micro/profession/save`, {
    method: 'POST',
    data: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

/**
 * 删除微专业
 * @param params
 */
export const deletemicromajor = (data: any) =>
  HTTP(`/learn/v1/micro/profession/delete`, {
    method: 'POST',
    data: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

/**
 * 修改微专业
 * @param params
 */
export const updatemicromajor = (data: any) =>
  HTTP(`/learn/v1/micro/profession/update`, {
    method: 'POST',
    data: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });


// 获取教学模块列表
export const getmodulelist = (data: any) =>
  HTTP(`/learn/m1/knowledge/query/teaching/module/page`, {
    method: 'GET',
    params: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

/**
 * 根据课程id或者模块id查询下面的作业列表
 */
export const getHomework = (courseId: string, state?: string, chapterId?: string, courseSemester?: number,) => {
  return HTTP(`/exam-api/microSpecial/map-info`, {
    method: 'GET',
    params: { courseId, chapterId, courseSemester, state }
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  }).catch(error => {
    return error;
  });
}

/**
 * 微专业预创建作业
 */
export const createMicroHomework = (data: any) => {
  return HTTP(`/exam-api/resource/homework/pre`, {
    method: 'POST',
    data
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  }).catch(error => {
    return error;
  });
}


/**
 * 微专业更新作业
 */
export const updateMicroHomework = (data: any) => {
  return HTTP(`/exam-api/microSpecial/update`, {
    method: 'POST',
    data
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  }).catch(error => {
    return error;
  });
}

/**
 * 微专业删除作业
 */
export const deleteMicroHomework = (data: any) => {
  return HTTP(`/exam-api/microSpecial/delete`, {
    method: 'POST',
    params: data
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  }).catch(error => {
    return error;
  });
}

/**
 * 微专业下架作业
 */
export const offShelfMajorHomework = (params: any) => {
  return HTTP(`/exam-api/microSpecial/update/status`, {
    method: 'GET',
    params
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  }).catch(error => {
    return error;
  });
}

// 作业分组API
/**
 * 点击添加按钮查询得人员列表(所有的)
 */
export const studentList = (params: any) => {
  const { homeId, size, page, studentName } = params;
  return HTTP(`/exam-api/homeworkteam/grouping/addbutton/student/list`, {
    method: 'GET',
    params: {
      homeId,
      size,
      page,
      studentName
    },
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  }).catch(error => {
    return error;
  });
}

/**
 * 移动学生到指定分组
 */
export const moveStudents = (data: any) => {
  const { homeId, groupIndex, teamName, teamId, ...bodyData } = data;
  return HTTP(`/exam-api/homeworkteam/grouping/move/student/list`, {
    method: 'POST',
    params: {
      homeId,
      groupIndex,
      teamName,
      teamId
    },
    data: bodyData.selectStudent // 学生数组放在请求体
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  }).catch(error => {
    return error;
  });
}

/**
 * 获取作业分组的组列表
 */
export const getGroupList = (params: { homeId: string; }) => {
  return HTTP(`/exam-api/homeworkteam/grouping/list`, {
    method: 'GET',
    params
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  }).catch(error => {
    return error;
  });
}

/**
 * 获取分组学生详情
 */
export const getGroupDetail = (params: any) => {
  return HTTP(`/exam-api/homeworkteam/grouping/detail`, {
    method: 'GET',
    params
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  }).catch(error => {
    return error;
  });
}

/**
 * 新增分组
 */
export const addGroup = (data: any) => {
  return HTTP(`/exam-api/homeworkteam/add/grouping`, {
    method: 'GET',
    params: data
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  }).catch(error => {
    return error;
  });
}

/**
 * 删除分组
 */
export const deleteGroup = (params: any) => {
  return HTTP(`/exam-api/homeworkteam/delete/grouping`, {
    method: 'DELETE',
    params
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  }).catch(error => {
    return error;
  });
}