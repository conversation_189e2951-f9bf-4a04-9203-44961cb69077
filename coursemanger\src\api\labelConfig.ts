import HTTP from './index';



// 新增标签
export const insertlabel = (data: any) =>
  HTTP(`/learn/m1/labelConfig/insert`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 查询标签（公共、自定义、资源）
export const getLabels = (data: any) => {
  if (data.type === 2) {
    // 资源标签接口
    const { page, size, ...rest } = data; // 提取 page 和 size
    const params = {
      pageIndex: page, // 后端接口 pageIndex 从 1 开始
      pageSize: size,
      ...rest, // 保留其他筛选条件
    };
    return HTTP(`/rman/v1/search/knowledge/fragment/label`, {
      method: 'POST',
      data: params,
    })
      .then(res => {
        if (res.status === 200) {
          // 可能需要根据实际返回结构调整
          return {
            results: res.data?.data.data || [], // 假设资源标签列表在 list 字段
            page:res.data.data.pageIndex,
            total: res.data?.data.recordTotal || 0, // 假设总数在 total 字段
          };
        }
      })
      .catch(error => {
        console.error('获取资源标签失败:', error);
        return Promise.reject(error); // 返回 rejected Promise
      });
  } else {
    // 公共标签和自定义标签接口
    return HTTP(`/learn/m1/labelConfig/page`, {
      method: 'POST',
      data,
    })
      .then(res => {
        if (res.status === 200) {
          return res.data; // 假设这个接口直接返回 { results: [], total: 0 }
        }
      })
      .catch(error => {
        console.error('获取公共/自定义标签失败:', error);
        return Promise.reject(error); // 返回 rejected Promise
      });
  }
};

// 删除标签
export const deleteLabel = (data: any) =>
  HTTP(`/learn/m1/labelConfig/delete`, {
    method: 'get',
    params: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 更新标签
export const updateLabel = (data: any) =>   
  HTTP(`/learn/m1/labelConfig/update`, {
    method: 'POST',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 获取是否允许老师新增至公共标签状态
export const getIsAllowTeacher = () =>
  HTTP(`/learn/m1/labelConfig/getIsAllowTeacher`, {
    method: 'GET',
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

// 设置是否允许教师将自定义标签新增至公共标签
export const setAllowTeacher = (data: any) =>
  HTTP(`/learn/m1/labelConfig/isAllowTeacher`, {
    method: 'GET',
    params: data, // 通过 params 传递参数，适用于 GET 请求
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });


