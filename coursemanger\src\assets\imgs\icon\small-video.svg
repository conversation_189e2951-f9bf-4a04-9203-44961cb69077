<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>形状结合</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#66C2FF" offset="0%"></stop>
            <stop stop-color="#5F67E9" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="切图" transform="translate(-25.000000, -21.000000)" fill="url(#linearGradient-1)" fill-rule="nonzero">
            <path d="M41.6666667,21 C43.5,21 45,22.6503174 45,24.6814772 L45,37.3069821 C45,39.3496826 43.5,41 41.6666667,41 C41.6222222,41 41.5666667,41 41.5222222,40.9884593 L27.7222222,39.118869 C26.2,38.9457588 25,37.5031737 25,35.8413156 L25,26.170225 C25,24.508367 26.2,23.0657819 27.7333333,22.881131 L41.5222222,21.0115407 C41.5666667,21 41.6222222,21 41.6666667,21 Z M33.5395036,27.7027559 C33.2555728,27.8716144 33.0796596,28.1847004 33.0777778,28.5245239 L33.0777778,33.4985574 C33.0777778,34.2256203 33.8444445,34.6872475 34.4555555,34.3179458 L38.1666667,31.8366994 C38.7777778,31.4673976 38.7777778,30.5441431 38.1666667,30.186382 L34.4555555,27.7051356 C34.1724416,27.534804 33.8234343,27.5338974 33.5395036,27.7027559 Z" id="形状结合"></path>
        </g>
    </g>
</svg>