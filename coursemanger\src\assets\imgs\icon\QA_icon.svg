<?xml version="1.0" encoding="UTF-8"?>
<svg width="68px" height="68px" viewBox="0 0 68 68" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>问答</title>
    <defs>
        <linearGradient x1="11.5850091%" y1="7.74720245%" x2="103.000819%" y2="107.907832%" id="linearGradient-1">
            <stop stop-color="#A7DE64" offset="0%"></stop>
            <stop stop-color="#39AA4F" offset="100%"></stop>
        </linearGradient>
        <path d="M20,0 C31.0457079,0 40,8.95429213 40,20 C40,31.0457079 31.0457079,40 20,40 C16.9962247,40 14.0865169,39.3360899 11.4379326,38.0795056 C11.3332135,38.0297978 11.1938876,37.9611685 11.0198652,37.873573 C10.8470259,37.7865709 10.6464172,37.7728967 10.4633708,37.8356404 L6.45249438,39.2106067 C4.99923596,39.708809 3.41725843,38.9345618 2.91901124,37.4813034 C2.77639426,37.0652419 2.73446832,36.6212922 2.79667416,36.1858876 L3.45568539,31.5721798 C3.48074493,31.3966763 3.44009058,31.2181013 3.34152809,31.0707416 C3.30496095,31.0161625 3.26870568,30.9613751 3.23276404,30.906382 C1.1381573,27.6931236 0,23.9308315 0,20 C0,8.95429213 8.95429213,0 20,0 Z M10.872,15.102 C9.696,15.102 9.696,15.102 8.863,15.501 C8.03,15.9 8.03,15.9 7.491,16.593 C6.952,17.286 6.952,17.286 6.7,18.231 C6.448,19.176 6.448,19.176 6.448,20.254 C6.448,21.374 6.448,21.374 6.728,22.326 C7.008,23.278 7.008,23.278 7.568,23.964 C8.128,24.65 8.128,24.65 8.954,25.035 C9.78,25.42 9.78,25.42 10.872,25.42 C11.712,25.42 11.712,25.42 12.342,25.217 C12.846,25.0546 12.846,25.0546 13.33208,24.7354 L13.574,24.566 L14.302,25.238 C14.498,25.434 14.498,25.434 14.82,25.378 C15.142,25.322 15.142,25.322 15.422,25.042 C15.674,24.79 15.674,24.79 15.737,24.454 C15.7895,24.174 15.7895,24.174 15.6524167,23.9815 L15.59,23.908 L14.722,23.096 C15.044,22.424 15.044,22.424 15.17,21.738 C15.296,21.052 15.296,21.052 15.296,20.254 C15.296,19.162 15.296,19.162 15.03,18.224 C14.764,17.286 14.764,17.286 14.218,16.593 C13.672,15.9 13.672,15.9 12.839,15.501 C12.006,15.102 12.006,15.102 10.872,15.102 Z M29.94,15.242 C29.548,15.242 29.548,15.242 29.317,15.417 C29.1322,15.557 29.1322,15.557 29.05044,15.72836 L29.016,15.816 L26.132,24.23 C25.936,24.804 25.936,24.804 26.195,25.042 C26.454,25.28 26.454,25.28 26.888,25.28 C27.322,25.28 27.322,25.28 27.546,25.077 C27.7252,24.9146 27.7252,24.9146 27.84168,24.66708 L27.896,24.538 L28.512,22.928 L31.354,22.928 L32.026,24.608 C32.138,24.888 32.138,24.888 32.348,25.091 C32.558,25.294 32.558,25.294 32.992,25.28 C33.426,25.28 33.426,25.28 33.685,25.035 C33.911625,24.820625 33.911625,24.820625 33.8113281,24.3865156 L33.776,24.258 L30.864,15.816 C30.794,15.592 30.794,15.592 30.563,15.417 C30.332,15.242 30.332,15.242 29.94,15.242 Z M20.294,14.892 C19.832,14.892 19.832,14.892 19.412,15.025 C18.992,15.158 18.992,15.158 18.663,15.41 C18.334,15.662 18.334,15.662 18.138,16.04 C17.942,16.418 17.942,16.418 17.942,16.908 C17.942,17.412 17.942,17.412 18.215,17.972 C18.488,18.532 18.488,18.532 18.852,18.98 C17.872,19.54 17.872,19.54 17.249,20.352 C16.626,21.164 16.626,21.164 16.626,22.13 C16.626,22.858 16.626,22.858 16.892,23.404 C17.158,23.95 17.158,23.95 17.606,24.321 C18.054,24.692 18.054,24.692 18.649,24.874 C19.244,25.056 19.244,25.056 19.916,25.056 C20.77,25.056 20.77,25.056 21.54,24.734 C22.31,24.412 22.31,24.412 22.94,23.88 C23.122,24.104 23.122,24.104 23.311,24.321 C23.5,24.538 23.5,24.538 23.696,24.776 C23.822,24.93 23.822,24.93 23.969,24.979 C24.116,25.028 24.116,25.028 24.27,25.014 C24.424,25 24.424,25 24.564,24.923 C24.704,24.846 24.704,24.846 24.802,24.734 C24.998,24.524 24.998,24.524 25.068,24.265 C25.138,24.006 25.138,24.006 24.9,23.726 C24.648,23.418 24.648,23.418 24.41,23.131 C24.172,22.844 24.172,22.844 23.948,22.578 C24.2,22.13 24.2,22.13 24.326,21.626 C24.452,21.122 24.452,21.122 24.452,20.618 C24.452,20.338 24.452,20.338 24.305,20.121 C24.158,19.904 24.158,19.904 23.668,19.876 C23.178,19.848 23.178,19.848 22.975,20.051 C22.772,20.254 22.772,20.254 22.744,20.534 C22.716,20.8 22.716,20.8 22.66,21.066 C22.31,20.66 22.31,20.66 21.981,20.268 C21.652,19.876 21.652,19.876 21.204,19.344 C21.54,19.078 21.54,19.078 21.806,18.756 C22.072,18.434 22.072,18.434 22.247,18.105 C22.422,17.776 22.422,17.776 22.52,17.447 C22.618,17.118 22.618,17.118 22.618,16.824 C22.618,16.404 22.618,16.404 22.429,16.047 C22.24,15.69 22.24,15.69 21.918,15.438 C21.596,15.186 21.596,15.186 21.176,15.039 C20.756,14.892 20.756,14.892 20.294,14.892 Z M10.872,16.698 C11.586,16.698 11.586,16.698 12.083,16.978 C12.58,17.258 12.58,17.258 12.888,17.748 C13.196,18.238 13.196,18.238 13.336,18.896 C13.476,19.554 13.476,19.554 13.476,20.296 C13.476,20.702 13.476,20.702 13.441,21.066 C13.4176667,21.3086667 13.4176667,21.3086667 13.3725556,21.5451111 L13.322,21.78 L12.916,21.388 C12.72,21.192 12.72,21.192 12.433,21.241 C12.146,21.29 12.146,21.29 11.824,21.612 C11.446,21.99 11.446,21.99 11.432,22.27 C11.4208,22.494 11.4208,22.494 11.53504,22.63736 L11.6,22.704 L12.342,23.404 C12.02,23.614 12.02,23.614 11.677,23.712 C11.334,23.81 11.334,23.81 10.872,23.81 C10.13,23.81 10.13,23.81 9.633,23.502 C9.136,23.194 9.136,23.194 8.828,22.69 C8.52,22.186 8.52,22.186 8.394,21.549 C8.268,20.912 8.268,20.912 8.268,20.254 C8.268,19.526 8.268,19.526 8.408,18.882 C8.548,18.238 8.548,18.238 8.856,17.755 C9.164,17.272 9.164,17.272 9.661,16.985 C10.158,16.698 10.158,16.698 10.872,16.698 Z M19.818,20.184 C20.014,20.422 20.014,20.422 20.259,20.723 C20.504,21.024 20.504,21.024 20.777,21.346 C21.05,21.668 21.05,21.668 21.337,22.004 C21.624,22.34 21.624,22.34 21.89,22.648 C21.526,23.054 21.526,23.054 21.05,23.292 C20.574,23.53 20.574,23.53 19.986,23.53 C19.664,23.53 19.664,23.53 19.363,23.425 C19.062,23.32 19.062,23.32 18.831,23.124 C18.6,22.928 18.6,22.928 18.46,22.655 C18.32,22.382 18.32,22.382 18.32,22.046 C18.292,21.514 18.292,21.514 18.635,21.059 C18.978,20.604 18.978,20.604 19.818,20.184 Z M29.94,17.874 L31.018,21.346 L28.862,21.346 L29.94,17.874 Z M20.294,16.376 C20.784,16.376 20.784,16.376 20.945,16.733 C21.106,17.09 21.106,17.09 20.826,17.58 C20.714,17.79 20.714,17.79 20.56,17.93 C20.406,18.07 20.406,18.07 20.238,18.196 C20.112,18.084 20.112,18.084 19.986,17.916 C19.86,17.748 19.86,17.748 19.762,17.566 C19.664,17.384 19.664,17.384 19.601,17.223 C19.538,17.062 19.538,17.062 19.538,16.95 C19.538,16.81 19.538,16.81 19.615,16.698 C19.692,16.586 19.692,16.586 19.804,16.516 C19.916,16.446 19.916,16.446 20.049,16.411 C20.182,16.376 20.182,16.376 20.294,16.376 Z" id="path-2"></path>
        <filter x="-32.5%" y="-27.5%" width="165.0%" height="165.0%" filterUnits="objectBoundingBox" id="filter-3">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="4" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.588235294   0 0 0 0 0.815686275   0 0 0 0 0.368627451  0 0 0 0.6 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="切图" transform="translate(-650.000000, -81.000000)">
            <g id="问答" transform="translate(650.000000, 81.000000)">
                <rect id="矩形" fill="#EAF8EC" x="0" y="0" width="68" height="68" rx="18"></rect>
                <g transform="translate(14.000000, 14.000000)" fill-rule="nonzero" id="形状结合">
                    <g>
                        <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                        <use fill="url(#linearGradient-1)" xlink:href="#path-2"></use>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>