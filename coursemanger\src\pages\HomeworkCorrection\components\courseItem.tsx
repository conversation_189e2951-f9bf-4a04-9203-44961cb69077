import React, { FC, useState, useEffect } from 'react';
import { Input, Row, Col, Pagination } from 'antd';
import { reqCourseListApi } from '@/api/homeworkCorrection'
interface CourseItemProps {
  emitSelectCourse: (course: any) => void;
  activeKey: string | number;//1，班级课 2：公开课 2，微课 4，培训课 
  modalVisible: boolean
}
const CourseItem: FC<CourseItemProps> = ({ emitSelectCourse, activeKey, modalVisible }) => {
  const [activeCard, setActiveCard] = useState<any>(undefined)
  const [courseList, setCourseList] = useState<any>([])
  const [keyword, setKeyword] = useState<string>('')
  const [total, setTotal] = useState<number>(0)
  const [pagination, setPagination] = useState<any>({
    current: 1,
    pageSize: 10,
  })

  const onSearch = (value: string) => {
    setKeyword(value)
  }
  useEffect(() => {
    getCourseList()
  }, [pagination, keyword, activeKey]);
  // 获取课程列表
  const getCourseList = async () => {
    if (activeKey == 1 || activeKey == 2 || activeKey == 4) {
      let courseType = 0
      if (activeKey == 1) courseType = 2
      if (activeKey == 2) courseType = 1
      if (activeKey == 4) courseType = 3
      reqCourseListApi({
        // courseType: 0, 2:班级课 1：公开课 3：培训课
        courseType,
        keyword,
        pageIndex: pagination.current,
        pageSize: pagination.pageSize,
        personalMode: false,
        // semester_teaching_courses
      }).then((res: any) => {
        if (res?.status == 200) {
          setTotal(res.data.total)
          setCourseList(res.data.results?.map((item: any) => {
            return {
              id: item.entityData.contentId_,
              name: item.entityData.name_,
              cover: item.entityData.cover,
              semester_teaching_courses: item.entityData.semester_teaching_courses,
              teacher_names: item.entityData.teacher_names,
              courseType: item.entityData.courseType,
              courseStatus: item.entityData.courseStatus,
              courseSemester: item.entityData.course_semester_id,
            }
          }))
        }
      })
    }
    // else if (activeKey == 3) {
    //   reqCourseListApi2({
    //     courseType: 0,
    //     keyword,
    //     order: [{ field: "createDate_", isDesc: true }],
    //     page: pagination.current,
    //     size: pagination.pageSize,
    //   }).then((res: any) => {
    //     if (res.status == 200) {
    //       setTotal(res.data.total)
    //       setCourseList(res.data.results?.map((item: any) => {
    //         return {
    //           id: item.contentId_,
    //           name: item.name_,
    //           cover: item.cover,
    //           semester_teaching_courses: '-',
    //           teacher_names: item.teacher_names,
    //           courseType: item.courseType,
    //           courseStatus: item.courseStatus,
    //           courseSemester: '-',
    //         }
    //       }))
    //     }
    //   })
    // }
  }
  return (
    <div className='chapter-content'>
      <Input.Search placeholder="请输入课程名称" onSearch={onSearch} style={{ width: 200 }} />
      <Row className='chapter-list' gutter={[16, 16]}>
        {courseList?.map((item: any) => {
          return <Col key={item.id} xs={12} md={8} xl={6} className='gutter-row'>
            <div className={`chapter-card ${activeCard == item.id ? 'chapter-card-checked' : ''}`} onClick={() => {
              setActiveCard(item.id)
              emitSelectCourse(item)
            }}>
              <img src={item.cover} alt="" />
              <div className='card-content'>
                <p className='title'>{item.name}</p>
                <p className='semester'>{item.semester_teaching_courses}</p>
                <p className='teacher'>{item.teacher_names?.join(',')}</p>
                <p className='school'>{item.school}</p>
              </div>
            </div>
          </Col>
        })}
      </Row>
      <Pagination
        size="small"
        total={total}
        showSizeChanger
        showQuickJumper
        current={pagination.current}
        pageSize={pagination.pageSize}
        onChange={(page, pageSize) => {
          setPagination({ ...pagination, current: page, pageSize: pageSize })
        }}
      />
    </div>
  );
};

export default CourseItem;