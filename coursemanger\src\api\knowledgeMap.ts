import HTTP from '@/api/index';

//获取知识点图谱数据
export const reqKnowledgeTree = (params?: any) => HTTP.get('/learn/v1/course/point/by/map', params).then((res) => {
  if (res.status === 200) {
    return res.data;
  }
})
//获取是否有资源权限
export const reqIfHasPermission = (params?: any) => HTTP('/learn/v1/course/point/by/map/role', { method: 'GET', params }).then((res) => {
  if (res.status === 200) {
    return res.data;
  }
})