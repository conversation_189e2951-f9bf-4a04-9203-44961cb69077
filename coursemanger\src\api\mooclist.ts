import axios from 'axios';
import HTTP from './index';

// 新建mooc课程
export function nweMoocCourse(data: any) {
  return HTTP.post(`/learn/v1/teaching/course/create`, data)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
// 获取学习对象列表
export function cultivationList() {
  return HTTP.get(`/learn/v1/course/cultivation/level`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
// 通过tpl创建课程
export function createCourseByTpl(data: any, templateId: string) {
  return HTTP.post(
    `/learn/v1/teaching/course/create/course/by/template?templateId=${templateId}`,
    data,
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 获取课程列表
export function getMoocList(data: string, params?: any) {
  return HTTP.get(`/learn/v1/teaching/course/get/courses/list?${data}`, {
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 发布课程
export function releaseCourse(
  data: string[],
  courseType?: number,
  review?: number,
) {
  return HTTP(`/learn/v1/teaching/course/publish`, {
    method: 'POST',
    params: {
      courseType,
      review,
    },
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 发布课程new
export function releaseCourseNew(
  data: string[],
  courseType?: number,
  review?: number,
) {
  return HTTP(`/learn/v1/teaching/course/audit`, {
    method: 'POST',
    params: {
      courseType,
      review,
    },
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
// 下架课程
export function offShelfCourse(data: string[], params: any) {
  return HTTP(`/learn/v1/teaching/course/off/shelf`, {
    method: 'POST',
    params,
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
// 下架课程
export function offShelfCourseNew(data: string[], params: any) {
  return HTTP(`/learn/v1/teaching/course/cancel`, {
    method: 'POST',
    params,
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function offShelfThirdCourse(params: any) {
  return HTTP(`/unifiedplatform/v1/api/course/publish-status`, {
    method: 'POST',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 删除
export function deleteTemplateCourse(data: string[]) {
  return HTTP(`/learn/v1/curriculum/center/delete/template`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function deleteCourse(data: string[]) {
  return HTTP(`/learn/v1/teaching/course/delete`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function deleteResult(processid: string) {
  return HTTP.get(`/recycle/delete/process?processid=${processid}`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 批量更新课程章节发布状态
export function updataCourse(courseId: string) {
  return HTTP.post(
    `/learn/v1/teaching/course/batch/update/publish/status/${courseId}/1`,
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

/**
 * 查询线下课程
 * @param params
 */
// export function getOfflineCourse(params: {
//   pageIndex: number;
//   pageSize: number;
// }) {
//   return HTTP(`/cvod/v1/teaching/course/get/offline/course`, {
//     method: 'POST',
//     data: JSON.stringify(params),
//   })
//     .then((res) => {
//       if (res.status === 200) {
//         return res.data;
//       }
//     })
//     .catch((error) => {
//       console.error(error);
//     });
// }

/**
 * 查询当前用户详情信息
 * @param cookie
 */
export function queryUserDetailInfo() {
  return HTTP(`/learn/v1/teaching/course/get/original/user/info`, {
    method: 'GET',
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
// export const queryUserDetailInfo = (cookie: string) =>
//   request<API.Response<any>>(
//     `${config.cvodService}/v1/teaching/course/get/original/user/info`,
//     {
//       method: 'GET',
//       headers: {
//         cookie,
//       },
//     }
//   );
// 查询课程门次
export function getCourse_floor(params: any) {
  return HTTP(`/ipingestman/schedule/course_floor/`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getCourseFloorDetail(params: any) {
  return HTTP(`/ipingestman/schedule/course_floor/detail`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
// 查询当前学期
export function fetchSemeter() {
  return HTTP(`/unifiedplatform/v1/base/data/database/get/semester/current`, {
    method: 'GET',
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function getSpocCourse(params: any, abordCallback?: any) {
  return HTTP(`/learn/v1/teaching/course/get/courses/list`, {
    method: 'GET',
    params,
    cancelToken: new axios.CancelToken(c => abordCallback && abordCallback(c)),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
/**
 * 获取第三方课堂
 * @param params
 */
export function fetchOtherCourse(name: string, params: any) {
  return HTTP(`/learn/v1/course/dock/thridcourse/${name}`, {
    method: 'GET',
    params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 导出课程资源包
export function exportword(data: string[], params?: any) {
  return HTTP(`/learn/v1/course/down/zip`, {
    method: 'POST',
    data: JSON.stringify(data),
    params,
    responseType: 'blob',
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function copyCourse(data: any) {
  return HTTP(`/learn/v1/teaching/course/copy/to`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function updateRelated(data: any) {
  return HTTP(`/learn/v1/teaching/course/related`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function publishMap(data: any) {
  return HTTP(`/learn/v1/teaching/map/course/publish`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function publishMapNew(data: any) {
  return HTTP(`/learn/v1/teaching/map/course/publish/flowbpm`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
// export const deleteResult = (processid: string) => {
//     return http<deleteTypes.IdeleteResult>(`/recycle/delete/process?processid=${processid}`)
// }

// 获取canvas 平台课课程数据
export function fetCanvasCourse() {
  return HTTP(`/canvas-lms-adapter/Graph/UserCourses4Binding`, {
    method: 'GET'
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 绑定cancas 平台的课程
export function bindCanvasCourse(data: any) {
  return HTTP(`/canvas-lms-adapter/Graph/BindingCourse?course_id=${data.course_id}&map_id=${data.map_id}&map_name=${data.map_name}&isRedirect=false`, {
    method: 'get',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 根据map_id查询当前绑定的课程
export function getCourseByMapId(data: any) {
  return HTTP(`/canvas-lms-adapter/Graph/MapBindingCourse?map_id=${data.map_id}`, {
    method: 'get',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    })
}

// 解绑
export function unbindCanvasCourse(data: any) {
  return HTTP(`/canvas-lms-adapter/Graph/UnBindingCourse?course_id=${data.course_id}&map_id=${data.map_id}`, {
    method: 'get',
    data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    })
}

// 分享图谱
export function shareMaptoUser(data: any) {
  return HTTP('/learn/course/share/add', {
    method: 'POST',
    data: JSON.stringify(data),
  })
}

// 获取分享给我的图谱列表
export function getShareList(params: any) {
  return HTTP('/learn/course/share/page', {
    method: 'GET',
    params,
  })
}

// 新增第三方课程  /learn/v1/course/add/or/update/dock/course
export function updataDockCourse(data: any) {
  return HTTP('/learn/v1/course/add/or/update/dock/course', {
    method: 'POST',
    data: JSON.stringify(data),
  }).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  }).catch(error => {
    console.error(error);
  })
}

// 是否显示第三方课程页面的视图模式
export function showDockCourseMode() {
  return HTTP('/learn/v1/course/support/top', {
    method: 'GET',
  })
}

// 第三方课程-置顶
export function sortDockCourse(data: any) {
  return HTTP('/learn/v1/course/top/thridCourse', {
    method: 'POST',
    data
  })
}