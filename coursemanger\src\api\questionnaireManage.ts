import React from 'react';
import HTTP from './index';
export namespace QuestionnaireService {
  // 我的问卷-课程
  export function getPageList(params: any) {
    return HTTP(`/customization/grantRecord/findMyCourseQuestionnaire`, {
      method: 'get',
      params,
    });
  }
  // 批量发放问卷
  export function addQuestionnaire(data: any) {
    return HTTP(`/customization/questionnaire/grantBatchQuestionnaire`, {
      method: 'post',
      data,
    });
  }
  // 删除问卷
  export function deleteQuestionnaire(id: any) {
    return HTTP(`/customization/grantRecord/delete/${id}`, {
      method: 'post',
    });
  }
  // 搜索问卷
  export function getFindPage(params: any) {
    return HTTP(`/customization/questionnaire/findQuestionnaire`, {
      method: 'get',
      params,
    });
  }
}
