.resource-upload-modal {
  .file-wrp {
    .pr-upload-header {
      display: flex;
      padding-bottom: 10px;
      align-items: center;

      // .ant-btn {
      //   margin-right: 20px;
      //   border-color: var(--primary-color);
      //   color: var(--primary-color);
      // }
      .ant-btn-primary {
        margin-right: 16px;
      }
    }
    .ant-upload-list-text {
      display: none;
    }
    .ant-upload-drag {
      height: calc(100% - 45px);
      background: #F7F9FA;

      .anticon {
        color: var(--primary-color);
        font-size: 30px;
      }

      .ant-upload-text {
        font-size: 14px;
        color: #525252;

        &:last-child {
          margin-top: 15px;
          color: #9D9D9D;

          &::before {
            display: inline-block;
            margin-right: 2px;
            color: #ff4d4f;
            font-size: 14px;
            font-family: SimSun, sans-serif;
            line-height: 1;
            content: '*';
          }

        }
      }
    }

    #videoElement {
      width: 1000px !important;
      height: 1000px / 16 * 9 !important;
      max-height: 1000px / 16 * 9 !important;
    }

    .trans-coding {
      text-align: center;
    }
  }
}
