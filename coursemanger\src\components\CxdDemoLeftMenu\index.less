
.cxd-composite-menu {
  display: flex;
  height: 100%;
  //width: 100%;
  background-color: #f7f8fa;
  box-shadow: 2px 0px 4px 0px rgba(0, 0, 0, 0.05);
  z-index: 9;
  .cxd-primary-nav {
    width: 70px;
    background-color: #fff;
    //padding-top: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    flex-shrink: 0;
    //border-right: 1px solid #e8e8e8;
    justify-content: center;

    .nav-item {
      width: 58px;
      height: 58px;
      margin-bottom: 40px;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s;
      color: #595959;

      .nav-icon {
        font-size: 24px;
        //margin-bottom: 8px;
      }

      .nav-label {
        font-size: 13px;
        margin-bottom: 2px;
      }

      &.active {
        background-color:  var(--hover-color);
        color: var(--primary-color);
      }
    }
    .nav-item:hover {
      border: 1px solid var(--primary-color);
      box-sizing: border-box;
    }
  }

  .cxd-secondary-nav {
    width: 200px;
    background-color: #fff;
    flex-shrink: 0;
    overflow-y: auto;
    padding-top: 8px;

    .ant-menu-inline {
      border-right: none;
    }

    .ant-menu-submenu-title,
    .ant-menu-item {
      font-size: 14px;
    }

    .ant-menu-item {
      margin-left: -1px;
    }

    .ant-menu-item-selected {
      background-color: #f0f2f5 !important;
      color: #262626;
      font-weight: bold;
      border-right: 3px solid #1890ff;
    }
  }
}
.icon {
  width: 20px;
  height: 20px;
  svg {
    width: 100%;
    height: 100%;
  }
}
