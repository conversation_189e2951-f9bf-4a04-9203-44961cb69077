/*
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2021-04-06 16:18:51
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2021-06-22 14:39:25
 */
/* Tiny Page Embed plugin
 *
 * Copyright 2010-2019 Tiny Technologies Inc. All rights reserved.
 *
 * Version: 1.1.0-34
 */
!function(t){"use strict";function r(e){return parseInt(e,10)}function i(e,n){var r=e-n;return 0==r?0:0<r?1:-1}function o(e,n,r){return{major:e,minor:n,patch:r}}function u(e){var n=/([0-9]+)\.([0-9]+)\.([0-9]+)(?:(\-.+)?)/.exec(e);return n?o(r(n[1]),r(n[2]),r(n[3])):o(0,0,0)}function s(e,n){return!!e&&-1===function(e,n){var r=i(e.major,n.major);if(0!==r)return r;var t=i(e.minor,n.minor);if(0!==t)return t;var o=i(e.patch,n.patch);return 0!==o?o:0}(function(e){return u(function(e){return[e.majorVersion,e.minorVersion].join(".").split(".").slice(0,3).join(".")}(e))}(e),u(n))}function e(){}function d(e){return function(){return e}}function n(){return l}var a,c=d(!1),f=d(!0),l=(a={fold:function(e,n){return e()},is:c,isSome:c,isNone:f,getOr:v,getOrThunk:p,getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:d(null),getOrUndefined:d(void 0),or:v,orThunk:p,map:n,each:e,bind:n,exists:c,forall:f,filter:n,equals:m,equals_:m,toArray:function(){return[]},toString:d("none()")},Object.freeze&&Object.freeze(a),a);function m(e){return e.isNone()}function p(e){return e()}function v(e){return e}function g(n){return function(e){return function(e){if(null===e)return"null";var n=typeof e;return"object"==n&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"==n&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":n}(e)===n}}function h(e,n){return-1<function(e,n){return se.call(e,n)}(e,n)}function y(e,n){for(var r=e.length,t=new Array(r),o=0;o<r;o++){var i=e[o];t[o]=n(i,o)}return t}function b(e,n){for(var r=0,t=e.length;r<t;r++){n(e[r],r)}}function S(e,n){for(var r=[],t=0,o=e.length;t<o;t++){var i=e[t];n(i,t)&&r.push(i)}return r}function w(e){for(var n=[],r=0,t=e.length;r<t;++r){if(!re(e[r]))throw new Error("Arr.flatten item "+r+" was not an array, input: "+e);ae.apply(n,e[r])}return n}function x(e,n){var r=y(e,n);return w(r)}function O(e,n){for(var r=ce(e),t=0,o=r.length;t<o;t++){var i=r[t];n(e[i],i)}}function N(e,n,r){!function(e,n,r){if(!(ne(r)||te(r)||ie(r)))throw t.console.error("Invalid call to Attr.set. Key ",n,":: Value ",r,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(n,r+"")}(e.dom(),n,r)}function E(e,n){var r=e.dom().getAttribute(n);return null===r?void 0:r}function T(e,n){return Q.from(E(e,n))}function C(e,n,r){return 0!=(e.compareDocumentPosition(n)&r)}function A(e,n){var r=function(e,n){for(var r=0;r<e.length;r++){var t=e[r];if(t.test(n))return t}}(e,n);if(!r)return{major:0,minor:0};function t(e){return Number(n.replace(r,"$"+e))}return he(t(1),t(2))}function D(e,n){return function(){return n===e}}function _(e,n){return function(){return n===e}}function R(e,n){var r=String(n).toLowerCase();return function(e,n){for(var r=0,t=e.length;r<t;r++){var o=e[r];if(n(o,r))return Q.some(o)}return Q.none()}(e,function(e){return e.search(r)})}function I(e,n){return-1!==e.indexOf(n)}function P(n){return function(e){return I(e,n)}}function L(e){return e.nodeType!==ke&&e.nodeType!==Me||0===e.childElementCount}function j(e){return function(e,n){var r=e.dom().childNodes;return Q.from(r[n]).map(ee.fromDom)}(e,0)}function k(e,n,r){var t=function(e,n){var r=E(e,n);return void 0===r||""===r?[]:r.split(" ")}(e,n).concat([r]);return N(e,n,t.join(" ")),!0}function M(e){return void 0!==e.dom().classList}function U(e,n){M(e)?e.dom().classList.add(n):function(e,n){k(e,"class",n)}(e,n)}function F(e,n){return M(e)&&e.dom().classList.contains(n)}function z(e){return F(e,Ue)}function B(e){var n=e.attr("class");return n&&I(" "+n+" "," "+Ue+" ")}function K(o,i,u){return Q.from(o).map(ee.fromDom).filter(z).bind(j).filter(function(e){return"iframe"===function(e){return e.dom().nodeName.toLowerCase()}(e)}).fold(function(){return i},function(e){function n(e,n,r){return Q.from(E(e,n)).getOr(r)}var r,t;return{source:{value:n(e,"src",i.source.value)},size:(r=o,t=ee.fromDom(r),function(e,n,r){return b(e,function(e){r=n(r,e)}),r}(u,function(e,n){return F(t,n.value)?n.value:e},"inline")),dimensions:{width:n(e,"width",i.dimensions.width),height:n(e,"height",i.dimensions.height)},name:n(e,"name",i.name),title:n(e,"title",i.title),descriptionUrl:{value:n(e,"longdesc",i.descriptionUrl.value)},showBorder:T(e,"frameborder").map(function(e){return"0"!==e.toLowerCase()}).getOr(i.showBorder),scrollbar:T(e,"scrolling").map(function(e){return"yes"===e.toLowerCase()}).getOr(i.scrollbar)}})}function V(e,n){e.dom().appendChild(n.dom())}function q(e,n){return function(e,n){var r=void 0===n?t.document:n.dom();return L(r)?[]:y(r.querySelectorAll(e),ee.fromDom)}(n,e)}function W(e,n,r){if(!ne(r))throw t.console.error("Invalid call to CSS.set. Property ",n,":: Value ",r,":: Element ",e),new Error("CSS value must be a string: "+r);!function(e){return void 0!==e.style&&oe(e.style.getPropertyValue)}(e)||e.style.setProperty(n,r)}function H(e){return x(e,ze)}var X,Y,G,$,J=function(r){function e(){return o}function n(e){return e(r)}var t=d(r),o={fold:function(e,n){return n(r)},is:function(e){return r===e},isSome:f,isNone:c,getOr:t,getOrThunk:t,getOrDie:t,getOrNull:t,getOrUndefined:t,or:e,orThunk:e,map:function(e){return J(e(r))},each:function(e){e(r)},bind:n,exists:n,forall:n,filter:function(e){return e(r)?o:l},toArray:function(){return[r]},toString:function(){return"some("+r+")"},equals:function(e){return e.is(r)},equals_:function(e,n){return e.fold(c,function(e){return n(r,e)})}};return o},Q={some:J,none:n,from:function(e){return null==e?l:J(e)}},Z=function(e){if(null==e)throw new Error("Node cannot be null or undefined");return{dom:d(e)}},ee={fromHtml:function(e,n){var r=(n||t.document).createElement("div");if(r.innerHTML=e,!r.hasChildNodes()||1<r.childNodes.length)throw t.console.error("HTML does not have a single root node",e),new Error("HTML must have a single root node");return Z(r.childNodes[0])},fromTag:function(e,n){var r=(n||t.document).createElement(e);return Z(r)},fromText:function(e,n){var r=(n||t.document).createTextNode(e);return Z(r)},fromDom:Z,fromPoint:function(e,n,r){var t=e.dom();return Q.from(t.elementFromPoint(n,r)).map(Z)}},ne=g("string"),re=g("array"),te=g("boolean"),oe=g("function"),ie=g("number"),ue=Array.prototype.slice,se=Array.prototype.indexOf,ae=Array.prototype.push,ce=(oe(Array.from)&&Array.from,Object.keys),fe=Object.hasOwnProperty,le=function(e,n){return fe.call(e,n)},de=(t.Node.ATTRIBUTE_NODE,t.Node.CDATA_SECTION_NODE,t.Node.COMMENT_NODE,t.Node.DOCUMENT_NODE),me=(t.Node.DOCUMENT_TYPE_NODE,t.Node.DOCUMENT_FRAGMENT_NODE,t.Node.ELEMENT_NODE),pe=(t.Node.TEXT_NODE,t.Node.PROCESSING_INSTRUCTION_NODE,t.Node.ENTITY_REFERENCE_NODE,t.Node.ENTITY_NODE,t.Node.NOTATION_NODE,void 0!==t.window?t.window:Function("return this;")(),function(e,n){return C(e,n,t.Node.DOCUMENT_POSITION_CONTAINED_BY)}),ve=function(e){function n(){return r}var r=e;return{get:n,set:function(e){r=e},clone:function(){return ve(n())}}},ge=function(){return he(0,0)},he=function(e,n){return{major:e,minor:n}},ye={nu:he,detect:function(e,n){var r=String(n).toLowerCase();return 0===e.length?ge():A(e,r)},unknown:ge},be="Firefox",Se=function(e){var n=e.current;return{current:n,version:e.version,isEdge:D("Edge",n),isChrome:D("Chrome",n),isIE:D("IE",n),isOpera:D("Opera",n),isFirefox:D(be,n),isSafari:D("Safari",n)}},we={unknown:function(){return Se({current:void 0,version:ye.unknown()})},nu:Se,edge:d("Edge"),chrome:d("Chrome"),ie:d("IE"),opera:d("Opera"),firefox:d(be),safari:d("Safari")},xe="Windows",Oe="Android",Ne="Solaris",Ee="FreeBSD",Te="ChromeOS",Ce=function(e){var n=e.current;return{current:n,version:e.version,isWindows:_(xe,n),isiOS:_("iOS",n),isAndroid:_(Oe,n),isOSX:_("OSX",n),isLinux:_("Linux",n),isSolaris:_(Ne,n),isFreeBSD:_(Ee,n),isChromeOS:_(Te,n)}},Ae={unknown:function(){return Ce({current:void 0,version:ye.unknown()})},nu:Ce,windows:d(xe),ios:d("iOS"),android:d(Oe),linux:d("Linux"),osx:d("OSX"),solaris:d(Ne),freebsd:d(Ee),chromeos:d(Te)},De=function(e,r){return R(e,r).map(function(e){var n=ye.detect(e.versionRegexes,r);return{current:e.name,version:n}})},_e=function(e,r){return R(e,r).map(function(e){var n=ye.detect(e.versionRegexes,r);return{current:e.name,version:n}})},Re=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Ie=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(e){return I(e,"edge/")&&I(e,"chrome")&&I(e,"safari")&&I(e,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Re],search:function(e){return I(e,"chrome")&&!I(e,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(e){return I(e,"msie")||I(e,"trident")}},{name:"Opera",versionRegexes:[Re,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:P("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:P("firefox")},{name:"Safari",versionRegexes:[Re,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(e){return(I(e,"safari")||I(e,"mobile/"))&&I(e,"applewebkit")}}],Pe=[{name:"Windows",search:P("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(e){return I(e,"iphone")||I(e,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:P("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:P("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:P("linux"),versionRegexes:[]},{name:"Solaris",search:P("sunos"),versionRegexes:[]},{name:"FreeBSD",search:P("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:P("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],Le={browsers:d(Ie),oses:d(Pe)},je=ve(function(e,n){var r=Le.browsers(),t=Le.oses(),o=De(r,e).fold(we.unknown,we.nu),i=_e(t,e).fold(Ae.unknown,Ae.nu);return{browser:o,os:i,deviceType:function(e,n,r,t){var o=e.isiOS()&&!0===/ipad/i.test(r),i=e.isiOS()&&!o,u=e.isiOS()||e.isAndroid(),s=u||t("(pointer:coarse)"),a=o||!i&&u&&t("(min-device-width:768px)"),c=i||u&&!a,f=n.isSafari()&&e.isiOS()&&!1===/safari/i.test(r),l=!c&&!a&&!f;return{isiPad:d(o),isiPhone:d(i),isTablet:d(a),isPhone:d(c),isTouch:d(s),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:d(f),isDesktop:d(l)}}(i,o,e,n)}}(t.navigator.userAgent,function(e){return t.window.matchMedia(e).matches})),ke=me,Me=de,Ue=(je.get().browser.isIE(),function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n]}("element","offset"),"tiny-pageembed"),Fe=function(e){var n=e.dom().styleSheets;return Array.prototype.slice.call(n)},ze=function(e){var n=e.cssRules;return x(n,function(e){return function(e){return e.type===t.CSSRule.IMPORT_RULE}(e)?ze(e.styleSheet):function(e){return e.type===t.CSSRule.STYLE_RULE}(e)?[function(e){var n=e.selectorText,r=e.style.cssText;if(void 0===r)throw new Error("WARNING: Browser does not support cssText property");return{selector:n,style:r,raw:e.style}}(e)]:[]})},Be={},Ke={exports:Be};Y=Be,G=Ke,$=X=void 0,function(e){if("object"==typeof Y&&void 0!==G)G.exports=e();else if("function"==typeof X&&X.amd)X([],e);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).EphoxContactWrapper=e()}}(function(){return function i(u,s,a){function c(n,e){if(!s[n]){if(!u[n]){var r="function"==typeof $&&$;if(!e&&r)return r(n,!0);if(f)return f(n,!0);var t=new Error("Cannot find module '"+n+"'");throw t.code="MODULE_NOT_FOUND",t}var o=s[n]={exports:{}};u[n][0].call(o.exports,function(e){return c(u[n][1][e]||e)},o,o.exports,i,u,s,a)}return s[n].exports}for(var f="function"==typeof $&&$,e=0;e<a.length;e++)c(a[e]);return c}({1:[function(e,n,r){var t,u,o=(t=function(e){var n,r,t,o,i=[];for(t=0,o=(n=e.split(",")).length;t<o;t+=1)0<(r=n[t]).length&&i.push(u(r));return i},u=function(a){var e,n,c=a,f={a:0,b:0,c:0},l=[];function r(e){var n,r,t,o;if(e.test(c))for(r=0,t=(n=c.match(e)).length;r<t;r+=1)o=n[r],c=c.replace(o,Array(o.length+1).join("A"))}return e=function(e,n){var r,t,o,i,u,s;if(e.test(c))for(t=0,o=(r=c.match(e)).length;t<o;t+=1)f[n]+=1,i=r[t],u=c.indexOf(i),s=i.length,l.push({selector:a.substr(u,s),type:n,index:u,length:s}),c=c.replace(i,Array(s+1).join(" "))},r(/\\[0-9A-Fa-f]{6}\s?/g),r(/\\[0-9A-Fa-f]{1,5}\s/g),r(/\\./g),(n=/:not\(([^\)]*)\)/g).test(c)&&(c=c.replace(n,"     $1 ")),function(){var e,n,r,t,o=/{[^]*/gm;if(o.test(c))for(n=0,r=(e=c.match(o)).length;n<r;n+=1)t=e[n],c=c.replace(t,Array(t.length+1).join(" "))}(),e(/(\[[^\]]+\])/g,"b"),e(/(#[^\#\s\+>~\.\[:]+)/g,"a"),e(/(\.[^\s\+>~\.\[:]+)/g,"b"),e(/(::[^\s\+>~\.\[:]+|:first-line|:first-letter|:before|:after)/gi,"c"),e(/(:[\w-]+\([^\)]*\))/gi,"b"),e(/(:[^\s\+>~\.\[:]+)/g,"b"),c=(c=c.replace(/[\*\s\+>~]/g," ")).replace(/[#\.]/g," "),e(/([^\s\+>~\.\[:]+)/g,"c"),l.sort(function(e,n){return e.index-n.index}),{selector:a,specificity:"0,"+f.a.toString()+","+f.b.toString()+","+f.c.toString(),specificityArray:[0,f.a,f.b,f.c],parts:l}},{calculate:t,compare:function(e,n){var r,t,o;if("string"==typeof e){if(-1!==e.indexOf(","))throw"Invalid CSS selector";r=u(e).specificityArray}else{if(!Array.isArray(e))throw"Invalid CSS selector or specificity array";if(4!==e.filter(function(e){return"number"==typeof e}).length)throw"Invalid specificity array";r=e}if("string"==typeof n){if(-1!==n.indexOf(","))throw"Invalid CSS selector";t=u(n).specificityArray}else{if(!Array.isArray(n))throw"Invalid CSS selector or specificity array";if(4!==n.filter(function(e){return"number"==typeof e}).length)throw"Invalid specificity array";t=n}for(o=0;o<4;o+=1){if(r[o]<t[o])return-1;if(r[o]>t[o])return 1}return 0}});void 0!==r&&(r.calculate=o.calculate,r.compare=o.compare)},{}],2:[function(e,n,r){var t=e("specificity");n.exports={boltExport:t}},{specificity:1}]},{},[2])(2)});function Ve(r,e,n){var t=x(e,function(n){var e=q(r,n.selector);return b(e,function(e){!function(e,n){var r=e.dom();O(n,function(e,n){W(r,n,e)})}(e,function(r,t){var o={};return b(r,function(e){if(void 0!==r[e]){var n=t.dom().style;h(n,e)||(o[e]=r[e])}}),o}(n.raw,e))}),e});n&&b(t,function(e){!function(e,n){e.dom().removeAttribute(n)}(e,"class")})}function qe(e,n,r){function t(e){return-1!==e.selector.indexOf(",")}var o=x(S(e,t),function(n){var e=n.selector.split(",");return y(e,function(e){return{selector:e.trim(),raw:n.raw}})}),i=S(e,function(e){return!t(e)}).concat(o);i.sort(function(e,n){return tn.compare(e.selector,n.selector)}).reverse(),Ve(n,i,r)}function We(e,n,r,t){var o=Fe(e),i=H(o).map(function(e){var n=e.selector;return{selector:r.hasOwnProperty(n)?r[n]:n,raw:e.raw}});qe(i,n,t)}function He(e,n,r,t){var o=Fe(e),i=H(o),u=S(i,function(e){return function(e,n){return function(e,n,r){return""===n||!(e.length<n.length)&&e.substr(r,r+n.length)===n}(e,n,0)}(e.selector,r)});qe(u,n,t)}function Xe(e,n,r,t){var o=Fe(e),i=H(o),u=S(i,function(e){return h(r,e.selector)});qe(u,n,t)}function Ye(e,n,r){0<r.trim().length&&N(e,n,r)}function Ge(e,n,r,t){var o=function(e){var n=ee.fromTag("div");return N(n,"contentEditable","false"),U(n,Ue),"inline"!==e.size&&U(n,e.size),n}(e),i=ee.fromTag("iframe");return Ye(i,"src",e.source.value),Ye(i,"title",e.title),Ye(i,"name",e.name),Ye(i,"longdesc",e.descriptionUrl.value),function(e,n){return le(e,n)?Q.from(e[n]):Q.none()}(e,"dimensions").each(function(e){Ye(i,"width",e.width),Ye(i,"height",e.height)}),e.showBorder||N(i,"frameborder","0"),N(i,"scrolling",e.scrollbar?"yes":"no"),V(o,i),n()&&function(e,n,r){var t=ee.fromTag("div");V(t,n);var o=e();r().fold(function(){un.inlinePrefixedStylesKeepClasses(o,t,".tiny-pageembed")},function(e){var n=y(e,function(e){return"."+e});un.inlineSelectorsStylesKeepClasses(o,t,n)});var i=q(t,"*[style]");b(i,function(e){var n=E(e,"style");N(e,"data-mce-style",n)})}(r,o,t),function(e){var n=ee.fromTag("div"),r=ee.fromDom(e.dom().cloneNode(!0));return V(n,r),function(e){return e.dom().innerHTML}(n)}(o)}function $e(e){return void 0!==e.tiny_pageembed_classes}function Je(e){return e.tiny_pageembed_classes}function Qe(t){var n=[{text:"Inline Value",value:"inline"}].concat($e(t.settings)?Je(t.settings):sn),r=function(e){return{title:"Insert/Edit Iframe",body:{type:"tabpanel",tabs:[{title:"General",items:w([[{name:"source",type:"urlinput",filetype:"media",label:"Source"}],1<n.length?[{label:"Size",type:"selectbox",name:"size",items:n}]:[],"inline"===e.size?[{type:"sizeinput",name:"dimensions",label:"Constrain proportions",constrain:!0}]:[]])},{title:"Advanced",items:[{name:"name",type:"input",label:"Name"},{name:"title",type:"input",label:"Title"},{name:"descriptionUrl",type:"urlinput",label:"Long description URL"},{type:"label",label:"Border",items:[{type:"checkbox",name:"showBorder",label:"Show iframe border"}]},{type:"label",label:"Scrollbar",items:[{type:"checkbox",name:"scrollbar",label:"Enable scrollbar"}]}]}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"submit",text:"Save",primary:!0}],onChange:function(e,n){"size"===n.name&&(e.redial(r(e.getData())),e.focus("size"))},onSubmit:function(e){var n=e.getData();if(0<n.source.value.length){var r=Ge(n,function(){return function(e){return!!e.tiny_pageembed_inline_styles}(t.settings)},function(){return ee.fromDom(t.getDoc())},function(){if($e(t.settings)){var e=Je(t.settings);return Q.some(y(e,function(e){return e.value}))}return Q.none()});t.insertContent(r),t.nodeChanged()}e.close()},initialData:e}},e=t.selection.getNode(),o=K(e,{source:{value:""},size:"inline",dimensions:{width:"350px",height:"260px"},name:"",title:"",descriptionUrl:{value:""},showBorder:!0,scrollbar:!1},n);t.windowManager.open(r(o))}function Ze(r){r.ui.registry.addToggleButton("pageembed",{icon:"embed-page",tooltip:"Embed iframe",onAction:function(e){Qe(r)},onSetup:function(n){function e(e){return n.setActive(!r.readonly&&e.element.classList.contains(Ue))}return r.on("nodechange",e),function(){return r.off("nodechange",e)}}}),r.ui.registry.addMenuItem("pageembed",{text:"Insert/edit iframe",icon:"embed-page",onAction:function(e){Qe(r)}})}function en(e){b(e,function(e){if(B(e)){var n=new tinymce.html.Node("span",1);n.attr("class","mce-shim"),n.attr("data-mce-bogus","1"),e.append(n),e.attr("contenteditable","false")}})}function nn(e){b(e,function(e){B(e)&&e.attr("contenteditable",null)})}function rn(n,e){if(s(tinymce,"5.0.0"))return console.error("The pageembed plugin requires at least 5.0.0 of TinyMCE"),{};!function(e){e.on("click keyup touchend",function(){Q.from(e.selection.getNode()).map(ee.fromDom).each(function(e){z(e)&&(!function(e,n){var r=e.dom();return!(!r||!r.hasAttribute)&&r.hasAttribute(n)}(e,"data-mce-selected")||N(e,"data-mce-selected","2"))})}),e.on("PreInit",function(){e.parser.addNodeFilter("div",en),e.serializer.addNodeFilter("div",nn)})}(n),Ze(n);var r=function(e){return e.tiny_pageembed_css_url}(n.settings),t=r||e+"/css/empa30.css";return n.on("init",function(e){n.dom.loadCSS(t)}),{}}var tn=Ke.exports.boltExport,on={inlineStyles:function(e,n,r){We(e,n,r,!0)},inlineStylesKeepClasses:function(e,n,r){We(e,n,r,!1)},inlinePrefixedStyles:function(e,n,r){He(e,n,r,!0)},inlinePrefixedStylesKeepClasses:function(e,n,r){He(e,n,r,!1)},inlineSelectorsStyles:function(e,n,r){Xe(e,n,r,!0)},inlineSelectorsStylesKeepClasses:function(e,n,r){Xe(e,n,r,!1)}},un={inlineStyles:on.inlineStyles,inlineStylesKeepClasses:on.inlineStylesKeepClasses,inlinePrefixedStyles:on.inlinePrefixedStyles,inlinePrefixedStylesKeepClasses:on.inlinePrefixedStylesKeepClasses,inlineSelectorsStyles:on.inlineSelectorsStyles,inlineSelectorsStylesKeepClasses:on.inlineSelectorsStylesKeepClasses},sn=[{text:"Responsive - 21x9",value:"tiny-pageembed--21by9"},{text:"Responsive - 16x9",value:"tiny-pageembed--16by9"},{text:"Responsive - 4x3",value:"tiny-pageembed--4by3"},{text:"Responsive - 1x1",value:"tiny-pageembed--1by1"}];tinymce.PluginManager.add("pageembed",rn)}(window);