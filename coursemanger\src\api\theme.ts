import HTTP from '@/api/index';
import { AxiosRequestConfig } from 'axios';

const http = (url: string, config?: AxiosRequestConfig) =>
  HTTP(url, config)
    .then(res => res.data)
    .catch(err => err);

namespace themeService {
  export const fetchSysSetting = () =>
    http(`/unifiedplatform/v1/setting/no/authorization`);

  /**
   * 获取全局参数配置
   */
  export const fetchParameterConfig = () => {
    return http(
      `/unifiedplatform/v1/app/app/module/parameter/list?moduleCode=MyTeaching`,
    );
  };

  /**
   * 获取全局参数配置
   */
  export const fetchParameterConfigs = () => {
    return http(`/unifiedplatform/v1/app/app/module/parameter/list`, {
      method: 'post',
      data: ['mooc_kcgl', 'spoc_kcgl', 'training_kcgl','map_kcgl', 'wdkc', 'zyzx', 'microcourse_kcgl', 'classreview_kcgl', 'kczx', 'LearningPortal', 'LearningPortalCourse','coursemap','workbench'],
    });
  };
  export const fetchUserPermissionsV2 = () =>
    http(`/unifiedplatform/v1/user/rolemodule`);
    
  /**
   * 获取 资源管理 模块的全局参数
   */
  export const fetchRmanGlobalParam = () =>
  http(`/unifiedplatform/v1/app/app/module/parameter/list?moduleCode=zyzx`);

  /**
   * 获取 资源管理 模块的全局参数
   */
  export const fetchCurrentEnv = () =>
  http(`/unifiedplatform/v1/app/app/module/parameter/list?moduleCode=LearningPortalHomePage`);

  // 获取头部导航
  export const fetchHeaderList = () =>
    http(`/unifiedplatform/v1/navigationsettings/user/navigation?type=2`)

  export function reqMessageList(data: any) {
    return http(`/unifiedplatform/v1/message/notify/self/list`,{
      method: 'GET',
      params: data
    });
  }
  
  export function reqUnreadMessageCount(data?: any) {
    return http(`/unifiedplatform/v1/message/notify/unread/count`,{
      method: 'GET',
      params: data,
    });
  }
  
  export function updateMessageRead(data: any) {
    return http(`/unifiedplatform/v1/message/read`,{
      method: 'GET',
      params: data
    });
  }
  export function statData(params: any) {
    return http(`/unifiedplatform/v1/user/browse/record`,{
      method: 'POST',
      params
    });
  }
}

export default themeService;
