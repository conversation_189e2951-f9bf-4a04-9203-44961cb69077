import React, { memo, useState } from 'react';
import { Spin } from 'antd';
import Entity from '@/components/entity/entity';
import VideoPauseDemo from '@/components/XgVideo/videoPauseDemo';
import { snapdom } from '@zumer/snapdom';

export interface ResourcePreviewProps {
  loading: boolean;
  previewEntity: any;
  visible: number;
  currentvideotype: number;
  onFinish?: () => void;
  type?: number;
  selectNode?: any;
  centerednode?: any;
}

// 使用React.memo来记忆组件，减少不必要的重渲染
const ResourcePreview = ({
  previewEntity,
  onFinish,
  type = 1,
  selectNode,
  centerednode
}: ResourcePreviewProps) => {
  
  const [knowledgePointModal, setKnowledgePointModal] = useState<any>({
    open: false,
    loading: false,
    data: null,
  });

  // 重置视频卡片数据，避免卡片数据混乱
  const resetVedioCardData = (params?: any) => {
    setKnowledgePointModal({
      open: false,
      loading: false,
      data: null,
      ...params
    })
  }

  const pauseCard = async () => {
    const el:any = document.getElementById('previewVideo');
    const blob = await snapdom.toBlob(el);
    debugger
    // /**
    //  * 当前data为演示假数据，需根据暂停的这一帧页面，通过后端接口匹配相应知识点展示，此处仅为演示效果，按需修改。
    //  */
    // resetVedioCardData({ loading: true }); // 请求前：重置清空卡片数据；
    // // 请求数据：
    // setKnowledgePointModal({
    //   open: true,
    //   loading: false,
    //   data: {
    //     selectNodeName: selectNode?.label,
    //     knowLedgePoints: [
    //       { name: '知识点1', id: "5526603a3aaa419bb9abf80ab7d54cad" },
    //       { name: '知识点2', id: "1751c38936ba43cfa42c702780327425" },
    //       { name: '知识点3', id: "567334a11ccb4f17b899931a18b06eac" },
    //       // { name: '知识点4', id: "5526603a3aaa419bb9abf80ab7d54cad" },
    //       // { name: '知识点5', id: "1751c38936ba43cfa42c702780327425" },
    //       // { name: '知识点6', id: "567334a11ccb4f17b899931a18b06eac" },
    //     ]
    //   }
    // })
  }

  if(previewEntity?.src){
    return (
          <div className="entity-preview" style={{ width: '100%' }}>
            <VideoPauseDemo knowledgePointModal={knowledgePointModal} centerednode={centerednode} canclePauseCard={resetVedioCardData} />
            <div className="video-wrap">
              <Entity
                type={previewEntity.type}
                src={previewEntity.src}
                finishStatus={previewEntity.finishStatus}
                id={'previewVideo'}
                selectNode={selectNode}
                centerednode={centerednode}
                isAutoplay={false}
                pip={false}
                cover={previewEntity.cover}
                knowledge={previewEntity.knowledge}
                onListener={(e: any) => {
                  if (e === 'ended') {
                    onFinish?.();
                  }else if(e =='pause'){
                    pauseCard()
                  }else if(e == 'continueplaying'){
                    resetVedioCardData()
                  }
                }}
              />
            </div>
          </div>
    );
  }else{
    return null
  }
}

// 自定义比较函数，只有当previewEntity.src或previewEntity.knowledge发生变化时才重新渲染
const arePropsEqual = (prevProps: ResourcePreviewProps, nextProps: ResourcePreviewProps) => {
  // 如果src不同，需要重新渲染
  if (prevProps.previewEntity?.src !== nextProps.previewEntity?.src) {
    return false;
  }

  // 如果knowledge不同，需要重新渲染
  if (JSON.stringify(prevProps.previewEntity?.knowledge) !== JSON.stringify(nextProps.previewEntity?.knowledge)) {
    return false;
  }

  // 其他属性变化不触发重新渲染
  return true;
};

export default memo(ResourcePreview, arePropsEqual);
