/*
 * @Author: 李武林
 * @Date: 2022-04-20 11:44:27
 * @LastEditors: 李武林
 * @LastEditTime: 2022-06-08 19:09:30
 * @FilePath: \coursemanger\src\api\banner.ts
 * @Description: 
 * 
 * Copyright (c) 2022 by 李武林/索贝数码, All Rights Reserved. 
 */
import HTTP from './index'

namespace bannerApis {
    export function getBanner() {
        return HTTP.get(`/learn/v1/configure/data/get/banner/config`).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            return error
        })
    }
    export function insertBanner(data: Banner.IInsertBanner) {
        return HTTP.post(`/learn/v1/configure/data/insert/banner/config`, data).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            return error
        })
    }
    export function updateBanner(data: Banner.IInsertBanner) {
        return HTTP.post(`/learn/v1/configure/data/update/banner/config`, data).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            return error
        })
    }
    export function deleteBanner(data: string[]) {
        return HTTP.post(`/learn/v1/configure/data/batch/delete/banner/config`, data).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            return error
        })
    }
    export function updateReleaseBanner(release_status: number, data: string[]) {
        return HTTP.post(`/learn/v1/configure/data/batch/update/banner/release/status?release_status=${release_status}`, data).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            return error
        })
    }

    export function getbymoduleCode(code: string) {
        return HTTP.get(`/unifiedplatform/v1/app/app/module/parameter/list?moduleCode=${code}`).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            return error
        })
    }

}

export default bannerApis;
