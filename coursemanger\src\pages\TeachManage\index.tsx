import React from "react";
import "./index.less";
import teach1 from '/src/assets/imgs/HomeNav/teach1.png'
import teach2 from '/src/assets/imgs/HomeNav/teach2.png'
import teach3 from '/src/assets/imgs/HomeNav/teach3.png'
import teach4 from '/src/assets/imgs/HomeNav/teach4.png'
import teach5 from '/src/assets/imgs/HomeNav/teach5.png'
import teach6 from '/src/assets/imgs/HomeNav/teach6.png'
import teach7 from '/src/assets/imgs/HomeNav/teach7.png'
import teach8 from '/src/assets/imgs/HomeNav/teach8.png'
import {IconFont} from "@/components/iconFont";
import {LeftOutlined} from "@ant-design/icons";
import Header from "@/components/Header";
const cards = [
  {
    title: "平台运行",
    desc: "模块文字介绍模块文字介绍模块文字",
    icon: teach1,
    href: '/unifiedplatform/#/screen/runningCondition'
  },
  {
    title: "资源建设",
    desc: "模块文字介绍模块文字介绍模块文字",
    icon: teach2,
    href: '/unifiedplatform/#/screen/resourceConstruction'
  },
  {
    title: "教学运行",
    desc: "模块文字介绍模块文字介绍模块文字",
    icon: teach3,
    href: '/unifiedplatform/#/screen/hotKeywords'
  },
  {
    title: "教学巡课",
    desc: "模块文字介绍模块文字介绍模块文字",
    icon: teach4,
    href: '/supervision/#/online/supervision'
  },
  {
    title: "任务监控",
    desc: "模块文字介绍模块文字介绍模块文字",
    icon: teach5,
    href: '/unifiedplatform/#/cockpit/screen/record?hideLeft=1&hideHeader=1'

  },
  {
    title: "视频监控",
    desc: "实时音视频数据监测面板，流状态监测",
    icon: teach6,
    href: '/unifiedplatform/#/overview/home'
  },
  {
    title: "培养方案",
    desc: "培养方案完整管理、可视化展示及成效分析",
    icon: teach7,
    href: '/pyfa/#/train/scheme'

  },
  {
    title: "系统管理",
    desc: "用户管理，系统设置及页面自定义",
    icon: teach8,
    href: '/unifiedplatform/#/basic/user/teacher'

  },
];

export default function TeachingManagement() {
  function goToReturn() {
    console.log(111)
    location.href = '/learn/workbench/#/newHome'
  }
  return (
    <>
      <Header />
    <div className="teaching-mgmt-bg">
      <div style={{ position: "relative" }}>
        <div className="teaching-mgmt-title" onClick={goToReturn}><LeftOutlined />教学管理</div>
      <div className="teaching-mgmt-grid">
        {cards.map((item, idx) => (
          <a href={item.href} target="_blank" key={idx}>
            <div className="teaching-mgmt-card">
              <div className="icon">
                <img src={item.icon} />
              </div>
              <div className="title">{item.title}</div>
              <div className="desc">{item.desc}</div>
            </div>
          </a>

        ))}
      </div>
      </div>
    </div>
    </>
  );
}
