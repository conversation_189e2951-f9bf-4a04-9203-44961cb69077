/// 读取 proxy.env 文件 写入代理配置,不存在则使用默认配置

import { defineConfig } from 'umi';
import * as dotenv from 'dotenv';
import fs from 'node:fs/promises';

import path from 'node:path';
import proxy from './config.proxy';

const ALL_KEY = 'ALL';
/** 代理配置 */
const proxyConfig = proxy as Record<string, any>;

type ProxyType = ReturnType<typeof defineConfig>['proxy'];

const result = dotenv.config({
  path: path.resolve(__dirname, '../proxy.env'),
}).parsed;

const isReadConfigError = !result || result?.error;
if (isReadConfigError) {
  //
  console.log('proxy.env not found.use default proxy url');
} else {
  if (result[ALL_KEY]) {
    console.log('use global proxy url:', result[ALL_KEY]);
    Object.entries(proxyConfig).forEach(([key, value]) => {
      proxyConfig[key] = {
        ...value,
        target: result[ALL_KEY],
      };
    });
  }
}


if (!isReadConfigError) {
  Object.keys(result)
    .filter(key => key !== ALL_KEY)
    .forEach(key => {
      const value = result[key];
      proxyConfig[`/${key}`] = {
        ...proxyConfig[key],
        target: value,
      };
      console.log(`${key} -> ${value}`);
    });
}

export { proxyConfig };
function debounce<T extends (...args: any) => void>(
  func: T,
  wait = 100,
  immediate?: boolean,
): T {
  let timer: any;
  return ((...args: any[]) => {
    if (timer) clearTimeout(timer);
    if (immediate) {
      const callNow = !timer;
      timer = setTimeout(() => {
        timer = null;
      }, wait);
      if (callNow) func(...args);
    } else {
      timer = setTimeout(() => {
        func(...args);
      }, wait);
    }
  }) as T;
}
