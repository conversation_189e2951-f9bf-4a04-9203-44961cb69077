import HTTP from './index';
export namespace GradeManageService {
  export function gradePageList(params: any) {
    return HTTP(`/learn/v1/teaching/course/get/student/score`,
      {
        method: 'get',
        params,
      },
    );
  }
  export function gradeSave(data: any) {
    return HTTP(`/learn/v1/teaching/course/save/student/grades`,
      {
        method: 'post',
        data,
      },
    );
  }
  export function downloadTemplate(params: any) {
    return HTTP(`/learn/v1/teaching/download/grades/template`,
      {
        method: 'get',
        params,
        responseType: 'blob',
      },
    );
  }
  export function importTemplate(data: any,params:any) {
    return HTTP(`/learn/v1/teaching/grades/import`,
      {
        method: 'post',
        data,
        params,
      },
    );
  }
  export function getThirdList(data: any) {
    return HTTP(`/learn/v1/third-api/tcmCourse/sync/third/grades`,
      {
        method: 'post',
        data,
      },
    );
  }
}
