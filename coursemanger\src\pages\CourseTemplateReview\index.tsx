import { addLog, reqAllPlate, reqAllSubject } from '@/api/addCourse';
import {
  courseOverrule,
  coursePass,
  getCourseListNew,
  queryColleges,
} from '@/api/course';
import courseTemplate from '@/api/courseTemplate'
import CourseBlock from '@/components/CourseBlockNew/CourseBlock';
import TurnThePageDataItem from '@/components/formItemBox/turnThePageDataItem';
import { IconFont } from '@/components/iconFont';
import LoggerModal from '@/components/LoggerModal';
import MobileSearch from '@/components/SearchForm/mobileSearch';
import useLocale from '@/hooks/useLocale';
import usePermission from '@/hooks/usePermission';
import { COURSE_TYPE } from '@/permission/moduleCfg';
import { ClockCircleOutlined, ReloadOutlined } from '@ant-design/icons';
import {
  Button,
  Checkbox,
  Empty,
  Form,
  Image,
  Input,
  message,
  Modal,
  Pagination,
  Popover,
  Select,
  Space,
  Table,
  Tooltip,
  TreeSelect,
} from 'antd';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { ColumnsType } from 'antd/lib/table';
import React, { FC, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'umi';
import './index.less';

const { Option } = Select;
interface ISemesters {
  id: string;
  code: string;
  name: string;
}

const MyReview: FC = () => {
  const { t } = useLocale();
  const [form] = Form.useForm();
  const [query, setQuery] = useState<any>({
    page: 1,
    size: 24,
    courseType: -1,
    // publishStatus: 2
    approvalStatus: null,
  });
  const [subjectList, setSubjectList] = useState<any[]>([]);
  const [courseTypeList, setCourseTypeList] = useState<any[]>([]);

  const [indeterminate, setIndeterminate] = useState<boolean>(false);
  const [checkAll, setCheckAll] = useState<boolean>(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);

  const [dataSource, setDataSource] = useState<any[]>([]);
  const [getDataTable, setDataTable] = useState<any[]>([]);
  const [myOrShare, setMyOrShare] = useState<boolean>(true);
  const [modeSwitch, setModeSwitch] = useState<boolean>(true); // 视图切换
  const { mobileFlag } = useSelector<{ config: any }, any>(
    ({ config }) => config,
  );

  const [operatMenuVisible, setOpreatMenuVisible] = useState<boolean>(false);
  const [total, setTotal] = useState<number>(0);
  const [tableLoading, setTableLoading] = useState<boolean>(false);
  useEffect(() => {
    console.log(location.pathname)
    console.log(location.pathname === '/coursetemplate/myReview')
    setMyOrShare(location.pathname === '/coursetemplate/myReview');
  }, []);
  const columns: ColumnsType<any> = [
    {
      title: '',
      dataIndex: 'check',
      key: 'check',
      align: 'center',
      width: 50,
      fixed: 'left',
      render: (_text: any, record: any) => (
        <Checkbox value={record.contentId_} />
      ),
    },
    {
      title: t('课程资源封面'),
      dataIndex: 'cover',
      align: 'center',
      key: 'cover',
      width: 100,
      render: (text: any) => <Image width={80} src={text} />,
    },
    {
      title: t('课程资源名称'),
      dataIndex: 'name_',
      align: 'center',
      key: 'name',
      width: 150,
      ellipsis: {
        showTitle: true,
      },
    },
    {
      title: t('教师'),
      dataIndex: 'teacher_names',
      align: 'center',
      key: 'teacher_names',
      ellipsis: {
        showTitle: true,
      },
      width: 200,
      render: (teacher: any): React.ReactNode => {
        return teacher?.join('，');
      },
    },
    {
      title: t('开课学院'),
      dataIndex: 'collegeName',
      key: 'collegeName',
      align: 'center',
      ellipsis: {
        showTitle: false,
      },
      width: 120,
      render: (text: any): React.ReactNode => {
        let college = text?.join('，');
        return (
          <Tooltip placement="topLeft" title={college} mouseEnterDelay={0.8}>
            {college}
          </Tooltip>
        );
      },
    },
    {
      title: t('发布板块'),
      dataIndex: 'plate',
      align: 'center',
      ellipsis: true,
      render: (text: any) => {
        return text?.join('，');
      },
      width: 120,
    },
    {
      title: t('专题栏目'),
      dataIndex: 'classificationName',
      align: 'center',
      ellipsis: true,
      render: (text: any) => {
        return text?.join('，');
      },
      width: 120,
    },
    {
      title: t('所属学科'),
      dataIndex: 'subjectName',
      align: 'center',
      ellipsis: true,
      width: 100,
      render: (text: any, record: any) => {
        return text?.join('，');
      },
    },
    {
      title: t('创建人'),
      dataIndex: 'createUser_',
      align: 'center',
      width: 120,
      render: (text: any) => {
        return text;
      },
    },
    {
      title: t('创建时间'),
      dataIndex: 'createDate_',
      align: 'center',
      width: 150,
    },
    {
      title: t('操作'),
      dataIndex: 'action',
      align: 'center',
      width: 160,
      key: 'action',
      fixed: 'right',
      render: (text, record: any) => {
        return (
          <span>
            <Tooltip title={t('预览')}>
              <Button
                type="text"
                icon={<IconFont type="iconviews" />}
                onClick={() => {
                  if (record.courseType === 0) {
                    window.open(
                      `/learn/course/micro/${record.contentId_}?preview=1`,
                    );
                  } else {
                    window.open(
                      `/learn/course/preview/${record.courseType == 1 ? 'mooc' : 'spoc'
                      }/${record.contentId_}?preview=1&show=1`,
                    );
                  }
                  addLog({
                    courseIds: [record.contentId_],
                    courseType: record.courseType,
                    operateType: 0,
                  });
                }}
              />
            </Tooltip>
            {record.courseType === 1 && (
              <Tooltip title={t('编辑')}>
                <IconFont
                  style={{ margin: '0 6px' }}
                  type="iconedit"
                  onClick={() => {
                    window.open(
                      `#/editcourse/moocbaseInfo?id=${record.contentId_}&type=mooc&sm=${record.course_semester_id}`,
                    );
                  }}
                />
              </Tooltip>
            )}
            <Tooltip title={t('发布')}>
              <Button
                type="text"
                icon={<IconFont type="iconrelease" />}
                onClick={() => {
                  setOneOrBatch(true);
                  setVisible(true);
                  setOperationData(record);
                  setIsRelease(true);
                  if (record.plate_id) {
                    setPlateId(record.plate_id[0]);
                    let arr = allPlate.filter(
                      (item: any) => item.id === record.plate_id[0],
                    );
                    let themarr = JSON.parse(arr[0].thematic_ids);
                    setAllSubject(
                      themarr?.filter((item: any) => item.is_enable),
                    );
                    setSubjectId(record.classification);
                  } else {
                    setPlateId(null);
                    setAllSubject(subjectData);
                    setSubjectId(record.classification || []);
                  }
                }}
              />
            </Tooltip>
            <Tooltip title={t('驳回')}>
              <Button
                type="text"
                icon={<IconFont type="iconrecall" />}
                onClick={() => {
                  setOneOrBatch(true);
                  setVisible(true);
                  setOperationData(record);
                  setIsRelease(false);
                }}
              />
            </Tooltip>
            <Tooltip title={t('日志')}>
              <Button
                type="text"
                icon={<ClockCircleOutlined />}
                onClick={() => handleLogShow(record)}
              />
            </Tooltip>
          </span>
        );
      },
    },
  ];
  const [deleteVisible,setDeleteVisible] = useState(false);
  const [isRelease, setIsRelease] = useState<boolean>(false);
  const [visible, setVisible] = useState<boolean>(false);
  const [oneOrBatch, setOneOrBatch] = useState<boolean>(false);
  const [operationData, setOperationData] = useState<any>({});
  const dispatch = useDispatch();
  // 所有板块
  const [allPlate, setAllPlate] = useState<any[]>([]);
  // 所有专题
  const [allsubject, setAllSubject] = useState<any[]>([]);
  // 板块id
  const [plateId, setPlateId] = useState<any>(null);
  // 当前选择的专题id
  const [subjectId, setSubjectId] = useState<any>([]);
  // 当前选择的板块名称
  const [plateName, setPlateName] = useState<any>(null);
  // 驳回原因
  const [rejectReason, setRejectReason] = useState<string>('');
  // 所有的专题
  const [subjectData, setSubjectData] = useState<any[]>([]);
  const [btn_list, setBtn_list] = useState<any>([]);
  const { getPermission } = usePermission();
  const { parameterConfig } = useSelector<
    { global: any },
    { buttonPermission: string[]; parameterConfig: any; permission: any }
  >(state => state.global);
  useEffect(() => {
    queryCourseSearch();
    getAllPlate();
  }, []);
  useEffect(() => {
    //按钮列表
    let btn_list1: any = [];
    btn_list1.push({
      title: t('通过'),
      disabled: !selectedRowKeys.length,
      func: () => {
        setOneOrBatch(false);
        setVisible(true);
        setIsRelease(true);
      },
      dom: (
        <Button
          type="text"
          ghost
          icon={<IconFont type="iconrelease" />}
          onClick={() => {
            setOneOrBatch(false);
            setVisible(true);
            setIsRelease(true);
          }}
          disabled={!selectedRowKeys.length}
        >
          {t('通过')}
        </Button>
      ),
    });
    btn_list1.push({
      title: t('驳回'),
      disabled: !selectedRowKeys.length,
      func: () => {
        setOneOrBatch(false);
        setVisible(true);
        setIsRelease(false);
      },
      dom: (
        <Button
          type="text"
          ghost
          icon={<IconFont type="iconrecall" />}
          onClick={() => {
            setOneOrBatch(false);
            setVisible(true);
            setIsRelease(false);
          }}
          disabled={!selectedRowKeys.length}
        >
          {t('驳回')}
        </Button>
      ),
    });
    setBtn_list(btn_list1);
  }, [selectedRowKeys]);
  useEffect(() => {
    fetchDataList();
  }, [query]);

  // 查询所有板块
  const getAllPlate = () => {
    reqAllPlate().then(res => {
      if (res.status === 200) {
        setAllPlate(res.data);
      }
    });
    reqAllSubject().then((res: any) => {
      if (res.status === 200) {
        setSubjectData(res.data?.filter((item: any) => item.is_enable));
        setAllSubject(res.data?.filter((item: any) => item.is_enable));
      }
    });
  };

  // * 查询下拉条件列表
  const queryCourseSearch = () => {
    queryColleges().then(res => {
      if (res.status === 200) {
        setCourseTypeList(res.data?.classificationType ?? []);
        setSubjectList(res.data?.subjectEducation ?? []);
      }
    });
  };
  const fetchDataList = () => {
    setTableLoading(true);
    const approvalStatus = form.getFieldValue('approvalStatus');
    console.log(query,'query')
    courseTemplate.getMyReview({
      ...query,
      teacher: query.teacher ? [query.teacher] : null,
      subjectId: query.subjectId ? [query.subjectId] : null,
      // classificationId: query.classificationId ? [query.classificationId] : null
      // approvalStatus: query.approvalStatus ?? 0
      approvalStatus:
        approvalStatus == 0
          ? null
          : approvalStatus == undefined
            ? 1
            : approvalStatus,
      modelKey: 'course_template_publish'
    })
      .then(res => {
        dispatch({
          type: 'config/changeShowLoading',
          payload: {
            value: false,
          },
        });
        setTableLoading(false);
        if (res.message === 'OK') {
          const { results = [], total } = res.data;
          setTotal(total);
          console.log('results---', results);
          setDataSource(results);
          let arr: any = results.map((item: any) => {
            return item.entityData || [];
          });
          console.log('arr',arr)
          setDataTable(arr);
          if (query.approvalStatus === 1) {
            dispatch({
              type: 'microCourse/updateState',
              payload: {
                myReviewCount: total,
              },
            });
          }
        }
      })
      .catch(() => setTableLoading(false));
  };

  // * form操作
  const handleSearch = (value: any) => {
    console.log('value', value)
    setQuery({
      ...query,
      page: 1,
      ...value,
    });
    console.log(query,'query')
    if (value.approvalStatus == 2) {
      setBtn_list([]);
    }
  };
  const handleReset = () => {
    form.resetFields();
    setQuery({
      page: 1,
      size: query.size,
      courseType: -1,
      // publishStatus: 2,
      approvalStatus: 1,
    });
  };
  const onTeacherChange = (value: any): void => {
    form.setFieldsValue({ teacher: value });
  };

  // * checkbox操作
  const handleCheckAllChange = (e: CheckboxChangeEvent) => {
    setSelectedRowKeys(
      e.target.checked
        ? dataSource.filter(item => item.entityData).map((item: any) => item.entityData.contentId_)
        : [],
    );

    setSelectedRows([...dataSource]);
    setIndeterminate(false);
    setCheckAll(e.target.checked);
  };
  const handleCheckChange = (check: any[]) => {
    setSelectedRowKeys(check);
    setSelectedRows(
      dataSource.filter(item => item.entityData).filter((d: any) => check.includes(d.entityData.contentId_)),
    );
    setIndeterminate(!!check.length && check.length < dataSource.length);
    setCheckAll(check.length === dataSource.length);
  };

  const handlePageChange = (page: any, size: any) => {
    setQuery({ ...query, page, size });
  };
   function cancle() {
     courseTemplate.releasestatus2({ contentId: operationData.contentId_,state: 2,}).then(res => {
       if (res.status === 200) {
         window.dispatchEvent(new Event('refreshReviewCount'));
         message.success('取消成功')
         setDeleteVisible(false)
         fetchDataList()
       }
     }).catch(() => {
       setDeleteVisible(false)
     })
   }
  const handleReleaseOrReject = () => {
    let arr = [];
    setVisible(false);
    if (oneOrBatch) {
      console.log('单选')
        arr.push(
          courseTemplate.releasestatus2(Object.assign({ contentId: operationData.contentId_,state: isRelease ? 1 : 0,}, !isRelease ? { reasonRejection: rejectReason  }:{}))
        )
    } else {
      console.log('多选')
      selectedRows.map(i => {
        console.log(i)
        arr.push(courseTemplate.releasestatus2(Object.assign({ contentId: i.entityData.contentId_,state: isRelease ? 1 : 0,}, !isRelease ? { reasonRejection: rejectReason  }:{})))
      });
    }
    if (arr.length > 0) {
      console.log(arr.length)
      Promise.all(arr)
        .then((res: any) => {
          if (res.every((item: any) => item.status === 200)) {
            window.dispatchEvent(new Event('refreshReviewCount'));
            message.success(
              `${t('课程')}${isRelease ? t('共享') : t('驳回')}${t('成功')}`,
            );
          } else {
            message.error(
              `${t('课程')}${isRelease ? t('共享') : t('驳回')}${t('失败')}`,
            );
          }
        })
        .finally(() => {
          fetchDataList();
          setSelectedRowKeys([]);
          setSelectedRows([]);
        });
    }
  };

  const [logOpen, setLogOpen] = useState<boolean>(false);

  const handleLogShow = (item: any) => {
    setLogOpen(true);
    setOperationData(item);
  };
  return (
    <div className="my-review-container">
      {mobileFlag ? (
        <MobileSearch
          resourceSearch={handleSearch}
          selected={'myReview'}
          form={form}
          reset={handleReset}
        />
      ) : (
        <Form layout="inline" name="basic" form={form} onFinish={handleSearch}>
          <Form.Item name="name">
            <Input
              placeholder={t('请输入课程名')}
              autoComplete="off"
              style={{ width: 250 }}
              onPressEnter={() => {
                form.submit();
              }}
            />
          </Form.Item>
          <Form.Item name="teacher">
            <TurnThePageDataItem
              message={t('请选择教师')}
              type={0}
              value={undefined}
              onTeacherChange={onTeacherChange}
            />
          </Form.Item>
          {/* <Form.Item name="classificationId">
            <Select
              placeholder={t("请选择课程分类")}
              allowClear
              style={{ width: 180 }}>

              {courseTypeList.map((item: any) =>
                <Option key={item.code} value={item.code}>{item.name}</Option>)}

            </Select>
          </Form.Item> */}
          <Form.Item name="approvalStatus">
            <Select
              defaultValue={1}
              placeholder={t('请选择审核状态')}
              allowClear
              style={{ width: 180 }}
              options={[
                { label: '全部', value: 0 },
                { label: '待审核', value: 1 },
                { label: '已审核', value: 2 },
              ]}
              onChange={() => form.submit()}
            ></Select>
          </Form.Item>
          <Form.Item name="subjectId">
            <TreeSelect
              placeholder={t('请选择学科')}
              allowClear
              treeData={subjectList}
              showArrow
              // treeNodeFilterProp={{}}
              fieldNames={{
                label: 'categoryName',
                value: 'categoryCode',
                children: 'children',
              }}
              style={{ width: '180px' }}
            />
          </Form.Item>
          <div className="reset-wrp" onClick={handleReset}>
            <span>{t('清空')}</span>
            <ReloadOutlined />
          </div>
          <Form.Item>
            <Button type="primary" htmlType="submit">
              {t('搜索')}
              <IconFont type="iconsousuo2" />
            </Button>
          </Form.Item>
        </Form>
      )}

      <div className="splitLine"></div>
      <div className="btn-container">
        <Space size={0}>
          <Checkbox
            indeterminate={indeterminate}
            onChange={handleCheckAllChange}
            checked={checkAll}
          >
            {t('全部')}
          </Checkbox>
          <div className="btn-wrp">
            {!mobileFlag ? (
              btn_list.map((item: any, index: number) => {
                return (
                  <div
                    onClick={() => !item.disabled && item.func()}
                    className={`item_${item.disabled ? ' disabled' : ''}`}
                    key={index}
                  >
                    {item.dom}
                  </div>
                );
              })
            ) : (
              //移动端取前几个展示即可
              <>
                {btn_list.slice(0, 4).map((item: any, index: number) => {
                  return (
                    <div
                      className={`item_${item.disabled ? ' disabled' : ''}`}
                      key={index}
                    >
                      {item.dom}
                    </div>
                  );
                })}

                {btn_list.slice(4, btn_list.length).length > 0 && (
                  <Popover
                    className="mobile_btns_popover"
                    onOpenChange={(newOpen: boolean) =>
                      setOpreatMenuVisible(newOpen)
                    }
                    open={operatMenuVisible}
                    content={
                      <div className="mobile_btns">
                        {btn_list
                          .slice(4, btn_list.length)
                          .map((item: any, index: number) => {
                            return (
                              <div
                                key={index}
                                className={item.disabled ? 'disabled' : ''}
                                onClick={() => {
                                  if (!item.disabled) {
                                    setOpreatMenuVisible(false);
                                    item.func();
                                  }
                                }}
                              >
                                {item.dom}
                                {item.title}
                              </div>
                            );
                          })}
                      </div>
                    }
                  >
                    <Button
                      onClick={(e: any) => {
                        e.preventDefault();
                        e.stopPropagation();
                        setOpreatMenuVisible(!operatMenuVisible);
                      }}
                    >
                      <IconFont type="iconziyuanku1" />
                    </Button>
                  </Popover>
                )}
              </>
            )}
          </div>
          <div className="mode_switch_container">
            <div onClick={() => setModeSwitch(true)} className="mode_switch">
              <Tooltip title={t('图例模式')}>
                <IconFont
                  type="iconhebingxingzhuangfuzhi2"
                  className={modeSwitch ? 'active' : ''}
                />
              </Tooltip>
            </div>
            <div onClick={() => setModeSwitch(false)} className="mode_switch">
              <Tooltip title={t('列表模式')}>
                <IconFont
                  type="iconliebiao"
                  className={modeSwitch ? '' : 'active'}
                />
              </Tooltip>
            </div>
          </div>
        </Space>
      </div>
      <Checkbox.Group
        value={selectedRowKeys}
        onChange={handleCheckChange}
        style={{ width: '100%' }}
      >
        {modeSwitch ? (
          <div className="data_wrapper">
            {dataSource && dataSource.length > 0 ? (
              dataSource.map((item: any) => (
                <CourseBlock
                  key={item.contentId_}
                  item={item}
                  isRejected={true}
                  onEdit={() => {
                    if (mobileFlag) {
                      message.info(t('暂不支持手机端，请前往电脑端操作'));
                    } else {
                      window.open(
                        `/learn/workbench/#/tempatedetail/courseInfo?id=${item.entityData.contentId_}&type=edit&myOrShare=${myOrShare}`,
                      );
                    }
                  }}
                  onPublish={() => {
                    setOperationData(item.entityData);
                    setOneOrBatch(true);
                    setVisible(true);
                    setIsRelease(true);
                    if (item.pladte_id) {
                      setPlateName(item.plate[0]);
                      setPlateId(item.plate_id[0]);
                      let arr = allPlate.filter(
                        (item2: any) => item2.id === item.plate_id[0],
                      );
                      let themarr = JSON.parse(arr[0].thematic_ids);
                      setAllSubject(
                        themarr?.filter((item: any) => item.is_enable),
                      );
                      setSubjectId(item.classification);
                    } else {
                      setPlateName(null);
                      setPlateId(null);
                      setAllSubject([]);
                      setSubjectId([]);
                    }
                  }}
                  onUnPublish={() => {
                    setOneOrBatch(true);
                    setOperationData(item.entityData);
                    setVisible(true);
                    setIsRelease(false);
                  }}
                  onLog={() => handleLogShow(item.entityData)}
                  onPreview={() => {
                    window.open(`/learn/workbench/#/tempatedetail/courseInfo?id=${item.entityData.contentId_}&type=see&myOrShare=${myOrShare}`)
                    addLog({
                      courseIds: [item.entityData?.contentId_],
                      courseType: item.entityData?.courseType,
                      operateType: 0,
                    });
                  }}
                  onDelete={() => {
                    setDeleteVisible(true)
                    setOperationData(item.entityData);
                  }}
                />
              ))
            ) : (
              <Empty style={{ width: '100%' }} />
            )}
          </div>
        ) : (
          <Table
            scroll={{ y: 'calc(100vh - 305px)' }}
            // rowSelection={rowSelection}

            rowKey="contentId_"
            columns={columns}
            size="small"
            dataSource={getDataTable}
            loading={tableLoading}
            pagination={false}
          />
        )}
      </Checkbox.Group>
      {total > 0 && (
        <Pagination
          style={{ textAlign: 'center', marginTop: 10 }}
          {...{
            current: query.page,
            pageSize: query.size,
            total: total,
            onChange: handlePageChange,
            showQuickJumper: true,
            defaultCurrent: 1,
            size: 'small',
            showTotal: total => t('共{name}条', String(total)),
            showSizeChanger: true,
            pageSizeOptions: ['24', '36', '48', '60'],
          }}
        />
      )}
      <Modal
        title={isRelease ? t('共享') : t('驳回')}
        visible={visible}
        onOk={handleReleaseOrReject}
        onCancel={() => setVisible(false)}
        destroyOnClose
      >
        {/* 确定要{isRelease ? '发布' : '驳回'}该课程吗？ */}

        {getPermission(
          ['micro'],
          ['microcourse_course_special_review'],
          false,
          COURSE_TYPE[operationData.courseType],
        ) && isRelease ? (
          <>
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <span style={{ marginRight: '15px' }}>{t('发布板块：')}</span>
              <Select
                value={plateId}
                style={{ width: '200px' }}
                placeholder={t('请选择板块')}
                allowClear
                onChange={(key: any, item: any) => {
                  if (key) {
                    setPlateId(key);
                    setPlateName(item.children);
                    let arr = allPlate.filter((item: any) => item.id === key);
                    if (arr.length > 0) {
                      let themarr = JSON.parse(arr[0].thematic_ids);
                      setAllSubject(
                        themarr?.filter((item: any) => item.is_enable),
                      );
                    }
                  } else {
                    setPlateId(null);
                    setPlateName(null);
                    setAllSubject([]);
                  }
                  setSubjectId([]);
                }}
              >
                {allPlate.map((item: any, index: number) => {
                  return (
                    <Option value={item.id} key={item.id}>
                      {item.name}
                    </Option>
                  );
                })}
              </Select>
            </div>
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                marginTop: '20px',
              }}
            >
              <span style={{ marginRight: '15px' }}>{t('专题栏目：')}</span>
              <Select
                mode="multiple"
                value={subjectId}
                style={{ width: '200px' }}
                placeholder={t('请选择专题栏目')}
                onChange={(e: any) => {
                  setSubjectId(e);
                }}
              >
                {allsubject.map((item: any) => {
                  return (
                    <Option value={item.code} key={item.code}>
                      {item.name}
                    </Option>
                  );
                })}
              </Select>
            </div>
          </>
        ) : !isRelease ? (
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Input.TextArea
              rows={4}
              style={{ width: '100%' }}
              placeholder={t('请输入驳回原因')}
              onChange={(e: any) => {
                setRejectReason(e.target.value);
              }}
            />
          </div>
        ) : (
          `${t('确定要')}${isRelease ? t('共享') : t('驳回')}${t('该课程吗？')}`
        )}
      </Modal>
       <Modal
         title="取消共享"
         visible={deleteVisible}
         onOk={cancle}
         onCancel={() => setDeleteVisible(false)}
         destroyOnClose>
         确定要取消共享吗
       </Modal>
      <LoggerModal
        id={operationData.contentId_}
        open={logOpen}
        onClose={() => setLogOpen(false)}
      />
    </div>
  );
};

export default MyReview;
