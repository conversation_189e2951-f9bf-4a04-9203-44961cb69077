import React, { FC, useCallback, useEffect, useRef, useState } from "react";
import { Button, message, Upload, Popconfirm, Progress } from 'antd';
import { CloseOutlined, PaperClipOutlined, LoadingOutlined } from "@ant-design/icons";
import "./UploadItem.less";
import { useDispatch, useLocation, useSelector } from "umi";
import { deleteHomeworkFile, uploadFileChunk, mergeFile, getMergeProgress, mergeFilesharding } from "@/api/homework";
import useLocale from "@/hooks/useLocale";
import ImageModal from "./ImageModal";
import ImageEditorModal from "@/components/ImageEditorModal";
import { getGuid } from "@/utils";

interface IUploadItem {
  data: any;
  canEdit: boolean;
  id: string;
  index: number;
  isTeacher?: boolean;
  isEnd?: boolean;
  submissionList?: any,
  onPreview: (file: any) => void;
  maxSize?: number; // 最大上传大小，单位MB，默认200MB
  chunkSize?: number; // 分片大小，单位MB，默认5MB
  chunkThreshold?: number; // 分片上传阈值，单位MB，默认10MB
}

// 分片大小 5MB
const DEFAULT_CHUNK_SIZE = 5 * 1024 * 1024;
// 默认最大上传大小 200MB
const DEFAULT_MAX_SIZE = 200 * 1024 * 1024;
// 默认分片上传阈值 10MB
const DEFAULT_CHUNK_THRESHOLD = 5 * 1024 * 1024;

// const fileMap = {
//   document: ["doc", "docm", "docx", "docxf", "dot", "dotm", "dotx", "epub", "fodt", "fb2", "htm", "html", "mht", "odt", "oform", "ott", "oxps", "pdf", "rtf", "txt", "djvu", "xml", "xps", "csv", "fods", "ods", "ots", "xls", "xlsb", "xlsm", "xlsx", "xlt", "xltm", "xltx", "fodp", "odp", "otp", "pot", "potm", "potx", "pps", "ppsm", "ppsx", "ppt", "pptm", "pptx"],
//   picture: [
//     'jpeg',
//     'png',
//     'jpg',
//     'svg',
//     'bmp',
//     'tif',
//     'gif',
//     'pcx',
//     'tga',
//     'exif',
//     'fpx',
//     'psd',
//     'cdr',
//     'pcd',
//     'dxf',
//     'ufo',
//     'eps',
//     'ai',
//     'raw',
//     'wmf',
//     'webp',
//   ],
//   audio: ['wav', 'flac', 'ape', 'alac', 'mp3', 'aac', 'ogg', 'opus'],
//   video: ['rmvb', 'mp4', 'mov', 'm4v', 'avi', 'mkv', 'flv', 'vob'],
// };

const dealSize = (size: number): string => {
  if (size >= 1024 && size < Math.pow(1024, 2)) {
    return `${(size / 1024).toFixed(1)}K`;
  } else if (size >= Math.pow(1024, 2)) {
    return `${(size / Math.pow(1024, 2)).toFixed(1)}M`;
  } else {
    return `${size ?? 0}B`;
  }
};

const UploadItem: FC<IUploadItem> = ({
  data,
  canEdit,
  id,
  index,
  isEnd,
  isTeacher,
  onPreview,
  submissionList,
  maxSize = DEFAULT_MAX_SIZE,
  chunkSize = DEFAULT_CHUNK_SIZE,
  chunkThreshold = DEFAULT_CHUNK_THRESHOLD
}) => {
  const { t } = useLocale();
  const { files, oldFiles, homeworkData, stuCode } = useSelector<any, any>(
    (state) => state.homework);

  const { userInfo, fileMap } = useSelector<any, any>(
    (state) => state.global);

  const [loading, setLoading] = useState<boolean>(false);
  const [fileData, setFileData] = useState<any>({});
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [currentFile, setCurrentFile] = useState<File | null>(null);
  const [fileGuid, setFileGuid] = useState<string>('');
  const dispatch = useDispatch();
  const location: any = useLocation();
  const [curImage, setCurImage] = useState<string>("");
  const [imageVisible, setImageVisible] = useState<boolean>(false);
  const [editorConfig, setEditorConfig] = useState<any>({});
  const [imageEditorVisible, setImageEditorVisible] = useState<boolean>(false);

  const getType = (filename: string) => {
    const fileArr = filename?.split('.');
    return fileArr ? fileArr[fileArr.length - 1].split('?')[0]?.toLocaleLowerCase() : '';
  };

  type tFileType = keyof typeof fileMap;
  const getPreviewType = (filePath: string): tFileType => {
    const fileSuffix = getType(filePath);
    const typeArr = Object.keys(fileMap).filter((item: string) => fileMap[(item as tFileType)].includes(fileSuffix));
    const type = typeArr.length > 0 ? typeArr[0] : '';
    return (type as tFileType);
  };

  useEffect(() => {
    if (Object.keys(files).length > 0) {
      setFileData(files?.[id]?.[index] ?? {});
      // 打印files和当前fileData，帮助排查index为何为null
      console.log('files:', files,index,index);
    }
  }, [files]);

  const beforeUpload = (file: any) => {
    if (file.size > maxSize) {
      message.warning(t(`附件大小不能超过${maxSize / (1024 * 1024)}M！`));
      return false;
    } else {
      // 如果文件大于阈值，使用分片上传
      if (file.size > chunkThreshold) {
        setCurrentFile(file);
        handleChunkUpload(file);
        return false; // 阻止默认上传
      }
      return file; // 小文件使用默认上传
    }
  };

  // 处理分片上传
  const handleChunkUpload = async (file: File) => {
    try {
      setLoading(true);
      setIsUploading(true);
      setUploadProgress(0);

      const guid = getGuid();
      setFileGuid(guid);
      const chunks = Math.ceil(file.size / chunkSize);
      let completedChunks = 0;

      // 上传所有分片
      for (let i = 0; i < chunks; i++) {
        const start = i * chunkSize;
        const end = Math.min(file.size, start + chunkSize);
        const chunk = file.slice(start, end);

        // 创建一个新的File对象，因为分片后的Blob对象不是File类型
        const chunkFile = new File([chunk], file.name, { type: file.type });

        const response = await uploadFileChunk({
          chunk: String(i),
          guid: guid,
          file: chunkFile,
          classification: 'homework',
          type: getType(file.name),
          originalName: file.name
        });

        if (!response || response.status !== 200) {
          throw new Error('分片上传失败');
        }
        completedChunks++;
        setUploadProgress(Math.floor((completedChunks / chunks) * 100));
      }

      // 所有分片上传完成，合并文件
      const mergeResponse = await mergeFile({
        fileName: file.name,
        guid: guid,
        classification: 'homework',
        type: getType(file.name),
        keepName: true,
        filePathType: 'http'
      });

      if (mergeResponse && mergeResponse?.status === 200) {
        let getMerges = await getMergeProgress(guid);

        // 如果state为0，需要轮询直到state变为-1
        while (getMerges?.data?.data?.state === 0) {
          await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
          getMerges = await getMergeProgress(guid);
        }

        if (getMerges?.data?.success === true && getMerges?.data?.data?.state !== -1) {

          var params = {
            homeworkId: homeworkData.realId,
            parentId: homeworkData.parentId || '',
            index: fileData.index || 1,
            questionId: id,
            courseId: location.query.id,
            stuCode: stuCode ?? "",
            fileName: getMerges?.data?.data?.fileName,
            fileUrl: getMerges?.data?.data?.finalFilePath,
          }
          mergeFileshardingups(params)
        } else {
          message.error(getMerges?.data?.data?.errorMsg || t("附件上传失败！"));
        }
      } else {
        throw new Error('文件合并失败');
      }
    } catch (error) {
      message.error(t("附件上传失败！"));
      setFileData({
        ...fileData,
        filename: "",
        size: 0
      });
    } finally {
      setLoading(false);
      setIsUploading(false);
      setUploadProgress(0);
      setCurrentFile(null);
    }
  };

  const mergeFileshardingups = async (info: any) => {
    const mergeResponse = await mergeFilesharding(info);
    if (mergeResponse?.data?.status == 200) {
      const obj = JSON.parse(JSON.stringify(files));
      obj[id][index] = {
        ...fileData,
        ...mergeResponse?.data?.data,
        type: getPreviewType(mergeResponse?.data?.data?.fileUrl)
      };
      dispatch({
        type: 'homework/handlePublicChange',
        payload: {
          files: obj
        }
      });
      message.success(t("附件上传成功！"));
    } else {
      setLoading(false);
      message.error(t("附件上传失败！"));
      setFileData({
        ...fileData,
        filename: "",
        size: 0
      });
    }

  };

  const onChange = (info: any) => {
    if (info.file.status === "uploading") {
      setLoading(true);
    }
    if (info.file.status === 'done') {
      setLoading(false);
      if (info.file.response.status == 200) {
        const obj = JSON.parse(JSON.stringify(files));
        obj[id][index] = {
          ...fileData,
          ...info.file.response.data,
          type: getPreviewType(info.file.response.data.fileUrl)
        };
        dispatch({
          type: 'homework/handlePublicChange',
          payload: {
            files: obj
          }
        });
        message.success(t("附件上传成功！"));
      } else {
        setLoading(false);
        message.error(t("附件上传失败！"));
        setFileData({
          ...fileData,
          filename: "",
          size: 0
        });
      }
    } else if (info.file.status === 'error') {
      message.error(t("附件上传失败！"));
      setLoading(false);
      setFileData({
        ...fileData,
        filename: "",
        size: 0
      });
    }
  };
  const onDownload = () => {
    var a = document.createElement("a");
    // ${userInfo.nickName}-${userInfo.userCode}-${fileData.name}-
    a.download = `${submissionList?.stuName || ''}${submissionList?.stuCode ? `（${submissionList.stuCode}）` : ''}_${fileData.filename}`;
    a.href = fileData.fileUrl;
    a.rel = 'noopener noreferrer';
    (document.querySelector("body") as any).append(a); // 修复firefox中无法触发click
    a.click();
    a.remove();
  };

  const handleDelete = () => {
    // 这里打印fileData.index，帮助排查为何为null
    // eslint-disable-next-line no-console
    console.log('handleDelete fileData.index:', fileData.index);
    const obj = JSON.parse(JSON.stringify(files));
    obj[id][index] = {
      name: fileData.name,
      required: fileData.required,
      key: fileData.key,
      index: fileData.index|| '1'
    };
    dispatch({
      type: 'homework/handlePublicChange',
      payload: {
        files: obj
      }
    });
    message.success(t("删除成功！"));
    // } else {
    //   deleteHomeworkFile({
    //     attachId: fileData.id,
    //   }).then(res => {
    //     if (res.status === 200) {
    //       const obj = JSON.parse(JSON.stringify(files));
    //       obj[id][index] = {
    //         name: fileData.name,
    //         required: fileData.required,
    //         key: fileData.key,
    //         index: fileData.index || null
    //       };
    //       dispatch({
    //         type: 'homework/handlePublicChange',
    //         payload: {
    //           files: obj,
    //         },
    //       });
    //       message.success("删除成功！");
    //     } else {
    //       message.error(res.message);
    //     }
    //   });
    // }
  };

  const previewAnnotate = (e: any) => {
    e.stopPropagation();
    if (isTeacher) {
      setImageEditorVisible(true);
      setEditorConfig({
        src: fileData.anSrc,
        originSrc: fileData.fileUrl
      });
      return;
    }
    setCurImage(fileData.anSrc);
    setImageVisible(true);
  };
  const handleAnnotation = (anSrc?: string, isReset?: boolean) => {
    const obj = JSON.parse(JSON.stringify(files));
    obj[id][index] = {
      ...fileData,
      anSrc: isReset ? null : anSrc,
    };
    dispatch({
      type: 'homework/handlePublicChange',
      payload: {
        files: obj
      }
    });
    setImageEditorVisible(false);
  };
  return <div className="upload-item">
    <span className={`title`}><PaperClipOutlined /><span className={`${fileData.required === 'true' || fileData.required === true ? 'required' : ''}`}>{data.name}：</span></span>
    {fileData.filename ? <div>
      <a onClick={() => {
        if (loading) return;
        if (isTeacher && !fileData.anSrc && fileData.type === "picture") {
          setImageEditorVisible(true);
          setEditorConfig({
            src: fileData.fileUrl,
            originSrc: fileData.fileUrl
          });
        } else {
          onPreview(fileData);
        }
      }}>{fileData.filename}</a>
      {!loading && <>
        {/* <span className="file-size">（{dealSize(fileData.fileSize)}）</span> */}
        {fileData.anSrc && (isEnd || isTeacher) && <span className="annotate-wrp" onClick={previewAnnotate}>教师已批注</span>}
        <a onClick={onDownload}>{t("下载")}</a>
        {canEdit && <Popconfirm placement="topLeft" title={t("确认删除该附件?")} onConfirm={handleDelete}>
          <CloseOutlined />
        </Popconfirm>}
      </>}
    </div> : canEdit ? (
      isUploading ? (
        <div className="upload-progress">
          <Progress percent={uploadProgress} size="small" />
          <span className="uploading"><LoadingOutlined />{t("上传中")} {uploadProgress}%</span>
        </div>
      ) : (
        <Upload
          data={{
            homeworkId: homeworkData.realId,
            parentId: homeworkData.parentId || '',
            index: fileData.index || 1,
            questionId: id,
            courseId: location.query.id,
            stuCode: stuCode ?? ""
          }}
          action="/exam-api/submit/resource/attach/upload"
          beforeUpload={beforeUpload}
          showUploadList={false}
          onChange={onChange}>

          <Button size="small">{t("上传附件")}</Button>
        </Upload>
      )
    ) : t("未提交")}
    {loading && !isUploading && <span className="uploading"><LoadingOutlined />{t("上传中")}</span>}
    <ImageModal image={curImage} visible={imageVisible} onClose={() => setImageVisible(false)} />
    <ImageEditorModal
      src={editorConfig.src ?? ""}
      visible={imageEditorVisible}
      originSrc={editorConfig.originSrc}
      onClose={() => {
        setImageEditorVisible(false);
        setEditorConfig({});
      }}
      onConfirm={handleAnnotation} />
  </div>;
};

export default UploadItem;
