import { IconFont } from '@/components/iconFont';
import useLocale from '@/hooks/useLocale';
import { <PERSON><PERSON><PERSON><PERSON>b, Button, Checkbox, Divider, Image, Input, List, Space, message } from 'antd';
import React, { FC, useEffect, useRef, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import './index.less';
import { searchKnowledgepoint } from '@/api/coursemap';
import { createguid } from '@/pages/Coursemaptool/Editmap/util';

const { Search } = Input;

interface KnowledgePointProps {
  onSelect?: (selectedPoints: any[]) => void;
  currentname?: string;
}

const KnowledgePoint: FC<KnowledgePointProps> = ({ onSelect, currentname='' }) => {
  const { t } = useLocale();
  const [loading, setLoading] = useState<boolean>(false);
  const [searchWord, setSearchWord] = useState<string>('');
  const [allPoints, setAllPoints] = useState<any[]>([]);
  const [selectedPoints, setSelectedPoints] = useState<any[]>([]);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const isClear = useRef<boolean>(false);

  // 分页参数
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });

  // 获取文档知识点列表
  const getDocumentPoints = async (pageIndex: number, pageSize: number, keyword: string) => {
    setLoading(true);
    try {
      // TODO: 替换为实际的文档知识点API
      const res = await searchKnowledgepoint({
        pageIndex,
        pageSize,
        name: keyword,
      });
      
      if (res.status == 200) {
        if (res.data.data.data.length < pageSize) {
          setHasMore(false);
        } else {
          setHasMore(true);
        }

        setPagination({
          ...pagination,
          current: pageIndex,
          total: res.data.data.recordTotal
        });
        const points:any = [];
        res.data.data.data.forEach((item: any) => {
            item.knowledgeSummaryVos.forEach((item2:any)=>{
                if(item2.chapterTitle!=''){
                    points.push({
                        recousetype: 'biz_sobey_knowledgepoints',
                        name: item2.chapterTitle,
                        contentId: item2.guid_,
                        contentId_: item2.guid_,
                        type: 'biz_sobey_knowledgepoints',
                        keyframe_: null,
                        tempid: createguid(),
                        keyframeno: null,
                        parentcontentId: item.contentId,
                        fragment_description: null,
                        inPoint: item2.inPoint,
                        outPoint: item2.outPoint,
                        parentname: item.name,
                        createDate: item.createDate,
                        suffix: item.suffix,
                        duration: item2.outPoint - item2.inPoint || 0
                    })
                }
            })
        });
        if (pageIndex === 1) {
          setAllPoints(points);
        } else {
          setAllPoints([...allPoints, ...points]);
        }
      }
    } catch (error) {
      message.error(t('获取文档知识点失败'));
    }
    setLoading(false);
  };

  useEffect(() => {
    getDocumentPoints(1, pagination.pageSize, '');
  }, []);

  // 修改后的handleSearch函数
  const handleSearch = (value: string) => {
    if(value === '' && currentname !=''){
      getDocumentPoints(1, pagination.pageSize,currentname);
      setSearchWord(currentname);
    }else{
      getDocumentPoints(1, pagination.pageSize, value);
      setSearchWord(value);
    }
  };

  // 处理选择知识点
  const handlePointSelect = (selected: any[]) => {
    setSelectedPoints(selected);
    onSelect?.(selected);
  };

  return (
    <div className="document-knowledge">
      <div className="knowledge-container">
        <div className="knowledge-list">
          <div>
            <Search
              value={searchWord}
              placeholder={currentname || t('请输入文档知识点')}
              onSearch={handleSearch}
              allowClear
              onChange={e => {
                if (e.target.value === '') {                  
                  isClear.current = true;
                }
                setSearchWord(e.target.value);
              }}
              style={{ width: 200, marginBottom: '15px' }}
            />
          </div>

          <div
            id="scrollableDiv"
            style={{
              width: '100%',
              height: 550,
              overflow: 'auto'
            }}
          >
            <Checkbox.Group
              value={selectedPoints}
              onChange={handlePointSelect}
              style={{ width: '100%' }}
            >
              <InfiniteScroll
                dataLength={allPoints.length}
                next={() => {
                  getDocumentPoints(
                    pagination.current + 1,
                    pagination.pageSize,
                    searchWord
                  );
                }}
                hasMore={hasMore}
                loader={
                  <h4 style={{ width: '100%', textAlign: 'center' }}>
                    {t('加载中...')}
                  </h4>
                }
                endMessage={
                  <Divider plain style={{ width: '100%', textAlign: 'center' }}>
                    {allPoints.length > 0 ? t('已加载完毕所有的知识点！') : ''}
                  </Divider>
                }
                scrollableTarget="scrollableDiv"
              >
                <List
                  grid={{ gutter: 10, column: 6 }}
                  dataSource={allPoints}
                  style={{ width: '99.5%' }}
                  renderItem={item => (
                    <List.Item>
                      <div className="card_point_item" style={{height: '225px'}}>
                        <div className="cover_img">
                        {item.keyframepath ? 
                            <Image
                            src={'/rman/static/images/document.png'}
                            fallback="/rman/static/images/document.png"
                            preview={false}
                            style={{ width: item.knowledgePoints[0].keyframepath ? '100%' : '40%' }}
                            onClick={() => {
                              window.open(`/rman/#/basic/rmanDetail/${item.parentcontentId}?showArrow=true&guid_=${item.contentId}&inpoint=${item.inPoint}&outpoint=${item.outPoint}`);
                            }}
                          />:
                            <div className='def_box'>
                              <img src="/rman/static/images/document.png" className='defimg' />
                            </div>
                          }
                          
                        </div>
                        <div className="point_name">
                          <Checkbox value={item}></Checkbox>
                          <span title={item.name} className="span_name">
                            {item.name}
                          </span>
                        </div>
                        <div className="point_link">
                          <span>
                            {t('时码：')}{(item.inPoint/10000000).toFixed(2)} - {(item.outPoint/10000000).toFixed(2)}
                          </span>
                        </div>
                        <div className="point_link">
                          <span title={t('来源：') + item.parentname}>
                            {t('来源：')}
                            {item.parentname}
                          </span>
                        </div>
                      </div>
                    </List.Item>
                  )}
                />
              </InfiniteScroll>
            </Checkbox.Group>
          </div>
        </div>
      </div>
    </div>
  );
};

export default KnowledgePoint;
