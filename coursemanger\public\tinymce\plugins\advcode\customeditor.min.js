/* Ephox advanced code plugin
 *
 * Copyright 2010-2016 Ephox Corporation.  All rights reserved.
 *
 * Version: 2.3.0-146
 */

!function(){"use strict";function i(e){return function(){return e}}var u=i(!1),c=i(!0),e=function(){return f},f={fold:function(e,t){return e()},is:u,isSome:u,isNone:c,getOr:r,getOrThunk:n,getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:i(null),getOrUndefined:i(void 0),or:r,orThunk:n,map:e,each:function(){},bind:e,exists:u,forall:c,filter:e,equals:t,equals_:t,toArray:function(){return[]},toString:i("none()")};function t(e){return e.isNone()}function n(e){return e()}function r(e){return e}function o(e,t,n){!function(e,t,n){if(!(_(n)||b(n)||I(n)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",n,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,n+"")}(e.dom,t,n)}function l(e,t){return function(e,t,n){n=function(e,t){t=function(e,t){t=e.dom.getAttribute(t);return null===t?void 0:t}(e,t);return void 0===t||""===t?[]:t.split(" ")}(e,t).concat([n]);return o(e,t,n.join(" ")),!0}(e,"class",t)}function a(e){return x(e)?e:(e=e,j.fromDom(e.dom.ownerDocument))}function s(e){return A(e)?e:j.fromDom(a(e).dom.body)}function d(e,t,n){if(!_(n))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",n,":: Element ",e),new Error("CSS value must be a string: "+n);var r;void 0!==(r=e).style&&E(r.style.getPropertyValue)&&e.style.setProperty(t,n)}var m,p,h,v,y,g=function(n){function e(){return o}function t(e){return e(n)}var r=i(n),o={fold:function(e,t){return t(n)},is:function(e){return n===e},isSome:c,isNone:u,getOr:r,getOrThunk:r,getOrDie:r,getOrNull:r,getOrUndefined:r,or:e,orThunk:e,map:function(e){return g(e(n))},each:function(e){e(n)},bind:t,exists:t,forall:t,filter:function(e){return e(n)?o:f},toArray:function(){return[n]},toString:function(){return"some("+n+")"},equals:function(e){return e.is(n)},equals_:function(e,t){return e.fold(u,function(e){return t(n,e)})}};return o},w=function(e){return null==e?f:g(e)},T=function(t){return function(e){return typeof e===t}},_=(m="string",function(e){return e=typeof(t=e),(null===t?"null":"object"==e&&(Array.prototype.isPrototypeOf(t)||t.constructor&&"Array"===t.constructor.name)?"array":"object"==e&&(String.prototype.isPrototypeOf(t)||t.constructor&&"String"===t.constructor.name)?"string":e)===m;var t}),b=T("boolean"),E=T("function"),I=T("number"),e=("undefined"!=typeof window||Function("return this;")(),function(t){return function(e){return e.dom.nodeType===t}}),L=e(1),x=e(9),A=e(11),O=function(e){if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},j={fromHtml:function(e,t){t=(t||document).createElement("div");if(t.innerHTML=e,!t.hasChildNodes()||1<t.childNodes.length)throw console.error("HTML does not have a single root node",e),new Error("HTML must have a single root node");return O(t.childNodes[0])},fromTag:function(e,t){e=(t||document).createElement(e);return O(e)},fromText:function(e,t){e=(t||document).createTextNode(e);return O(e)},fromDom:O,fromPoint:function(e,t,n){return w(e.dom.elementFromPoint(t,n)).map(O)}},N=E(Element.prototype.attachShadow)&&E(Node.prototype.getRootNode)?function(e){return j.fromDom(e.dom.getRootNode())}:a,T={},e={exports:T};h=T,v=e,y=p=void 0,function(e){"object"==typeof h&&void 0!==v?v.exports=e():"function"==typeof p&&p.amd?p([],e):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).EphoxContactWrapper=e()}(function(){return function r(o,i,u){function c(t,e){if(!i[t]){if(!o[t]){var n="function"==typeof y&&y;if(!e&&n)return n(t,!0);if(f)return f(t,!0);throw(n=new Error("Cannot find module '"+t+"'")).code="MODULE_NOT_FOUND",n}n=i[t]={exports:{}},o[t][0].call(n.exports,function(e){return c(o[t][1][e]||e)},n,n.exports,r,o,i,u)}return i[t].exports}for(var f="function"==typeof y&&y,e=0;e<u.length;e++)c(u[e]);return c}({1:[function(e,t,n){var r,o,t=t.exports={};function i(){throw new Error("setTimeout has not been defined")}function u(){throw new Error("clearTimeout has not been defined")}function c(t){if(r===setTimeout)return setTimeout(t,0);if((r===i||!r)&&setTimeout)return r=setTimeout,setTimeout(t,0);try{return r(t,0)}catch(e){try{return r.call(null,t,0)}catch(e){return r.call(this,t,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:i}catch(e){r=i}try{o="function"==typeof clearTimeout?clearTimeout:u}catch(e){o=u}}();var f,l=[],a=!1,s=-1;function d(){a&&f&&(a=!1,f.length?l=f.concat(l):s=-1,l.length&&m())}function m(){if(!a){var e=c(d);a=!0;for(var t=l.length;t;){for(f=l,l=[];++s<t;)f&&f[s].run();s=-1,t=l.length}f=null,a=!1,function(t){if(o===clearTimeout)return clearTimeout(t);if((o===u||!o)&&clearTimeout)return o=clearTimeout,clearTimeout(t);try{o(t)}catch(e){try{return o.call(null,t)}catch(e){return o.call(this,t)}}}(e)}}function p(e,t){this.fun=e,this.array=t}function h(){}t.nextTick=function(e){var t=new Array(arguments.length-1);if(1<arguments.length)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];l.push(new p(e,t)),1!==l.length||a||c(m)},p.prototype.run=function(){this.fun.apply(null,this.array)},t.title="browser",t.browser=!0,t.env={},t.argv=[],t.version="",t.versions={},t.on=h,t.addListener=h,t.once=h,t.off=h,t.removeListener=h,t.removeAllListeners=h,t.emit=h,t.prependListener=h,t.prependOnceListener=h,t.listeners=function(e){return[]},t.binding=function(e){throw new Error("process.binding is not supported")},t.cwd=function(){return"/"},t.chdir=function(e){throw new Error("process.chdir is not supported")},t.umask=function(){return 0}},{}],2:[function(e,s,t){(function(t){function r(){}function i(e){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof e)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],a(e,this)}function o(n,r){for(;3===n._state;)n=n._value;0!==n._state?(n._handled=!0,i._immediateFn(function(){var e,t=1===n._state?r.onFulfilled:r.onRejected;if(null!==t){try{e=t(n._value)}catch(e){return void c(r.promise,e)}u(r.promise,e)}else(1===n._state?u:c)(r.promise,n._value)})):n._deferreds.push(r)}function u(t,e){try{if(e===t)throw new TypeError("A promise cannot be resolved with itself.");if(e&&("object"==typeof e||"function"==typeof e)){var n=e.then;if(e instanceof i)return t._state=3,t._value=e,void f(t);if("function"==typeof n)return void a((r=n,o=e,function(){r.apply(o,arguments)}),t)}t._state=1,t._value=e,f(t)}catch(e){c(t,e)}var r,o}function c(e,t){e._state=2,e._value=t,f(e)}function f(e){2===e._state&&0===e._deferreds.length&&i._immediateFn(function(){e._handled||i._unhandledRejectionFn(e._value)});for(var t=0,n=e._deferreds.length;t<n;t++)o(e,e._deferreds[t]);e._deferreds=null}function l(e,t,n){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof t?t:null,this.promise=n}function a(e,t){var n=!1;try{e(function(e){n||(n=!0,u(t,e))},function(e){n||(n=!0,c(t,e))})}catch(e){if(n)return;n=!0,c(t,e)}}var e,n;e=this,n=setTimeout,i.prototype.catch=function(e){return this.then(null,e)},i.prototype.then=function(e,t){var n=new this.constructor(r);return o(this,new l(e,t,n)),n},i.all=function(e){var c=Array.prototype.slice.call(e);return new i(function(o,i){if(0===c.length)return o([]);var u=c.length;for(var e=0;e<c.length;e++)!function t(n,e){try{if(e&&("object"==typeof e||"function"==typeof e)){var r=e.then;if("function"==typeof r)return void r.call(e,function(e){t(n,e)},i)}c[n]=e,0==--u&&o(c)}catch(e){i(e)}}(e,c[e])})},i.resolve=function(t){return t&&"object"==typeof t&&t.constructor===i?t:new i(function(e){e(t)})},i.reject=function(n){return new i(function(e,t){t(n)})},i.race=function(o){return new i(function(e,t){for(var n=0,r=o.length;n<r;n++)o[n].then(e,t)})},i._immediateFn="function"==typeof t?function(e){t(e)}:function(e){n(e,0)},i._unhandledRejectionFn=function(e){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",e)},i._setImmediateFn=function(e){i._immediateFn=e},i._setUnhandledRejectionFn=function(e){i._unhandledRejectionFn=e},void 0!==s&&s.exports?s.exports=i:e.Promise||(e.Promise=i)}).call(this,e("timers").setImmediate)},{timers:3}],3:[function(f,e,l){(function(e,t){var r=f("process/browser.js").nextTick,n=Function.prototype.apply,o=Array.prototype.slice,i={},u=0;function c(e,t){this._id=e,this._clearFn=t}l.setTimeout=function(){return new c(n.call(setTimeout,window,arguments),clearTimeout)},l.setInterval=function(){return new c(n.call(setInterval,window,arguments),clearInterval)},l.clearTimeout=l.clearInterval=function(e){e.close()},c.prototype.unref=c.prototype.ref=function(){},c.prototype.close=function(){this._clearFn.call(window,this._id)},l.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},l.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},l._unrefActive=l.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;0<=t&&(e._idleTimeoutId=setTimeout(function(){e._onTimeout&&e._onTimeout()},t))},l.setImmediate="function"==typeof e?e:function(e){var t=u++,n=!(arguments.length<2)&&o.call(arguments,1);return i[t]=!0,r(function(){i[t]&&(n?e.apply(null,n):e.call(null),l.clearImmediate(t))}),t},l.clearImmediate="function"==typeof t?t:function(e){delete i[e]}}).call(this,f("timers").setImmediate,f("timers").clearImmediate)},{"process/browser.js":1,timers:3}],4:[function(e,t,n){var r=e("promise-polyfill"),e="undefined"!=typeof window?window:Function("return this;")();t.exports={boltExport:e.Promise||r}},{"promise-polyfill":2}]},{},[4])(4)});function S(){function c(e,t,n){function r(n){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];o||(o=!0,null!==i&&(clearTimeout(i),i=null),n.apply(null,e))}}void 0===n&&(n=1e3);var o=!1,i=null,e=r(e),u=r(t);return{reject:u,resolve:e,start:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];o||(i=setTimeout(function(){return u.apply(null,e)},n))}}}var n={},f={};tinymce.Resource={add:function(e,t){f[e]&&(f[e](t),delete f[e]),n[e]=F.resolve(t)},load:function(r,o){var i='Script at URL "'+o+'" failed to load',u='Script at URL "'+o+"\" did not call `tinymce.Resource.add('"+r+"', data)` within 1 second";if(void 0!==n[r])return n[r];var e=new F(function(e,t){var n=c(e,t);f[r]=n.resolve,tinymce.ScriptLoader.loadScripts([o],function(){return n.start(u)},function(){return n.reject(i)})});return n[r]=e}}}var F=e.exports.boltExport;function C(n,r){function e(e,t){return t&&!t()||setTimeout(function(){e.state.completionActive||e.showHint({completeSingle:!1,container:r})},100),n.Pass}return{completeAfter:e,completeIfAfterLt:function(t){return e(t,function(){var e=t.getCursor();return"<"===t.getRange(n.Pos(e.line,e.ch-1),e)})},completeIfInTag:function(t){return e(t,function(){var e=t.getTokenAt(t.getCursor());return!!("string"!==e.type||/['"]/.test(e.string.charAt(e.string.length-1))&&1!==e.string.length)&&n.innerMode(t.getMode(),e.state).state.tagName})}}}function P(t){return w(tinymce.get).bind(function(e){return w(e(t))})}function R(e,t,n,r){function o(e){27!==e.keyCode&&e.stopPropagation()}void 0===r&&(r="");var i=C(e,P(n.editorId).map(function(e){e=N(j.fromDom(e.getElement()));return s(e).dom}).getOrUndefined()),r={lineWrapping:n.lineWrapping,lineNumbers:n.lineNumbers,foldGutter:n.foldGutter,theme:n.theme,direction:n.direction,matchTags:{bothTags:!0},keyMap:"sublime",gutters:n.gutter?["CodeMirror-linenumbers","CodeMirror-foldgutter"]:[],extraKeys:{"Alt-F":"findPersistent","Ctrl-J":"toMatchingTag","Ctrl-B":"selectNextOccurrence","'<'":i.completeAfter,"'/'":i.completeIfAfterLt,"' '":i.completeIfInTag,"'='":i.completeIfInTag,"Ctrl-Q":function(e){e.foldCode(e.getCursor())}},mode:"text/html",value:r},u=e(t,r);return t.addEventListener("keyup",o),t.addEventListener("keydown",o),t.addEventListener("keypress",o),setTimeout(function(){u.focus(),n.cursor&&u.doc.setCursor(n.cursor)},tinymce.Env.ie?50:0),setTimeout(function(){return u.refresh()},200),{cmi:u,unbind:function(){t.removeEventListener("keyup",o),t.removeEventListener("keydown",o),t.removeEventListener("keypress",o)}}}function k(t,n){if(U[n])return F.resolve();var r,o,e=P(n),i=e.bind(function(e){return w(null===(e=e.ui)||void 0===e?void 0:e.styleSheetLoader)}).getOr(tinymce.DOM.styleSheetLoader);return e.each(function(e){e.on("remove",function(){i.unload&&i.unload(t),delete U[n]})}),U[n]=!0,r=t,o=i,new F(function(e,t){(o||tinymce.DOM.styleSheetLoader).load(r,e,t)})}function D(e,t,n){return F.all([k(t,n),(n="tinymce.plugins.advcode.CodeMirror",e=e,tinymce.Resource||S(),tinymce.Resource.load(n,e))]).then(function(e){e[0];return(0,e[1])()})}function M(r,o){return new F(function(e){var t=j.fromDom(r);!function(e,t,n){e=e.dom;d(e,t,n)}(t,"width","100%"),w(t.dom.parentNode).map(j.fromDom).filter(L).each(function(e){return t="mce-codemirror",void(void 0!==(e=e).dom.classList?e.dom.classList.add(t):l(e,t));var t}),e(D(o.codeMirrorScriptUrl,o.codeMirrorCssUrl,o.editorId).then(function(e){var e=R(e,r,o),t=e.cmi,n=e.unbind;return{getValue:function(){return t.doc.getValue()},setValue:function(e){return t.doc.setValue(e)},destroy:function(){return n()}}}))})}var U={};tinymce.Resource.add("tinymce.plugins.advcode.customeditor",M)}();