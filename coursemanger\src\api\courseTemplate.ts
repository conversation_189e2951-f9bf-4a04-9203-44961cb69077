import HTTP from './index';

namespace courseTemplate {
    export function getTemplateCatalogue() {
        return HTTP.get(`/learn/v1/curriculum/center/course/template/catalogue`)
            .then((res) => {
                if (res.status === 200) {
                    return res.data;
                }
            })
            .catch((error) => {
                return error;
            });
    }


    export function getTemplateCatalogue2(isPublish: boolean, data: any) {
        return HTTP.post(`/learn/v1/curriculum/center/course/template/catalogue?isPublish=${isPublish}`, data).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            return error
        })
    }

    export function updateTemplateDetail(hivetree: any, privilege: string, data: any) {
        return HTTP.post(`/learn/v1/curriculum/center/update/course/template?tree=${hivetree}&privilege=${privilege}`, data).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            return error
        })
    }

    //添加章
    export function addChapter(data: Chapter.IaddChapterParamsItem) {
        return HTTP.post(`/learn/v1/curriculum/center/add/course/template/chapter`, data).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            console.error(error)
        })
    }



    // export function getTemplateList(data: any) {
    //     return HTTP.post(`/cvod/v1/curriculum/center/search`, data)
    //         .then((res) => {
    //             if (res.status === 200) {
    //                 return res.data;
    //             }
    //         })
    //         .catch((error) => {
    //             return error;
    //         });
    // }

    export function newTemplate(name: string) {
        return HTTP.post(
            `/learn/v1/curriculum/center/create/course/template?templateName=${name}&isPrivate=true`,
        )
            .then((res) => {
                if (res.status === 200) {
                    return res.data;
                }
            })
            .catch((error) => {
                return error;
            });
    }

    export function getTemplateDetail(id: string) {
        return HTTP.get(`/learn/v1/curriculum/center/course/template/details/${id}`)
            .then((res) => {
                if (res.status === 200) {
                    return res.data;
                }
            })
            .catch((error) => {
                return error;
            });
    }


    export function getChapter(data: string) {
        return HTTP.get(
            `/learn/v1/curriculum/center/course/template/chapter/tree?${data}`,
        )
            .then((res) => {
                if (res.status === 200) {
                    return res.data;
                }
            })
            .catch((error) => {
                console.error(error);
            });
    }


    //添加节  - 无
    export function addSection(
        contentId: number,
        data: Chapter.IaddSectionParamsItem[],
    ) {
        return HTTP.post(
            `/cvod/v1/curriculum/center/add/course/template/section?contentId=${contentId}`,
            data,
        )
            .then((res) => {
                if (res.status === 200) {
                    return res.data;
                }
            })
            .catch((error) => {
                console.error(error);
            });
    }

    //添加内容
    export function addCourse(data: Chapter.IaddCourseParamsItem) {
        return HTTP.post(
            `/learn/v1/curriculum/center/add/course/template/resource`,
            data,
        )
            .then((res) => {
                if (res.status === 200) {
                    return res.data;
                }
            })
            .catch((error) => {
                console.error(error);
            });
    }

    // 统一编辑
    export function updateAny(data: Chapter.IupdateParam) {
        return HTTP.post(
            `/learn/v1/curriculum/center/part/update/course/template/chapter`,
            data,
        )
            .then((res) => {
                if (res.status === 200) {
                    return res.data;
                }
            })
            .catch((error) => {
                console.error(error);
            });
    }

    // 统一删除
    export function deleteAny(data: Chapter.IdeleteParam) {
        return HTTP.post(
            `/learn/v1/curriculum/center/delete/course/template/chapter`,
            data,
        )
            .then((res) => {
                if (res.status === 200) {
                    return res.data;
                }
            })
            .catch((error) => {
                console.error(error);
            });
    }
    export function personaltoshare(contentId: string, data: any,  params?: any) {
        return HTTP.post(`/learn/v1/curriculum/center/personal/to/share/audit/${contentId}`,data, ).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            console.error(error)
        })
    }
  export function releasestatus2( params: any) {
    return HTTP(`/learn/v1/curriculum/center/audit/template`,{
      method: 'POST',
      params
    }).then(res => {
      if (res.status === 200) {
        return res
      }
    }).catch(error => {
      console.error(error)
    })

  }
    export function copyhomework(data: any) {
        return HTTP.post(`/exam-api/template/homework/copy`,data).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            console.error(error)
        })
    }

    // 草稿到待发布
    export function updataChapterOne(contentId: string, data: string[]) {
        return HTTP.post(`/learn/v1/curriculum/course/publish/resource?contentId=${contentId}`, data)
            .then((res) => {
                if (res.status === 200) {
                    return res.data;
                }
            })
            .catch((error) => {
                console.error(error);
            });
    }


    // 章节状态修改
    export function updataChapterTwo(contentId: string, data: string[], state: number) {
        return HTTP.post(
          `/learn/v1/curriculum/course/offshelf/resource/${state}?contentId=${contentId}`,
          data,
      )
            .then((res) => {
                if (res.status === 200) {
                    return res.data;
                }
            })
            .catch((error) => {
                console.error(error);
            });
    }

    // 移动节和资源 无
    export function moveresource(contentId: string, data: Chapter.ImoveParams[]) {
        return HTTP.post(
            `/cvod/v1/curriculum/center/move/course/template/chapter-resource?contentId=${contentId}`,
            data,
        )
            .then((res) => {
                if (res.status === 200) {
                    return res.data;
                }
            })
            .catch((error) => {
                console.error(error);
            });
    }


    export function releasestatus(status: any, data: any) {
        return HTTP.post(`/learn/v1/curriculum/center/update/course/template/release/status/${status}`, data).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            console.error(error)
        })
    }


    // // 统一删除
    // export function deleteBatch(data: Chapter.IdeleteBatchParam) {
    //     return HTTP.delete(`/cvod/v1/curriculum/center/delete/course/template/chapter`, { data }).then(res => {
    //         if (res.status === 200) {
    //             return res.data
    //         }
    //     }).catch(error => {
    //         console.error(error)
    //     })
    // }

    // 资源列表
    export function getResoucesList(data: string) {
        return HTTP.get(
            `/learn/v1/curriculum/center/search/course/template/resouces${data}`,
        )
            .then((res) => {
                if (res.status === 200) {
                    return res.data;
                }
            })
            .catch((error) => {
                console.error(error);
            });
    }

    export function downloadentity(data: Array<string>) {
        return HTTP.post(`/rman/v1/entity/download/fileinfo`, data)
            .then((res) => {
                if (res.status === 200) {
                    return res.data;
                }
            })
            .catch((error) => {
                console.error(error);
            });
    }

    // export const downloadentity = (data: Array<string>) => {//文件下载
    //     return http<searchTypes.IDownLoad[]>(`/entity/download/fileinfo`, {
    //         method: 'POST',
    //         data: data
    //     })
    // }

    //
    export function getTeacherList(id: string, isPrivate: boolean) {
        return HTTP.get(`/learn/v1/curriculum/center/editor/team?contentId=${id}&isPrivate=${isPrivate}`)
            .then((res) => {
                if (res.status === 200) {
                    return res.data;
                }
            })
            .catch((error) => {
                console.error(error);
            });
    }

    export function geteditorteam(data: any) {
        return HTTP(`/learn/v1/curriculum/center/editor/team`, {
            method: 'get',
            params: data
        }).then((res) => {
            if (res.status === 200) {
                return res.data;
            }
        })
            .catch((error) => {
                console.error(error);
            });
    }

    export function getquotelist(data: string) {
        return HTTP.get(`/learn/v1/curriculum/center/course/template/quote?${data}`)
            .then((res) => {
                if (res.status === 200) {
                    return res.data;
                }
            })
            .catch((error) => {
                console.error(error);
            });
    }

    export function copytemplate(data: string) {
        return HTTP.post(`/learn/v1/curriculum/center/copy/course/template/${data}`)
            .then((res) => {
                if (res.status === 200) {
                    return res.data;
                }
            })
            .catch((error) => {
                console.error(error);
            });
    }

    export function sharetopersonal(data: string) {
        return HTTP.post(`/learn/v1/curriculum/center/share/to/personal/${data}`)
            .then((res) => {
                if (res.status === 200) {
                    return res.data;
                }
            })
            .catch((error) => {
                console.error(error);
            });
    }

    export function gettemplatequote(data: string) {
        return HTTP.get(`/learn/v1/curriculum/center/course/template/quote?${data}`)
            .then((res) => {
                if (res.status === 200) {
                    return res.data;
                }
            })
            .catch((error) => {
                console.error(error);
            });
    }

    export function gettemplatesituation(data: string) {
        return HTTP.get(`/learn/v1/curriculum/center/course/open/situation?${data}`)
            .then((res) => {
                if (res.status === 200) {
                    return res.data;
                }
            })
            .catch((error) => {
                console.error(error);
            });
    }


    /**
     * 查询所有教师
     * @param params
     */
    export const fetchAllTeacher = (params: {
        roleCode?: string;
        keyword?: string;
        page: number;
        size: number;
    }) =>
        HTTP(`/unifiedplatform/v1/role/search/user/bind`, {
            method: 'POST',
            data: JSON.stringify(params),
        })
            .then((res) => {
                if (res.status === 200) {
                    return res.data;
                }
            })
            .catch((error) => {
                console.error(error);
            });
    /**
     * 查询所有教师
     * @param params
     */
     export const GetAllTeacher = (params: {
        sourceTpye: number;
        school:string;
        keyword?: string;
        pageIndex: number;
        pageSize: number;
    }) =>
        HTTP(`/unifiedplatform/v1/base/data/database/source/data`, {
            method: 'GET',
            params:params
        })
            .then((res) => {
                if (res.status === 200) {
                    return res.data;
                }
            })
            .catch((error) => {
                console.error(error);
            });

    export const addTemplateAdmin = (contentId: string, ids: string[],isStudent: boolean) =>
        HTTP(`/learn/v1/curriculum/center/add/course/template/administrators`, {
            method: 'POST',
            params: {
                contentId,
                isStudent
            },
            data: JSON.stringify(ids),
        })
            .then((res) => {
                if (res.status === 200) {
                    return res.data;
                }
            })
            .catch((error) => {
                console.error(error);
            });

    export const addTemplateEditor = (contentId: string, ids: string[]) =>
        HTTP(`/learn/v1/curriculum/center/add/course/template/editor`, {
            method: 'POST',
            params: {
                contentId,
            },
            data: JSON.stringify(ids),
        })
            .then((res) => {
                if (res.status === 200) {
                    return res.data;
                }
            })
            .catch((error) => {
                console.error(error);
            });
    export const deleteTemplateEditor = (contentId: string, ids: string[]) =>
        HTTP(`/learn/v1/curriculum/center/delete/course/template/editor`, {
            method: 'POST',
            params: {
                contentId,
            },
            data: JSON.stringify(ids),
        })
            .then((res) => {
                if (res.status === 200) {
                    return res.data;
                }
            })
            .catch((error) => {
                console.error(error);
            });
    export const deleteTemplateAdmin = (contentId: string, ids: string[]) =>
        HTTP(`/learn/v1/curriculum/center/delete/course/template/administrators`, {
            method: 'POST',
            params: {
                contentId,
            },
            data: JSON.stringify(ids),
        })
            .then((res) => {
                if (res.status === 200) {
                    return res.data;
                }
            })
            .catch((error) => {
                console.error(error);
            });
    export const templateteacherUpdate = (contentId: string, userCode: string, oldRole: string, newRole: string) =>
        HTTP(`/learn/v1/curriculum/center/update/template/teacher/role`, {
            method: 'POST',
            params: {
                contentId,
                userCode,
                oldRole,
                newRole
            },
        })
            .then((res) => {
                if (res.status === 200) {
                    return res.data;
                }
            })
            .catch((error) => {
                console.error(error);
            });

    // 批量更新课程章节发布状态
    export function updatatemCourse(data: string[]) {
        return HTTP.post(
            `/learn/v1/curriculum/center/update/course/template/release/status/1`, data
        )
            .then((res) => {
                if (res.status === 200) {
                    return res.data;
                }
            })
            .catch((error) => {
                console.error(error);
            });
    }
    interface ITreeUpdata {
      courseId: string;
      list: any[];
      parentId: string;
      sourceId: string;
    }
    // 批量更新章节目录树
    export function updataLearnTree(data: ITreeUpdata) {
      return HTTP.post(`/learn/v1/curriculum/center/template/chapter/drag`, data)
        .then(res => {
          if (res.status === 200) {
            return res.data;
          }
        })
        .catch(error => {
          console.error(error);
        });
    }
    //获取我的审核列表
    export function getMyReview(params:  MicroCourse.searchParams & { modelKey: string,name: string }) {
      const {
        name,
        keyword,
        publishStatus,
        classificationId,
        approvalStatus,
        teacher,
        subjectId,
        startUpdateTime,
        endUpdateTime,
        page,
        size,
        courseType,
        modelKey
      } = params;
      return HTTP.post(`/learn/v1/course/bpm/release`, {
        name,
        keyword,
        publishStatus,
        classificationId,
        approvalStatus,
        subjectId,
        teacher,
        startUpdateTime,
        endUpdateTime,
        modelKey,
        isTop: null,
        courseType: courseType ? courseType : 0,
        // college: [],
        // professionIds: [],
        order: [
          {
            field: 'createDate_',
            isDesc: true,
          },
        ],
        page,
        size: size ? size : 12,
      })
        .then(res => {
          if (res.status === 200) {
            return res.data;
          }
        })
        .catch(error => {
          console.error(error);
        })
    }
}

export default courseTemplate;
